services:
  redis:
    container_name: 'gaio-redis'
    image: redis
    hostname: 'redis'
    command: redis-server --save "" --appendonly no --maxclients 20000
    ports:
      - 6379:6379
    restart: unless-stopped
    networks:
      - gaio-network
    cpus: '0.05'
    mem_limit: '0.1GB'
  h2o:
    container_name: 'gaio-h2o'
    image: h2oai/h2o-open-source-k8s
    ports:
      - 54321:54321
    restart: unless-stopped
    volumes:
      - type: bind
        source: ./content
        target: /home/<USER>/api/content
    networks:
      - gaio-network
    cpus: '4'
    mem_limit: '4GB'
  app:
    build:
      context: .
      args:
        - BUN_VERSION=${BUN_VERSION:-1.1.25}
        - RUST_VERSION=${RUST_VERSION:-1.81.0}
    container_name: 'gaio-api'
    env_file:
      - .env
    command: bun run dev
    environment:
      GAIO_REDIS_HOST: redis
      GAIO_REDIS_PORT: 6379
    ports:
      - 3000:3000
    volumes:
      - type: bind
        source: ./
        target: /home/<USER>/api
    # develop:
    #   watch:
    #     - action: sync
    #       path: .
    #       target: /home/<USER>/api
    #     # - action: sync
    #     #   path: ./drizzle
    #     #   target: /usr/src/app/server/drizzle
    #     - action: rebuild
    #       path: ./package.json
    depends_on:
      - redis
      - h2o
    networks:
      - gaio-network
    cpus: '4'
    mem_limit: '4GB'

networks:
  gaio-network:
    driver: bridge
    attachable: true
