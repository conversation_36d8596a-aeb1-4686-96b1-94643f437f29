import {
	deleteRepo,
	getListOfRepo,
	getListOfRepoBase,
	getRepoDataById,
	upsertRepo
} from '@/components/repo/use-cases/repo.manager';
import jwtGuard from '@/server/middleware/jwt.guard';
import { readBody } from '@/server/middleware/readBody';
import type { SourceType } from '@gaio/shared/types';
import { Hono } from 'hono';

const app = new Hono()
	.get('/data/:repoId', jwtGuard('admin').isAuth, async (c) => {
		const repoId = c.req.param('repoId');
		return c.json(await getRepoDataById(repoId));
	})

	.get('/list', jwtGuard('admin').isAuth, async (c) => {
		return c.json(await getListOfRepo());
	})

	.get('/list-base', jwtGuard('user').isAuth, async (c) => {
		return c.json(await getListOfRepoBase());
	})

	.get('/list-details', jwtGuard('admin').isAuth, async (c) => {
		return c.json(await getListOfRepo());
	})

	.post('/save', jwtGuard('admin').isAuth, async (c) => {
		const { repoData, user } = await readBody<{ repoData: SourceType }>(c);
		return c.json(await upsertRepo(repoData, user));
	})

	.post('/delete', jwtGuard('admin').isAuth, async (c) => {
		const { repoId } = await readBody<{ repoId: string }>(c);
		return c.json(await deleteRepo(repoId));
	});

export default app;
