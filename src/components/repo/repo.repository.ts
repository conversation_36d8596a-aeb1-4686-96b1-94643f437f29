import { dbGaio } from '@/db/db.gaio';
import type { CredentialsType, SourceType, UserType } from '@gaio/shared/types';
import type { TaskType } from '@gaio/shared/types';
import {
	getBucketNameFromAppId,
	getRepositoryUserNameFromAppId
} from '@gaio/shared/utils';

async function getRepoCredentials(taskData: TaskType, role = 'bucket') {
	// to do: use pass password instead of default
	return await dbGaio('getRepoCredentials')
		.query<{ credentials: CredentialsType; password: string }>(
			`SELECT repo.credentials as credentials,
				pass.password as password
			FROM repo 
			LEFT JOIN pass ON pass.repoId = repo.repoId
			WHERE repo.repoId = {repoId: String} AND
				pass.appId = {appId: String}
			LIMIT 1`,
			{
				params: { appId: taskData.appId, repoId: taskData.repoId },
				parse: ['credentials']
			}
		)
		.then((res) => {
			if (res[0] && res[0].credentials) {
				return {
					...res[0].credentials,
					client: 'clickhouse',
					database:
						role === 'bucket'
							? getBucketNameFromAppId(taskData.appId)
							: res[0].credentials['database'],
					password: res[0].password,
					user: getRepositoryUserNameFromAppId(taskData.appId)
				};
			}

			return null;
		});
}

async function getRepoDataById(
	repoId: string
): Promise<Partial<CredentialsType>> {
	return await dbGaio('getRepoDataById')
		.query<{ credentials: CredentialsType }>(
			`SELECT repo.credentials as credentials
				FROM repo
				WHERE repoId = {repoId: String}
				LIMIT 1`,
			{
				params: {
					repoId
				},
				parse: ['credentials']
			}
		)
		.then((res) => (res[0] && res[0].credentials) || {});
}

async function getListOfRepoBase() {
	return dbGaio('listRepo').query<SourceType>(
		`SELECT 
			repoName, 
			repoId, 
			client, 
		FROM repo`
	);
}

async function getListOfRepo() {
	return dbGaio('listRepo').query<SourceType>(
		`SELECT 
			repoName, 
			repoId, 
			client, 
			credentials 
		FROM repo`,
		{
			parse: ['credentials']
		}
	);
}

async function deleteRepository(repoId: string) {
	return dbGaio('deleteRepo').query(
		`ALTER TABLE repo 
			DELETE WHERE repoId = {repoId: String}`,
		{ params: { repoId } }
	);
}

async function saveRepo(repoData: SourceType, contextUser: UserType) {
	return await dbGaio('saveRepo').exec(
		`ALTER TABLE repo
			UPDATE repoName = {repoName: String},
				credentials = {credentials: String},
				client = {client: String},
				modifiedBy = {modifiedBy: String},
				updatedAt = NOW()
			WHERE repoId = {repoId: String}`,
		{
			params: {
				client: repoData.client,
				credentials: repoData.credentials,
				modifiedBy: contextUser.userId,
				repoId: repoData.repoId,
				repoName: repoData.repoName
			},
			stringify: ['credentials']
		}
	);
}

async function createRepo(repoData: SourceType, contextUser: UserType) {
	return await dbGaio('createRepo').insert('repo', [
		{
			client: repoData.client,
			createdBy: contextUser.userId,
			credentials: JSON.stringify(repoData.credentials),
			modifiedBy: contextUser.userId,
			repoId: repoData.repoId,
			repoName: repoData.repoName
		}
	]);
}

export default {
	getListOfRepoBase,
	getRepoCredentials,
	deleteRepository,
	createRepo,
	getListOfRepo,
	getRepoDataById,
	saveRepo
};
