import RepoRepository from '@/components/repo/repo.repository';
import type { SourceType, UserType } from '@gaio/shared/types';
import { getId } from '@gaio/shared/utils';

export async function getListOfRepo() {
	return RepoRepository.getListOfRepo().then((res) => {
		return res.map((o) => {
			delete o.credentials.password;
			return o;
		});
	});
}

export async function getListOfRepoBase() {
	return RepoRepository.getListOfRepoBase();
}

export async function getRepoDataById(repoId: string) {
	return RepoRepository.getRepoDataById(repoId).then((res) => {
		delete res.password;
		return res;
	});
}

export async function deleteRepo(repoId: string) {
	await RepoRepository.deleteRepository(repoId);
	return {
		status: 'done'
	};
}

export async function upsertRepo(repoData: SourceType, userContext: UserType) {
	if (repoData.repoId) {
		await RepoRepository.saveRepo(repoData, userContext);
	} else {
		const repoId = getId();
		await RepoRepository.createRepo(
			{
				createdBy: userContext.userId,
				credentials: repoData.credentials,
				modifiedBy: userContext.userId,
				repoId,
				repoName: repoData.repoName
			},
			userContext
		);
	}

	return {
		status: 'done'
	};
}
