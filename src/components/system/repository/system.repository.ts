import * as os from 'node:os';
import { dbGaio } from '@/db/db.gaio';
import { formatHeapStatsFromBytes } from '@/utils/helpers';
import type { DiskUsageType } from '../entities/system.entity';

export async function getSystemInfo(): Promise<DiskUsageType> {
	const diskUsageQuery = await dbGaio().query<Record<string, string | number>>(`
			SELECT
				path,
				name,
				free_space,
				total_space
			FROM 
				system.disks
		`);

	const diskUsage = diskUsageQuery[0];

	return {
		name: diskUsage.name.toString(),
		path: diskUsage.path.toString(),
		freeDisk: formatHeapStatsFromBytes(diskUsage.free_space as number),
		totalDisk: formatHeapStatsFromBytes(diskUsage.total_space as number),
		usedDisk: formatHeapStatsFromBytes(
			(diskUsage.total_space as number) - (diskUsage.free_space as number)
		),
		percentUsageDisk: (
			(((diskUsage.total_space as number) - (diskUsage.free_space as number)) /
				(diskUsage.total_space as number)) *
			100
		)
			.toFixed(1)
			.concat(' %')
	} as DiskUsageType;
}

function getCPUInfo() {
	const cpus = os.cpus();
	let idle = 0;
	let total = 0;

	cpus.forEach((core) => {
		const { user, nice, sys, idle: coreIdle, irq } = core.times;
		total += user + nice + sys + coreIdle + irq;
		idle += coreIdle;
	});

	return { idle, total };
}

export async function getCPUUsage(interval = 1000): Promise<string> {
	const startMeasurement = getCPUInfo();

	return new Promise((resolve) => {
		setTimeout(() => {
			const endMeasurement = getCPUInfo();
			const idleDiff = endMeasurement.idle - startMeasurement.idle;
			const totalDiff = endMeasurement.total - startMeasurement.total;

			const usagePercentage = (1 - idleDiff / totalDiff) * 100;

			resolve(usagePercentage.toFixed(2));
		}, interval);
	});
}

export function getMEMInfo() {
	return {
		total: formatHeapStatsFromBytes(os.totalmem()),
		free: formatHeapStatsFromBytes(os.freemem())
	};
}
