import { TemplateService } from '@/utils/template.service';
import type {
	CommonBuilderTaskType,
	ParamType,
	SchemaSortType
} from '@gaio/shared/types';
import knex, { type Knex } from 'knex';

export class SortService {
	private template = new TemplateService();

	public build(
		taskData: CommonBuilderTaskType,
		params: ParamType[],
		builder: Knex.QueryBuilder
	) {
		const schema = taskData.schema;

		const knexConn = knex({
			client: taskData.client,
			useNullAsDefault: true
		});

		if (schema.sort.length <= 0 && taskData.client === 'mssql') {
			schema.sort.push({
				alias: 'just_sort',
				columnName: 'just_sort',
				computedId: 'fihEVv',
				content: '(select null)',
				dataType: 'Nullable(String)',
				id: 'OFAzeX',
				order: 'asc',
				type: 'computed'
			} as SchemaSortType);
		}

		schema.sort.forEach((field) => {
			if (field.type === 'computed' || field.computedId) {
				this.getComputedContent(taskData, params, builder, field, knexConn);
			} else if (field.alias && field.alias !== '' && field.type !== 'value') {
				const orderName = knexConn.raw(field.alias) as string;

				builder.orderBy(orderName, field.order);
			} else if (
				field.type &&
				field.type !== 'none' &&
				field.type !== 'value'
			) {
				const orderName = knexConn.raw(`${field.type}_${field.columnName}`);

				builder.orderBy(orderName, field.order);
			} else {
				builder.orderBy(`${field.tableName}.${field.columnName}`, field.order);
			}
		});

		return builder;
	}

	private getComputedContent(
		taskData: CommonBuilderTaskType,
		params: ParamType[],
		builder: Knex.QueryBuilder,
		field: SchemaSortType,
		knexConn: Knex
	) {
		const computed = taskData.schema.computed.find(
			(o) => o.computedId === field.computedId
		);

		if (computed) {
			const orderName = knexConn.raw(
				`(${this.template.render(computed.content, params)})`
			);

			builder.orderBy(orderName, field.order);
		}

		return builder;
	}
}
