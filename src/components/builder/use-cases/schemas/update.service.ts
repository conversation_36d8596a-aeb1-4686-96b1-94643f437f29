import { TemplateService } from '@/utils/template.service';
import type {
	CommonBuilderTaskType,
	FieldType,
	ParamType
} from '@gaio/shared/types';
import knex, { type Knex } from 'knex';

export class UpdateService {
	private template = new TemplateService();

	private static getValueFromParameter(value: unknown) {
		return value !== 0 && value !== '0' ? value || '' : '0';
	}

	private static castValue(field: FieldType, value: unknown, k: Knex) {
		if (field.dataType.includes('Date')) {
			if (field.dataType.includes('Nullable')) {
				return k.raw(`parseDateTimeBestEffortOrNull('${value || ''}')`);
			}

			return k.raw(`parseDateTimeBestEffort('${value}')`);
		} else if (
			field.dataType.includes('Float') ||
			field.dataType.includes('Decimal')
		) {
			if (field.dataType.includes('Nullable')) {
				if (value) {
					return k.raw(
						`floor(toFloat64OrNull('${this.getValueFromParameter(value)}'), ${field.columnLength || 2})`
					);
				}

				return k.raw(`null`);
			}

			return k.raw(
				`floor(toFloat64OrNull(toString('${this.getValueFromParameter(value)}')), ${field.columnLength || 2})`
			);
		} else if (
			field.dataType.includes('Int') ||
			field.dataType.includes('Uni')
		) {
			if (field.dataType.includes('Nullable')) {
				if (value) {
					return k.raw(
						`round(toFloat64OrNull('${this.getValueFromParameter(value)}'),0)`
					);
				}

				return k.raw(`null`);
			}

			return k.raw(
				`floor(toFloat64OrNull('${this.getValueFromParameter(value)}'), 0)`
			);
		} else {
			if (value === 0) {
				return '0';
			} else if (value) {
				return `${value}`;
			}

			return k.raw(`null`);
		}
	}

	public build(
		taskData: CommonBuilderTaskType,
		params: ParamType[],
		builder: Knex.QueryBuilder
	) {
		const schema = taskData.schema;

		const k = knex({
			client: 'mysql',
			useNullAsDefault: true
		});

		schema.update.forEach((field) => {
			if (field.valueType === 'computed') {
				this.getComputedContent(params, builder, field, k);
			} else if (field.valueType === 'parameter') {
				const p = params.find((o) => o.paramName === field.value);

				if (p) {
					builder.update(
						field.columnName,
						UpdateService.castValue(field, p.paramValue, k)
					);
				}
			} else {
				builder.update(
					field.columnName,
					UpdateService.castValue(field, field.value, k)
				);
			}
		});

		return builder;
	}

	private getComputedContent(
		params: ParamType[],
		builder: Knex.QueryBuilder,
		field: FieldType,
		k: Knex
	) {
		return builder.update(
			field.columnName,
			k.raw(`${this.template.render(field.content, params)}`)
		);
	}
}
