import { TemplateService } from '@/utils/template.service';
import type {
	CommonBuilderTaskType,
	FieldType,
	ParamType
} from '@gaio/shared/types';
import knex, { type Knex } from 'knex';

export class GroupService {
	private template = new TemplateService();

	public build(
		taskData: CommonBuilderTaskType,
		params: ParamType[],
		builder: Knex.QueryBuilder
	) {
		const schema = taskData.schema;

		if (taskData.schema.selectAll) {
			builder.groupBy('*');
		}

		schema.group.forEach((field) => {
			const groupName = field.columnName;

			if (field.type === 'computed' || field.computedId) {
				builder = this.getComputedContent(taskData, params, builder, field);
			} else {
				builder.groupBy(`${field.tableName}.${groupName}`);
				return;
			}
		});

		return builder;
	}

	private getComputedContent(
		taskData: CommonBuilderTaskType,
		params: ParamType[],
		builder: Knex.QueryBuilder,
		field: FieldType
	) {
		const knexConn = knex({
			client: taskData.client,
			useNullAsDefault: true
		});

		const computed = taskData.schema.computed.find(
			(o) => o.computedId === field.computedId
		);

		if (computed) {
			builder.groupBy(
				knexConn.raw(`(${this.template.render(computed.content, params)})`),
				field.order
			);
		}

		return builder;
	}
}
