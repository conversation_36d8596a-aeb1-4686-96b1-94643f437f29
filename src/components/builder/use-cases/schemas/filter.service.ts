import { TemplateService } from '@/utils/template.service';
import type {
	CommonBuilderTaskType,
	FieldType,
	GenericType,
	GenericValueType,
	ParamType
} from '@gaio/shared/types';
import knex, { type Knex } from 'knex';
import { cloneDeep, isNumber } from 'lodash-es';
import {
	isDateLiteral,
	isNumeric,
	renderDateLiteral,
	transformValue
} from '../tools';

type FilterOrHavingType = 'filter' | 'having';

export class FilterService {
	private type: FilterOrHavingType = 'filter';

	constructor(type: FilterOrHavingType) {
		this.type = type;
	}

	template = new TemplateService();

	public build(
		taskData: CommonBuilderTaskType,
		parameters: ParamType[],
		builder: Knex.QueryBuilder
	) {
		const schema = taskData.schema;

		schema[this.type].forEach((fil) => {
			if (this.type === 'having') {
				this.whereRules(taskData, builder, fil.list, parameters);
			} else {
				if (fil.andOr && fil.andOr === 'or') {
					builder = builder.orWhere((whrBuild) => {
						return this.whereRules(taskData, whrBuild, fil.list, parameters);
					});
				} else {
					builder = builder.where((whrBuild) => {
						return this.whereRules(taskData, whrBuild, fil.list, parameters);
					});
				}
			}
		});
		return builder;
	}

	whereRules(
		taskData: CommonBuilderTaskType,
		builder: Knex.QueryBuilder,
		list: GenericType[],
		parameters: ParamType[]
	) {
		list.forEach((field) => {
			let value: string;
			let extraValue: string;
			let column: string;

			const forceTableName = (field: FieldType, columnName: string) => {
				if (
					field.forceTableName &&
					!columnName.startsWith(`${field.tableName}.`)
				) {
					return `${field.tableName}.${columnName}`;
				}

				return columnName;
			};

			// SET FIELD ALIAS, IF NEEDED
			if (field.type === 'computed' || field.computedId) {
				column = this.renderTemplate(
					taskData,
					parameters,
					field.content,
					field.columnName
				);
			} else {
				column =
					field.alias && field.alias !== ''
						? field.alias
						: `${field.tableName}.${field.columnName}`;

				column = forceTableName(field, column);
			}

			// SET VALUE
			if (isDateLiteral(field.value)) {
				value = renderDateLiteral(taskData, field.value);

				if (field.extraValue) {
					extraValue = renderDateLiteral(taskData, field.extraValue);
				}
			} else {
				switch (field.valueType) {
					case 'computed':
						value = this.renderTemplate(
							taskData,
							parameters,
							field.value,
							column
						);

						if (field.extraValue) {
							extraValue = this.renderTemplate(
								taskData,
								parameters,
								field.extraValue,
								column
							);
						}

						break;
					case 'parameter':
						value = this.getValueFromParameter(parameters, field, 'value');

						if (field.extraValue) {
							extraValue = this.getValueFromParameter(
								parameters,
								field,
								'extraValue'
							);
						}

						break;
					default:
						value = transformValue(field, field.value);

						if (field.extraValue) {
							extraValue = transformValue(field, field.extraValue);
						}

						break;
				}
			}

			this.loadBuilder(
				builder,
				field,
				column,
				value,
				extraValue,
				taskData.clickhouse
			);
		});

		return builder;
	}

	loadBuilder(
		builder: Knex.QueryBuilder,
		field: FieldType,
		column: string,
		value: GenericValueType,
		extraValue: GenericValueType,
		clickhouse: boolean
	) {
		switch (field.operator) {
			case 'all':
				builder.whereRaw(`(${column} is not null OR ${column} is null)`);
				break;

			case 'function':
				builder.whereRaw(
					`(${column} ${field.functionOperator}  ${field.value.replace(/\{{|}}/gi, '').replace(/&#39;/g, "'")})`
				);

				break;

			case 'isNull':
				if (field.andOr === 'and') {
					builder.whereNull(column);
				} else {
					builder.orWhereNull(column);
				}

				break;

			case 'isNotNull':
				if (field.andOr === 'and') {
					builder.whereNotNull(column);
				} else {
					builder.orWhereNotNull(column);
				}

				break;

			case 'in':
				if (field.andOr === 'and') {
					builder.whereIn(column, this.whereInType(value));
				} else {
					builder.orWhereIn(column, this.whereInType(value));
				}

				break;

			case 'notIn':
				if (field.andOr === 'and') {
					builder.whereNotIn(column, this.whereInType(value));
				} else {
					builder.orWhereNotIn(column, this.whereInType(value));
				}

				break;

			case 'between': {
				let firstValue = null;
				let secondValue = null;

				if (
					field.valueType === 'parameter' &&
					`${field.dataType}`.includes('Date')
				) {
					firstValue =
						value === 'all_'
							? knex(field.tableName).min(column).from(field.tableName)
							: value;
					secondValue =
						extraValue === 'all_'
							? knex(field.tableName).max(column).from(field.tableName)
							: extraValue;
				} else if (
					field.valueType !== 'computed' &&
					!field.isFunction &&
					(`${field.dataType}`.includes('Float') ||
						`${field.dataType}`.includes('Decimal'))
				) {
					firstValue = isNumber(Number(value)) ? Number(value) : value || '';
					secondValue = isNumber(Number(extraValue))
						? Number(extraValue)
						: extraValue || '';
				}

				if (field.andOr === 'and') {
					builder.whereBetween(column, [
						firstValue ?? value,
						secondValue ?? extraValue
					]);
				} else {
					builder.orWhereBetween(column, [
						firstValue ?? value,
						secondValue ?? extraValue
					]);
				}

				break;
			}

			case 'notBetween': {
				let firstValue = null;
				let secondValue = null;

				if (
					field.valueType === 'parameter' &&
					`${field.dataType}`.includes('Date')
				) {
					firstValue =
						value === 'all_'
							? knex(field.tableName).min(column).from(field.tableName)
							: value;
					secondValue =
						extraValue === 'all_'
							? knex(field.tableName).max(column).from(field.tableName)
							: extraValue;
				} else if (
					field.valueType !== 'computed' &&
					!field.isFunction &&
					(`${field.dataType}`.includes('Float') ||
						`${field.dataType}`.includes('Decimal'))
				) {
					firstValue = isNumber(Number(value)) ? Number(value) : value || '';
					secondValue = isNumber(Number(extraValue))
						? Number(extraValue)
						: extraValue || '';
				}

				if (field.andOr === 'and') {
					builder.whereNotBetween(column, [
						firstValue ?? value,
						secondValue ?? extraValue
					]);
				} else {
					builder.orWhereNotBetween(column, [
						firstValue ?? value,
						secondValue ?? extraValue
					]);
				}

				break;
			}

			case 'like':
				if (field.andOr === 'and') {
					builder.where(column, 'like', `%${value}%`);
				} else {
					builder.orWhere(column, 'like', `%${value}%`);
				}

				break;

			case 'notLike':
				if (field.andOr === 'and') {
					builder.where(column, 'not like', `%${value}%`);
				} else {
					builder.orWhere(column, 'not like', `%${value}%`);
				}

				break;

			case 'startsWith':
				if (field.andOr === 'and') {
					builder.where(column, 'like', `${value}%`);
				} else {
					builder.orWhere(column, 'like', `${value}%`);
				}

				break;

			case 'doesNotStartsWith':
				if (field.andOr === 'and') {
					builder.where(column, 'not like', `${value}%`);
				} else {
					builder.orWhere(column, 'not like', `${value}%`);
				}

				break;

			case 'endsWith':
				if (field.andOr === 'and') {
					builder.where(column, 'like', `%${value}`);
				} else {
					builder.orWhere(column, 'like', `%${value}`);
				}

				break;

			case 'doesNotEndsWith':
				if (field.andOr === 'and') {
					builder.where(column, 'not like', `%${value}`);
				} else {
					builder.orWhere(column, 'not like', `%${value}`);
				}

				break;

			case '!=':
				if (clickhouse) {
					if (field.andOr === 'and') {
						builder.whereNotIn(column, this.whereInType(value));
					} else {
						builder.orWhereNotIn(column, this.whereInType(value));
					}
				} else {
					if (field.andOr === 'and') {
						builder.where(column, field.operator, value);
					} else {
						builder.orWhere(column, field.operator, value);
					}
				}

				break;

			default:
				if (field.andOr === 'and') {
					builder.where(column, field.operator, value);
				} else {
					builder.orWhere(column, field.operator, value);
				}

				break;
		}
		return builder;
	}

	whereInType(value: unknown): unknown[] {
		if (Array.isArray(value)) {
			return value;
		}

		if (typeof value === 'string') {
			const list = value.split(',');

			list.forEach((item, index) => {
				list[index] = item ? item.trim() : '';
			});

			return list || [];
		}
		return value as unknown[];
	}

	getValueFromParameter(
		parameters: ParamType[],
		field: FieldType,
		type: string
	) {
		// I KNOW, I KNOW, FIELD IS BEING MUTATED :(
		const param =
			parameters.find((o) => o.paramName === field[type]) || ({} as ParamType);
		let isFunction = false;

		if (!param.paramValue) {
			return null;
		}

		const k = knex({
			client: 'mysql',
			useNullAsDefault: true
		});

		if (!(Array.isArray(param.paramValue) && param.paramValue[0] === 'all_')) {
			if (
				isNumeric(field) &&
				param.paramValue !== 'all_' &&
				!`${param.paramValue}`.trim().startsWith('{{')
			) {
				param.paramValue = transformValue(field, param.paramValue);
			}
		}
		if (
			(field.operator !== 'between' &&
				field.operator !== 'notBetween' &&
				param.all &&
				param.paramValue === '') ||
			(param.all && param.paramValue === 'all_') ||
			(param.all && `${param.paramValue}` !== '0' && !param.paramValue)
		) {
			field.operator = 'all';
		} else if (Array.isArray(param.paramValue)) {
			if (
				param.paramValue.some(
					(o) =>
						o === 'all_' ||
						(Array.isArray(o) && o.includes('all_')) ||
						(Array.isArray(o) && o.length <= 0)
				) ||
				param.paramValue.length <= 0 ||
				param.paramValue[0] === 'all_'
			) {
				field.operator = 'all';

				field.value = '';
			} else if (field.operator === 'notIn') {
				field.operator = 'notIn';
			} else {
				field.operator = 'in';
			}
		} else if (
			field.operator !== 'between' &&
			field.operator !== 'notBetween' &&
			param.paramValue === 'all_'
		) {
			field.operator = 'all';
		} else if (`${param.paramValue}`.trim().startsWith('{{')) {
			isFunction = true;
		}

		field.isFunction = isFunction;
		field[type] = isFunction
			? k.raw(param.paramValue?.replace(/\{{|}}/gi, ''))
			: param.paramValue;

		if (Number(field[type]) === 0 || field[type] === '0') {
			return field[type];
		}

		return field[type] || null;
	}

	renderTemplate(
		taskData: CommonBuilderTaskType,
		parameters: ParamType[],
		value: GenericValueType,
		column: string
	) {
		const k = knex({
			client: taskData.client,
			useNullAsDefault: true
		});

		if (value === 'all_') {
			value = `null or ${column} is not null`;
		}

		return k.raw(
			this.template.render(`${value}`, parameters).replace(/&#39;/g, "'")
		);
	}
}
