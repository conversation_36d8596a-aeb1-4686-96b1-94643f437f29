import type { CommonBuilderTaskType } from '@gaio/shared/types';
import knex from 'knex';

const dateLiteral = [
	'YESTERDAY',
	'TODAY',
	'TOMORROW',
	'LAST_WEEK',
	'THIS_WEEK',
	'NEXT_WEEK'
	// 'LAST_MONTH',
	// 'THIS_MONTH',
	// 'NEXT_MONTH',
	// 'LAST_90_DAYS',
	// 'NEXT_90_DAYS',
	// 'THIS_QUARTER',
	// 'LAST_QUARTER',
	// 'NEXT_QUARTER',
	// 'THIS_YEAR',
	// 'LAST_YEAR',
	// 'NEXT_YEAR',
	// 'THIS_FISCAL_QUARTER',
	// 'LAST_FISCAL_QUARTER',
	// 'NEXT_FISCAL_QUARTER',
	// 'THIS_FISCAL_YEAR',
	// 'LAST_FISCAL_YEAR',
	// 'NEXT_FISCAL_YEAR',
];

const dateNLiteral = [
	'NEXT_N_DAYS',
	'LAST_N_DAYS',
	'N_DAYS_AGO',
	'NEXT_N_WEEKS',
	'LAST_N_WEEKS',
	'N_WEEKS_AGO',
	'NEXT_N_MONTHS',
	'LAST_N_MONTHS',
	'N_MONTHS_AGO',
	'NEXT_N_QUARTERS',
	'LAST_N_QUARTERS',
	'N_QUARTERS_AGO',
	'NEXT_N_YEARS',
	'LAST_N_YEARS',
	'N_YEARS_AGO',
	'NEXT_N_FISCAL_QUARTERS',
	'LAST_N_FISCAL_QUARTERS',
	'N_FISCAL_QUARTERS_AGO',
	'NEXT_N_FISCAL_YEARS',
	'LAST_N_FISCAL_YEARS',
	'N_FISCAL_YEARS_AGO'
];

const separateNLiteral = (value: string) => {
	const separate = `${value}`.split(':');

	return {
		dateLiteral: separate[0],
		n: separate[1]
	};
};

const isDateNumberLiteral = (value: string) => {
	return dateNLiteral.includes(separateNLiteral(value).dateLiteral);
};

const isDateLiteral = (value: string) => {
	return !!value && (dateLiteral.includes(value) || isDateNumberLiteral(value));
};

const convertDateLiteral = (value: string) => {
	if (!value) {
		return null;
	}

	let dateLiteral = 'null';

	if (value === 'YESTERDAY') {
		dateLiteral = 'subtractDays(today(), 1)';
	} else if (value === 'TODAY') {
		dateLiteral = 'today()';
	} else if (value === 'TOMORROW') {
		dateLiteral = 'addDays(today(), 1)';
	} else if (value === 'LAST_WEEK') {
		dateLiteral = 'toStartOfWeek(subtractWeeks(today(),1))';
	} else if (value === 'THIS_WEEK') {
		dateLiteral = 'subtractDays(today(), DAYOFWEEK(today()))';
	} else if (value === 'NEXT_WEEK') {
		dateLiteral = 'toStartOfWeek(addWeeks(today(),1))';
	} else if (`${value}`.startsWith('LAST_N_DAYS')) {
		dateLiteral = 'subtractDays(today(), ${separateNLiteral(value).n})';
	} else if (`${value}`.startsWith('NEXT_N_DAYS')) {
		dateLiteral = 'addDays(today(), ${separateNLiteral(value).n})';
	}

	return dateLiteral;
};

const renderDateLiteral = (taskData: CommonBuilderTaskType, value: string) => {
	const k = knex({
		client: taskData.client,
		useNullAsDefault: true
	});

	const dateLiteral = convertDateLiteral(value);

	return k.raw(dateLiteral);
};

export { isDateLiteral, renderDateLiteral };
