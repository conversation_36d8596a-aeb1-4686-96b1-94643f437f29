import type { FieldType } from '@gaio/shared/types';
import { isNil } from 'lodash-es';
import { isDateLiteral, renderDateLiteral } from './date-literal';

const isNumeric = (field: FieldType) => {
	return [
		'Int',
		'Int8',
		'Int16',
		'Int32',
		'Int64',
		'Decimal',
		'UInt8',
		'UInt16',
		'UInt32',
		'UInt64',
		'Float',
		'Float32',
		'Float64',
		'Float128',
		'Decimal',
		'Decimal32',
		'Decimal64',
		'Decimal128'
	].includes(`${field.dataType}`.replace('Nullable(', '').replace(')', ''));
};

const transformValue = (field: FieldType, value: unknown) => {
	if (value === 'all_') return 'all_';

	if (value === 'null' || value === 'undefined') {
		value = undefined;
	}

	return (
		isNumeric(field)
			? Array.isArray(value)
				? value.map((o) => Number(o)).filter((o) => !isNil(o))
				: !value && value !== 0
					? null
					: isNil(value)
						? null
						: Number.isNaN(Number(value))
							? null
							: Number(value)
			: value || ''
	) as string;
};

export { transformValue, isNumeric, isDateLiteral, renderDateLiteral };
