import { QueryBuilder } from '@/components/builder/use-cases/builder';
import jwtGuard from '@/server/middleware/jwt.guard';
import type { ParamType } from '@gaio/shared/types';
import { Hono } from 'hono';

const app = new Hono().post('/sql', jwtGuard('dev').isAuth, async (c) => {
	try {
		const { taskData, params } = await c.req.json<{
			taskData: unknown;
			params: ParamType[];
		}>();

		return c.json({
			query: await new QueryBuilder().generate(taskData, params)
		});
	} catch (e) {
		return c.json({ error: true, message: e.message, query: '' });
	}
});

export default app;
