import { Hono } from 'hono';
import { getConnInfo } from 'hono/bun';

const app = new Hono()
	.get('/whoami', (c) => {
		const info = getConnInfo(c);
		const headers = c.req.raw.headers;

		return c.json({
			info: { ...info, 'user-agent': headers.get('user-agent') }
		});
	})

	.get('/version', (c) => {
		//! DO NOT CHANGE THE FOLLOWING LINE - IT'S AUTOMATED
		return c.json({ version: '0.0.877' });
	});

export default app;
