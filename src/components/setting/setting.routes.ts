import type { ShareDataType } from '@/components/setting/settings.app-share';
import {
	listOfSharedApps,
	setupSharedApps
} from '@/components/setting/settings.app-share';
import {
	deleteSetting,
	getFoundation,
	getSetting,
	updateSetting
} from '@/components/setting/settings.core';
import {
	changeTagRole,
	deleteGroup,
	grantTag,
	listByTag,
	removeTag,
	saveGroup,
	tagManagerList
} from '@/components/setting/setttings.tag-manager';
import jwtGuard from '@/server/middleware/jwt.guard';
import { readBody } from '@/server/middleware/readBody';
import type { TagTypePermission, UserType } from '@gaio/shared/types';
import { Hono } from 'hono';
import { aml, automlRest } from '../automl/automl.core';

const app = new Hono()
	.get('/tag/manager-list', jwtGuard('admin').isAuth, async (c) => {
		return c.json(await tagManagerList());
	})

	.get('/automl/status', jwtGuard('dev').isAuth, async (c) => {
		try {
			await automlRest.get(aml('3/About'));
			return c.json({ status: 'online' });
		} catch {
			return c.json({ status: 'offline' });
		}
	})

	.post('/tag/list-by-type', jwtGuard('admin').isAuth, async (c) => {
		const { ...tagData } = await readBody<TagTypePermission>(c);

		return c.json(await listByTag(tagData));
	})

	.post('/tag/save-group', jwtGuard('admin').isAuth, async (c) => {
		const { userData, user } = await readBody<{ userData: UserType }>(c);

		return c.json(await saveGroup(userData, user));
	})

	.post('/tag/delete-group', jwtGuard('admin').isAuth, async (c) => {
		const { userData } = await readBody<{ userData: UserType }>(c);

		return c.json(await deleteGroup(userData));
	})

	.post('/tag/change-role', jwtGuard('admin').isAuth, async (c) => {
		const { user, tagData } = await readBody<{ tagData: TagTypePermission }>(c);

		return c.json(await changeTagRole(tagData, user));
	})

	.post('/tag/grant', jwtGuard('admin').isAuth, async (c) => {
		const { userList, tagList, user } = await readBody<{
			userList: UserType[];
			tagList: TagTypePermission[];
		}>(c);

		return c.json(await grantTag(userList, tagList, user));
	})
	.post('/tag/remove', jwtGuard('admin').isAuth, async (c) => {
		const { userList, tagList } = await readBody<{
			userList: UserType[];
			tagList: TagTypePermission[];
		}>(c);

		return c.json(await removeTag(userList, tagList));
	})

	.get('/app/shared-list', jwtGuard('admin').isAuth, async (c) => {
		return c.json(await listOfSharedApps());
	})

	.post('/app/setup-share', jwtGuard('admin').isAuth, async (c) => {
		const { user, ...shareData } = await readBody<ShareDataType>(c);

		return c.json(await setupSharedApps(shareData, user));
	})

	.get('/get/:settingId', jwtGuard('admin').isAuth, async (c) => {
		const settingId = c.req.param('settingId');

		return c.json(await getSetting(settingId));
	})

	.delete('/delete/gaio-docker-license', async (c) => {
		return c.json(await deleteSetting('gaio-docker-license'));
	})

	.post('/update', jwtGuard('admin').isAuth, async (c) => {
		const { user, ...shareData } = await readBody<ShareDataType>(c);

		return c.json(setupSharedApps(shareData, user));
	})

	.post('/save', jwtGuard('admin').isAuth, async (c) => {
		return c.json(await updateSetting(c));
	})

	.get('/foundation', jwtGuard('user').isAuth, async (c) => {
		return c.json(await getFoundation());
	})

	.get('/time', jwtGuard('dev').isAuth, async (c) => {
		return c.json({ time: new Date().toISOString() });
	});

export default app;
