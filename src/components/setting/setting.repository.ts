import { dbGaio } from '@/db/db.gaio';
import type { SettingType, UserType } from '@gaio/shared/types';

async function getSetting(settingId: string): Promise<SettingType | null> {
	try {
		const result = await dbGaio('getSetting')
			.query(
				`SELECT * 
						FROM setting
					WHERE settingId = {settingId: String}`,
				{
					params: { settingId },
					parse: ['options']
				}
			);
		
		
		if (!result || result.length === 0) {
			return null;
		}
		
		return result[0];
	} catch (error) {
		console.error('Error in getSetting:', error);
		throw error;
	}
}

async function getFoundation(ids: string[]) {
	return await dbGaio('getFoundation').query(
		`SELECT * 
				FROM setting
			WHERE settingId IN ('${ids.join("','")}')`,
		{
			parse: ['options']
		}
	);
}

async function upsertSetting(settingData: SettingType, userContext: UserType) {
	const setting = await getSetting(settingData.settingId);

	if (!setting) {
		return await addSetting(settingData, userContext);
	}

	return await updateSetting(settingData, userContext);
}

async function addSetting(settingData: SettingType, userContext: UserType) {
	return await dbGaio('addSetting').insert('setting', [
		{
			createdBy: userContext.userId,
			modifiedBy: userContext.userId,
			options: JSON.stringify(settingData.options),
			settingId: settingData.settingId
		}
	]);
}

async function updateSetting(settingData: SettingType, userContext: UserType) {
	const setting = await getSetting(settingData.settingId);

	if (!setting || !setting.settingId) {
		await addSetting(settingData, userContext);
	}

	return await dbGaio('updateSetting').exec(
		`ALTER TABLE setting
			UPDATE options = {options: JSON},
				modifiedBy = {modifiedBy: String},
				updatedAt = NOW() 
			WHERE settingId = {settingId: String}`,
		{
			params: {
				modifiedBy: userContext.userId,
				options: settingData.options,
				settingId: settingData.settingId
			},
			stringify: ['options']
		}
	);
}

async function deleteSetting(settingId: string) {
	return await dbGaio('deleteSetting').exec(
		`ALTER TABLE setting 
			DELETE WHERE settingId = {settingId: String}`,
		{
			params: { settingId }
		}
	);
}

export default { getSetting, upsertSetting, getFoundation, deleteSetting };
