import AppRepository from '@/components/app/app.repository';
import SourceRepository from '@/components/source/source.repository';
import TagRepository from '@/components/tag/tag.repository';
import UserRepository from '@/components/user/user.repository';
import type { TagTypePermission, UserType } from '@gaio/shared/types';
import { uniqBy } from 'lodash-es';
import AiThreadRepository from '../ai-thread/ai-thread.repository';

export async function tagManagerList() {
	const [apps, tags, sources] = await Promise.all([
		AppRepository.getAppsToTagControl(),
		UserRepository.getUsersToTagControl(),
		SourceRepository.getSourcesToTagControl()
	]);

	return {
		apps,
		sources,
		tags
	};
}

export async function listByTag(tagData: TagTypePermission) {
	let tags = [];
	let apps: TagTypePermission[] = [];
	let sources: unknown[] = [];

	if (tagData.type === 'user') {
		tags = await UserRepository.getUsersToTagControl(tagData.userId);
		apps = await AppRepository.getAppsToTagControl(tagData.userId);
		sources = await SourceRepository.getSourcesToTagControl(tagData.userId);
	} else {
		tags = await UserRepository.getUsersByTagRefAndType(
			tagData.refId,
			tagData.type
		);

		switch (tagData.type) {
			case 'app':
				apps = await AppRepository.getAppsInTagByType(tagData.refId);
				break;
			case 'source':
				sources = await SourceRepository.getSourcesInTagByType(tagData.refId);
				break;
		}
	}

	return {
		apps,
		sources,
		tags
	};
}

export async function changeTagRole(
	tagData: TagTypePermission,
	user: UserType
) {
	await TagRepository.changeTagRoleByUser(tagData, user);
	return {
		success: true
	};
}

export async function saveGroup(userData: UserType, userContext: UserType) {
	await UserRepository.saveGroup(userData, userContext);
	return {
		status: 'done'
	};
}

export async function deleteGroup(userData: UserType) {
	await UserRepository.deleteGroup(userData.userId);
	return {
		status: 'done'
	};
}

export async function grantTag(
	userList: UserType[],
	tagList: TagTypePermission[],
	userContext: UserType
) {
	const listTagData = [];
	const listToRemove = [];

	for (const user of userList) {
		for (const tag of tagList) {
			listToRemove.push({
				refId: tag.refId,
				type: tag.type,
				userId: user.userId
			});
			listTagData.push({
				createdBy: userContext.userId,
				modifiedBy: userContext.userId,
				refId: tag.refId,
				role: tag.role || 'view',
				type: tag.type,
				userId: user.userId
			});
		}
	}

	await Promise.all(
		listToRemove.map((tag) =>
			TagRepository.removeTag(tag.userId, tag.refId, tag.type)
		)
	);
	await TagRepository.insertTagPermission(
		uniqBy(listTagData, (o) => o.userId + o.refId + o.type)
	);

	return { status: 'success' };
}

export async function removeTag(
	userList: UserType[],
	tagList: TagTypePermission[]
) {
	const listTagData = [];
	for (const user of userList) {
		for (const tag of tagList) {
			listTagData.push({
				refId: tag.refId,
				type: tag.type,
				userId: user.userId
			});
		}
	}
	await Promise.all(
		listTagData.map((tag) =>
			TagRepository.removeTag(tag.userId, tag.refId, tag.type)
		)
	);

	await Promise.all(
		userList.map((user) =>
			AiThreadRepository.deleteAiThreadByUserId(user.userId)
		)
	);

	return { status: 'success' };
}
