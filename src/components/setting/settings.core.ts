import SettingRepository from '@/components/setting/setting.repository';
import type { SettingType, UserType } from '@gaio/shared/types';
import type { Context } from 'hono';

export async function getSetting(settingId: string) {
	const setting = await SettingRepository.getSetting(settingId);
	return setting;
}

export async function updateSetting(c: Context) {
	const userContext: UserType = c.get('user');
	const { settingData }: { settingData: SettingType } = await c.req.json();
	const setting = await SettingRepository.upsertSetting(
		settingData,
		userContext
	);
	return c.json(setting);
}

export async function deleteSetting(settingId: string) {
	const result = await SettingRepository.deleteSetting(settingId);
	return result;
}

export async function getFoundation() {
	const listOfSettings = ['aiManagerSettings'];
	const foundation = await SettingRepository.getFoundation(listOfSettings);

	return Object.fromEntries(
		foundation.map((item) => [item.settingId, item.options])
	);
}
