import AppRepository from '@/components/app/app.repository';
import PassRepository from '@/components/pass/pass.repository';
import { repositoryInstance } from '@/db/connections/db.clickhouse';
import type { UserType } from '@gaio/shared/types';
import {
	getBucketNameFromAppId,
	getId,
	getRepositoryUserNameFromAppId
} from '@gaio/shared/utils';

export type ShareDataType = {
	appId: string;
	repoId: string;
	sees: string[];
	revokeList: string[];
};

export async function listOfSharedApps() {
	return AppRepository.appShared();
}
export async function setupSharedApps(
	shareData: ShareDataType,
	userContext: UserType
) {
	await upsetSees(shareData, userContext.userId);
	await addUserAndDefineGrants(shareData);
	await removeSeesFromThisApps(
		shareData.appId,
		shareData.repoId,
		shareData.revokeList
	);
	return {
		status: 'done'
	};
}

async function addUserAndDefineGrants(shareData: ShareDataType) {
	const repoDb = repositoryInstance(shareData.repoId);
	for (const seeId of shareData.sees) {
		const passResult = await PassRepository.getPassPassword(
			seeId,
			shareData.repoId
		);

		if (passResult && passResult.length > 0) {
			const password = passResult[0].password as string;
			const bucket = getBucketNameFromAppId(seeId);
			const userSee = getRepositoryUserNameFromAppId(seeId);
			const userApp = getRepositoryUserNameFromAppId(shareData.appId);

			await defineUserAtBucket(seeId, shareData.repoId, password);

			await repoDb
				.query(`CREATE DATABASE IF NOT EXISTS ${bucket}`)
				.catch(() => {});

			await repoDb
				.query(`GRANT ${grantOptions} ON ${bucket}.* TO ${userSee}`)
				.catch(() => {});

			await repoDb
				.query(`GRANT ${grantOptionsGlobalScope} ON *.* TO ${userSee}`)
				.catch(() => {});

			await repoDb
				.query(`GRANT ${grantOptions} ON ${bucket}.* TO ${userApp}`)
				.catch(() => {});

			await repoDb
				.query(`GRANT ${grantOptionsGlobalScope} ON *.* TO ${userApp}`)
				.catch(() => {});
		}
	}
}

async function upsetSees(shareData: ShareDataType, userId: string) {
	const { appId, repoId, sees } = shareData;

	const pass = await PassRepository.getPassPassword(appId, repoId);
	let password = getId();

	if (pass && pass.length > 0) {
		password = pass[0].password as string;
		await PassRepository.saveSees(appId, repoId, sees);
	} else {
		await PassRepository.createRepoPassOnRepository(
			repoId,
			appId,
			password,
			userId
		);
	}

	await defineUserAtBucket(shareData.appId, shareData.repoId, password);

	return { status: 'done' };
}

async function defineUserAtBucket(
	appId: string,
	repoId: string,
	password: string
) {
	const userApp = getRepositoryUserNameFromAppId(appId);
	const bucket = getBucketNameFromAppId(appId);
	const repoDb = repositoryInstance(repoId);

	await repoDb.query(`DROP USER IF EXISTS ${userApp}`).catch((err) => {});

	await repoDb
		.query(
			`CREATE USER IF NOT EXISTS ${userApp} 
                SETTINGS PROFILE 'default' IDENTIFIED
                WITH PLAINTEXT_PASSWORD BY '${password}'`
		)
		.catch((err) => {});

	await repoDb
		.query(`GRANT ${grantOptions} ON ${bucket}.* TO ${userApp}`)
		.catch(() => {});

	await repoDb
		.query(`GRANT ${grantOptionsGlobalScope} ON *.* TO ${userApp}`)
		.catch(() => {});
}

async function removeSeesFromThisApps(
	appId: string,
	repoId: string,
	revokeList: string[]
) {
	const userApp = getRepositoryUserNameFromAppId(appId);
	const repoDb = repositoryInstance(repoId);
	for (const seeId of revokeList) {
		// revokes all privileges to it
		if (seeId !== appId) {
			// create database of this app/user, if not exit
			await repoDb
				.query(
					`CREATE DATABASE IF NOT EXISTS bucket_${getBucketNameFromAppId(seeId)}`
				)
				.catch(() => {});

			await repoDb
				.query(
					`REVOKE ALL PRIVILEGES ON ${getBucketNameFromAppId(seeId)}.* FROM ${userApp}`
				)
				.catch(() => {});
		}
	}

	return { status: 'done' };
}

// const retrieveRepoPass = async (appId: string, repoId: string, userId: string) => {
//     // take care we filter repoId too, in case of power-user changes apps repo
//     let password = (await PassRepository.getPassPassword(appId, repoId).then((res) =>
//         res[0] ? res[0].password : ''
//     )) as string
//
//     if (!password) {
//         password = getId(6) as string
//         await PassRepository.createRepoPassOnRepository(repoId, appId, password, userId)
//     }
//
//     return password || getId(6)
// }

const grantOptionsGlobalScope = `
                    HDFS, 
                    ODBC, 
                    jdbc, 
                    remote, 
                    s3, 
                    file, 
                    create temporary table, 
                    create function, 
                    drop function, 
                    url, 
                    MONGO`;
const grantOptions = ` SHOW, 
                    SELECT, 
                    INSERT, 
                    ALTER, 
                    CREATE DATABASE, 
                    CREATE TABLE, 
                    CREATE VIEW, 
                    CREATE DICTIONARY, 
                    DROP, 
                    TRUNCATE, 
                    OPTIMIZE, 
                    SYSTEM MERGES, 
                    SYSTEM TTL MERGES, 
                    SYSTEM FETCHES, 
                    SYSTEM MOVES, 
                    SYSTEM SENDS, 
                    SYSTEM REPLICATION QUEUES, 
                    SYSTEM DROP REPLICA, 
                    SYSTEM SYNC REPLICA,
                    SYSTEM RESTART REPLICA, 
                    SYSTEM RESTORE REPLICA, 
                    SYSTEM FLUSH DISTRIBUTED, 
                    dictGet `;
