import CronRepository from '@/components/cron/cron.repository';
import { dbGaio } from '@/db/db.gaio';
import { clickDate } from '@/utils/helpers';
import { currentDatetime } from '@/utils/index';
import type {
	FlowEntity,
	FlowType,
	NodeType,
	ParamType,
	WorkflowType
} from '@gaio/shared/types';
import { executables, getId } from '@gaio/shared/utils';
import { HTTPException } from 'hono/http-exception';

async function getFlowListByAppId(appId: string) {
	return await dbGaio()
		.query<FlowType>(
			`
			SELECT
				DISTINCT flow.flowId AS flowId,
				user.name as userName,
				cron.cron as cron,
				cron.cronBase as cronBase,
				cron.cronStatus as cronStatus,
				flow.flowType as flowType,
				flow.flowName as flowName,
				flow.workflow as workflow,
				flow.appId as appId,
				flow.createdAt as createdAt,
				flow.createdBy as createdBy,
				flow.modifiedBy as modifiedBy,
				flow.options as options,
				flow.status as status,
				flow.locked as locked,
				flow.updatedAt as updatedAt,
				flowOrder,
				flowDescription,
				flowKey
			FROM
				flow
			LEFT JOIN cron
				ON cron.refType = 'flow'
					AND cron.refId = flow.flowId
			LEFT JOIN user ON user.userId = flow.createdBy
			WHERE
				flow.appId = {appId: String}
			ORDER BY
				flowOrder ASC, flowName ASC`,
			{
				params: {
					appId
				},
				parse: ['options', 'cronBase', 'workflow']
			}
		)

		.then((res) => {
			return res.map((f) => {
				// @ts-ignore
				f.user = {
					// @ts-ignore
					name: f.userName,
					userId: f.createdBy
				};
				// @ts-ignore
				delete f.userName;
				return f;
			});
		});
}

async function getSmartFlowsByUser(userId: string) {
	return await dbGaio('getSmartFlowsByUser').query(
		`
			SELECT distinct flowId, flow.appId as appId, flowName, flow.options, app.appName, app.options as appOptions
				FROM flow 
				INNER JOIN tag ON tag.refId = flow.appId AND tag.userId = {userId: String}
				LEFT JOIN app ON app.appId = flow.appId
				WHERE flow.createdBy = {userId: String} AND flowType = 'smart'
	`,
		{
			params: {
				userId
			},
			parse: ['options', 'appOptions']
		}
	);
}

async function getFlowTasksAndMetadata(appId: string, flowId: string) {
	return await dbGaio('getFlowTasksAndMetadata')
		.query<{ workflow: WorkflowType; flowTimeout: number }>(
			`
				SELECT
					workflow, 
					simpleJSONExtractInt(options, 'flowTimeout') AS flowTimeout
				FROM
					flow 
				WHERE
					flowId = {flowId: String} 
					AND appId = {appId: String}
			`,
			{
				params: {
					appId,
					flowId
				},
				parse: ['workflow']
			}
		)
		.then((res) => {
			let tasks: Array<NodeType> = [];
			let flowTimeout = 0;

			if (res?.[0]?.workflow) {
				tasks = res[0].workflow.nodes.filter((o: NodeType) =>
					executables.includes(o.type)
				);
				flowTimeout = res[0].flowTimeout;
			}

			return { flowTimeout, tasks };
		});
}

async function getSomeDetailFromFlowByAppId(appId: string) {
	return await dbGaio().query<
		Pick<
			FlowEntity,
			'flowName' | 'flowType' | 'flowId' | 'flowOrder' | 'options'
		>
	>(
		`
			SELECT 
				flowName, 
				flowType,
				flowId, 
				flowOrder,
				options
			FROM
				flow 
			WHERE
				appId = {appId: String}
			ORDER BY flowOrder ASC
		`,
		{
			params: {
				appId
			},
			parse: ['options']
		}
	);
}

async function mergeFlow(flowData: FlowType, userId: string) {
	const { flowId, appId } = flowData;

	if (flowData.cronBase) {
		flowData.cron = flowData.cronBase.current || '';
		flowData.cronStatus = flowData.cronBase.status || 'inactive';
	} else {
		flowData.cron = '';
		flowData.cronStatus = 'inactive';
		flowData.cronBase = {};
	}

	await dbGaio().updateOrInsert({
		primaryKeys: ['refId', 'appId', 'refType'],
		table: 'cron',
		values: {
			appId: flowData.appId,
			createdBy: userId,
			cron: flowData.cron,
			cronBase: JSON.stringify(flowData.cronBase),
			cronStatus: flowData.cronStatus,
			refId: flowId,
			refType: 'flow',
			updatedAt: currentDatetime().replace('T', ' '),
			updatedBy: userId
		}
	});

	return await dbGaio().exec(
		`
			ALTER TABLE
				flow
			UPDATE
				flowName = {flowName: String},
				flowDescription = {flowDescription: String},
				options = {options : String},
				flowType = {flowType: String},
				flowKey = {flowKey: String},
				locked = {locked: Boolean},
				workflow = {workflow: String},
				modifiedBy = {modifiedBy: String}
			WHERE
				flowId = {flowId: String}
				AND appId = {appId: String}
		`,
		{
			params: {
				appId,
				flowDescription: flowData.flowDescription,
				flowId,
				flowKey: flowData.flowKey,
				flowName: flowData.flowName,
				flowType: flowData.flowType,
				locked: !!flowData.locked,
				modifiedBy: userId,
				options: flowData.options,
				updatedAt: clickDate(new Date()),
				workflow: flowData.workflow
			},
			stringify: ['options', 'workflow']
		}
	);
}

async function justSaveTheWorkflow(flowData: FlowType, userId: string) {
	return await dbGaio().exec(
		`
			ALTER TABLE
				flow
			UPDATE
				workflow = {workflow: String},
				modifiedBy = {userId: String}
			WHERE
				flowId = {flowId: String}
				AND appId = {appId: String}
		`,
		{
			params: {
				...flowData,
				userId
			},
			stringify: ['workflow']
		}
	);
}

async function createFlow(flowData: Partial<FlowType>, userId: string) {
	flowData.flowId = flowData.flowId || getId();

	const options = flowData.options
		? {
				...flowData.options,
				dashboardType: flowData.options.dashboardType || 'page'
			}
		: {
				dashboardType: 'page'
			};

	await dbGaio().insert('flow', [
		{
			appId: flowData.appId,
			createdBy: userId,
			flowDescription: flowData.flowDescription,
			flowId: flowData.flowId,
			flowKey: `${flowData.appId}:${getId(32)}`,
			flowName: flowData.flowName,
			flowOrder: flowData.flowOrder || 0,
			locked: !!flowData.locked,
			status: flowData.status || 'active',
			flowType: flowData.flowType || 'flow',
			modifiedBy: userId,
			options: JSON.stringify(options),
			workflow: JSON.stringify(
				flowData.workflow ?? {
					edges: [],
					nodes: []
				}
			)
		}
	]);

	await CronRepository.createCronReference({
		cron: {
			appId: flowData.appId,
			cron: flowData.cron,
			cronBase: JSON.stringify(flowData.cronBase || {}),
			cronStatus: flowData.cronStatus || 'inactive',
			refId: flowData.flowId,
			refType: 'flow'
		},
		userId
	});

	return flowData;
}

async function deleteFlowByAppId(appId: string) {
	await CronRepository.deleteCronReference({
		appId,
		refType: 'flow'
	});

	return await dbGaio().exec('DELETE FROM flow WHERE appId = {appId: String}', {
		params: {
			appId
		}
	});
}

async function renewFlowKey(flowId: string, appId: string, userId: string) {
	const flowKey = `${appId}:${getId(32)}`;
	await dbGaio().exec(
		`
			ALTER TABLE
				flow
			UPDATE
				flowKey = {flowKey: String},
				modifiedBy = {modifiedBy: String},
				updatedAt = {updatedAt: String}
			WHERE
				flowId = {flowId: String}
				AND appId = {appId: String}
		`,
		{
			params: {
				appId,
				flowId,
				flowKey,
				modifiedBy: userId,
				updatedAt: clickDate(new Date())
			}
		}
	);
	return {
		appId,
		flowId,
		flowKey
	};
}

async function removeFlow(flowId: string, appId: string) {
	await CronRepository.deleteCronReference({
		appId,
		refId: flowId,
		refType: 'flow'
	});

	return await dbGaio().exec(
		'DELETE FROM flow WHERE flowId = {flowId: String} AND appId = {appId: String}',
		{
			params: {
				appId,
				flowId
			}
		}
	);
}

async function updateFlowOrder(
	flowOrder: number,
	flowId: string,
	appId: string,
	userId: string
) {
	return await dbGaio().exec(
		`
			ALTER TABLE
				flow
			UPDATE
				flowOrder = {flowOrder: Int64},
				modifiedBy = {modifiedBy: String}
			WHERE
				flowId = {flowId: String}
				AND appId = {appId: String}
		`,
		{
			params: {
				appId,
				flowId,
				flowOrder,
				modifiedBy: userId
			}
		}
	);
}

async function getFlow(flowId: string, appId: string) {
	return await dbGaio()
		.query<FlowEntity>(
			`
				SELECT
					flow.*,
					cron.cron as cron,
					cron.cronBase as cronBase,
					cron.cronStatus as cronStatus
				FROM
					flow
				LEFT JOIN
					cron
						ON cron.refType = 'flow'
						AND cron.refId = flow.flowId
				WHERE
					flowId = {flowId: String}
					AND appId = {appId: String}
			`,
			{
				params: {
					appId,
					flowId
				},
				parse: ['workflow', 'cronBase']
			}
		)
		.then((res) => {
			if (res?.[0]) {
				return res[0] as unknown as FlowType;
			}

			return;
		});
}

async function getFlowToRestfullApi(restFlowId: string, restAppId: string) {
	return await dbGaio()
		.query(
			`
				SELECT
					flow.appId as appId,
					flow.flowId as flowId,
					app.params as params
				FROM flow
					LEFT JOIN app ON app.appId = flow.appId
				WHERE flowId = {flowId: String} AND app.appId = {appId: String}
				LIMIT 1
			`,
			{
				params: {
					flowId: restFlowId,
					appId: restAppId
				},
				parse: ['params']
			}
		)
		.then((results) => {
			const { appId, flowId, params } = results[0] as {
				appId: string;
				flowId: string;
				params: ParamType[];
			} | null;

			if (!results || !appId) {
				throw new HTTPException(401, {
					cause: 'invalidFlowAccess',
					message: 'Not authorized to access this flow'
				});
			}

			return {
				appId,
				flowId,
				params
			};
		});
}

export default {
	getFlowToRestfullApi,
	createFlow,
	deleteFlowByAppId,
	getFlow,
	getFlowListByAppId,
	getFlowTasksAndMetadata,
	getSomeDetailFromFlowByAppId,
	justSaveTheWorkflow,
	mergeFlow,
	removeFlow,
	renewFlowKey,
	updateFlowOrder,
	getSmartFlowsByUser
};
