import FlowRepository from '@/components/flow/flow.repository';
import { Queue } from 'bullmq';
import { HTTPException } from 'hono/http-exception';
import type { CronJobData } from '../../cron/cron.schedules.tools';
import {
	BULLMQ_CRON_QUEUE,
	addCronJobToRedis,
	gaioBullMQOptions,
	removeCronJobFromRedis
} from '../../cron/cron.schedules.tools';

export async function flowSchedulesSave({
	schedules
}: { schedules: Array<{ appId: string; flowId: string }> }) {
	if (!schedules || schedules.length === 0) {
		return {
			details: 'Schedules is empty',
			message: 'schedulesEmpty',
			success: false,
			timestamp: new Date().toISOString()
		};
	}

	const flowQueue = new Queue<CronJobData>(
		BULLMQ_CRON_QUEUE,
		gaioBullMQOptions
	);

	for (const schedule of schedules) {
		try {
			const flow = await FlowRepository.getFlow(
				schedule.flowId,
				schedule.appId
			);

			if (
				!flow ||
				!flow.cron ||
				flow.status !== 'active' ||
				flow.cronStatus !== 'active'
			) {
				await removeCronJobFromRedis({
					appId: schedule.appId,
					queue: flowQueue,
					refId: schedule.flowId,
					refType: 'flow'
				});
			}

			if (
				flow &&
				flow.status === 'active' &&
				flow.cronStatus === 'active' &&
				flow.cron
			) {
				await addCronJobToRedis({
					appId: schedule.appId,
					cron: flow.cron,
					cronStatus: flow.cronStatus,
					queue: flowQueue,
					refId: schedule.flowId,
					refType: 'flow'
				});
			}
		} catch {
			console.error('[FLOW] Error on flowSchedulesSave', { asError: true });

			throw new HTTPException(500, {
				cause: 'flowSchedulesSaveError',
				message: 'Error on flowSchedulesSave'
			});
		}
	}

	await flowQueue.close();

	return { success: true, timestamp: new Date().toISOString() };
}
