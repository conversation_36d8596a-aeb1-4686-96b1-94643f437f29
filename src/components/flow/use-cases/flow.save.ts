import FlowRepository from '@/components/flow/flow.repository';
import { gaioRedis } from '@/db/gaioRedis';
import type { FlowType, UserType } from '@gaio/shared/types';

export async function flowSave(flowData: FlowType, { userId }: UserType) {
	if (flowData.flowId && flowData.appId) {
		gaioRedis.pub.publish(
			`type:app-${flowData.appId}-${userId}`,
			JSON.stringify({
				flowData,
				type: 'flow'
			})
		);

		if (flowData.flowName) {
			await FlowRepository.mergeFlow(flowData, userId);
		}

		await FlowRepository.justSaveTheWorkflow(flowData, userId);

		return flowData;
	}

	return FlowRepository.createFlow(flowData, userId);
}
