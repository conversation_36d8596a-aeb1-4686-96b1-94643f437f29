import FlowRepository from '@/components/flow/flow.repository';
import type { UserType } from '@gaio/shared/types';
import { getId } from '@gaio/shared/utils';
import { cloneDeep } from 'lodash-es';

export async function flowClone(
	flowId: string,
	appId: string,
	{ userId }: UserType
) {
	const fromFlowData = await FlowRepository.getFlow(flowId, appId);
	const toFlowData = cloneDeep(fromFlowData);
	const newFlowId = getId();

	toFlowData.workflow.nodes = toFlowData.workflow.nodes.map((node) => {
		if (node.type !== 'group') {
			const id = node.id;
			node.id = getId(6);

			toFlowData.workflow.edges = toFlowData.workflow.edges.map((edge) => {
				if (edge.source === id) {
					edge.source = node.id;
				}
				if (edge.target === id) {
					edge.target = node.id;
				}
				return edge;
			});
		}
		return node;
	});

	toFlowData.workflow = JSON.parse(
		JSON.stringify(toFlowData.workflow)
			.split(`"flowId":${fromFlowData.flowId}`)
			.join(`"flowId":"${newFlowId}"`)
			.split(`"flowId":"${fromFlowData.flowId}"`)
			.join(`"flowId":"${newFlowId}"`)
	);

	toFlowData.flowKey = `${appId}:${getId(32)}`;
	toFlowData.flowId = newFlowId;

	toFlowData.flowName = `Dup. ${fromFlowData.flowName}`;

	return FlowRepository.createFlow(toFlowData, userId);
}
