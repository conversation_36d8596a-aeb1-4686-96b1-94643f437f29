import { flowClone } from '@/components/flow/use-cases/flow.clone';
import { flowDelete } from '@/components/flow/use-cases/flow.delete';
import { flowList } from '@/components/flow/use-cases/flow.list';
import { flowRenewFlowKey } from '@/components/flow/use-cases/flow.renew-flow-key';
import { flowSave } from '@/components/flow/use-cases/flow.save';
import { flowSchedulesSave } from '@/components/flow/use-cases/flow.schedules.save';
import { flowUpdateOrder } from '@/components/flow/use-cases/flow.update-order';
import jwtGuard from '@/server/middleware/jwt.guard';
import { readBody } from '@/server/middleware/readBody';
import type {
	FlowCloneRequestDTO,
	FlowListRequestDTO,
	FlowRemoveRequestDTO,
	FlowRenewFlowKeyRequestDTO,
	FlowSaveRequestDTO,
	FlowSaveSchedulesRequestDTO,
	FlowUpdateOrderRequestDTO
} from '@gaio/shared/types';
import { Hono } from 'hono';

const app = new Hono()
	.post('/save', jwtGuard('dev').isAuth, async (c) => {
		const { flowData, user } = await readBody<FlowSaveRequestDTO>(c);
		return c.json(await flowSave(flowData, user));
	})

	.post('/save-schedules', jwtGuard('dev').isAuth, async (c) => {
		const { schedulesList } = await readBody<FlowSaveSchedulesRequestDTO>(c);

		return c.json(await flowSchedulesSave({ schedules: schedulesList }));
	})

	.post('/list', jwtGuard('dev').isAuth, async (c) => {
		const { appId } = await readBody<FlowListRequestDTO>(c);
		return c.json(await flowList(appId));
	})

	.post('/remove', jwtGuard('dev').isAuth, async (c) => {
		const { flowId, appId } = await readBody<FlowRemoveRequestDTO>(c);
		return c.json(await flowDelete(flowId, appId));
	})

	.post('/clone', jwtGuard('dev').isAuth, async (c) => {
		const { flowId, appId, user } = await readBody<FlowCloneRequestDTO>(c);
		return c.json(await flowClone(flowId, appId, user));
	})

	.post('/renew-flow-key', jwtGuard('dev').isAuth, async (c) => {
		const { flowId, appId, user } =
			await readBody<FlowRenewFlowKeyRequestDTO>(c);
		return c.json(await flowRenewFlowKey(flowId, appId, user));
	})

	.post('/update-order', jwtGuard('dev').isAuth, async (c) => {
		const { flowList, appId, user } =
			await readBody<FlowUpdateOrderRequestDTO>(c);
		return c.json(await flowUpdateOrder(flowList, appId, user.userId));
	});

export default app;
