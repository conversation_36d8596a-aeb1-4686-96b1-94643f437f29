import CodeSnippetRepository from '@/components/code-snippet/code-snippet.repository';
import { codeSnippetSave } from '@/components/code-snippet/use-cases/code-snippet.save';
import jwtGuard from '@/server/middleware/jwt.guard';
import { readBody } from '@/server/middleware/readBody';
import { type Context, Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { HTTPException } from 'hono/http-exception';
import { z } from 'zod';
import { codeSnippetDocs } from './code-snippet.docs';

const _saveCodeSnippetSchema = z.object({
	codeSnippetData: z.object({
		appId: z.string(),
		codeSnippet: z.string(),
		codeSnippetId: z.string(),
		flowId: z.string(),
		options: z.unknown(),
		type: z.string()
	})
});

type CodeSnippetsSaveDTO = z.infer<typeof _saveCodeSnippetSchema>;

const app = new Hono()
	.post('/list', jwtGuard('dev').isAuth, async (c) => {
		const { user, appId } = await readBody<{ appId: string }>(c);
		return c.json(
			await CodeSnippetRepository.getAllDraftUserId(appId, user.userId)
		);
	})
	.get('/:codeSnippetId', jwtGuard('user').isAuth, async (c) => {
		try {
			const { codeSnippetId } = c.req.param();

			const codeSnippet =
				await CodeSnippetRepository.getCodeSnippetById(codeSnippetId);

			if (!codeSnippet) {
				throw new HTTPException(200, {
					cause: 'codeSnippetNotFound',
					message: 'Code Snippet not found'
				});
			}

			return c.json(
				{
					output: codeSnippet,
					message: 'codeSnippetFound',
					timestamp: new Date().toISOString()
				},
				{ status: 200 }
			);
		} catch (err) {
			console.error('err', err);
			if (err instanceof HTTPException) {
				return c.json(
					{
						details: err.message,
						output: null,
						message: err.cause,
						timestamp: new Date().toISOString()
					},
					err.status
				);
			}

			return c.json(
				{
					message: 'internalErrorFetchingCodeSnippet',
					timestamp: new Date().toISOString()
				},
				{ status: 500 }
			);
		}
	})

	.post('/save-many', jwtGuard('user').isAuth, async (c: Context) => {
		const { user, codeSnippetDataList } = await readBody<{
			codeSnippetDataList: CodeSnippetsSaveDTO['codeSnippetData'][];
		}>(c);

		const result = await Promise.all(
			codeSnippetDataList.map(async (codeSnippetData) => {
				const { codeSnippetId, appId, flowId, codeSnippet, type, options } =
					codeSnippetData;

				return codeSnippetSave({
					data: {
						appId,
						codeSnippet,
						codeSnippetId,
						flowId,
						options,
						type
					},
					userId: user.userId
				});
			})
		);

		return c.json(result);
	})

	.post(
		'/save',
		describeRoute(codeSnippetDocs.saveCodeSnippet),
		jwtGuard('user').isAuth,
		async (c) => {
			try {
				const { user, codeSnippetData } =
					await readBody<CodeSnippetsSaveDTO>(c);

				const { codeSnippetId, appId, flowId, codeSnippet, type, options } =
					codeSnippetData;

				const res = await codeSnippetSave({
					data: {
						appId,
						codeSnippet,
						codeSnippetId,
						flowId,
						options,
						type
					},
					userId: user.userId
				});

				return c.json(
					{
						output: res,
						message: 'codeSnippetSaved',
						timestamp: new Date().toISOString()
					},
					{ status: 200 }
				);
			} catch (err) {
				if (err instanceof HTTPException) {
					return c.json(
						{
							details: err.message,
							output: null,
							message: err.cause,
							timestamp: new Date().toISOString()
						},
						err.status
					);
				}

				return c.json(
					{
						message: 'internalErrorSavingCodeSnippet',
						timestamp: new Date().toISOString()
					},
					{ status: 500 }
				);
			}
		}
	)

	.delete(
		'/:codeSnippetId',
		describeRoute(codeSnippetDocs.deleteCodeSnippet),
		jwtGuard('dev').isAuth,
		async (c) => {
			try {
				const { codeSnippetId } = c.req.param();

				const res =
					await CodeSnippetRepository.deleteCodeSnippetById(codeSnippetId);

				return c.json(
					{
						output: res,
						message: 'codeSnippetDeleted',
						timestamp: new Date().toISOString()
					},
					{ status: 200 }
				);
			} catch (err) {
				if (err instanceof HTTPException) {
					return c.json(
						{
							details: err.message,
							output: null,
							message: err.cause,
							timestamp: new Date().toISOString()
						},
						err.status
					);
				}

				return c.json(
					{
						message: 'internalErrorDeletingCodeSnippet',
						timestamp: new Date().toISOString()
					},
					{ status: 500 }
				);
			}
		}
	);

export default app;
