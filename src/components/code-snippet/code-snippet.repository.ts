import { dbGaio } from '@/db/db.gaio';
import type { CodeSnippetEntity } from '@/db/entities';
import { currentDatetime } from '@/utils/helpers';
import type { GenericType } from '@gaio/shared/types';

type SaveCodeSnippetInput = {
	codeSnippetId: string;
	appId: string;
	flowId: string;
	type: string;
	userId: string;
	codeSnippet: string;
	options?: GenericType;
};

async function createCodeSnippet({
	appId,
	codeSnippet,
	codeSnippetId,
	type,
	flowId,
	userId,
	options
}: SaveCodeSnippetInput) {
	return await dbGaio().insert('code_snippet', [
		{
			appId,
			codeSnippet,
			codeSnippetId: codeSnippetId,
			createdBy: userId,
			flowId,
			type,
			updatedBy: userId,
			options: JSON.stringify(options || {})
		}
	]);
}

async function getAllCodeSnippetsByAppId(appId: string) {
	return await dbGaio().query<CodeSnippetEntity>(
		`
			SELECT *
			FROM code_snippet
			WHERE appId = {appId: String}
		`,
		{
			params: {
				appId
			}
		}
	);
}

async function getAllDraftUserId(appId: string, userId: string) {
	return await dbGaio().query<CodeSnippetEntity>(
		`
			SELECT *
			FROM code_snippet
			WHERE appId = {appId: String}
				AND createdBy = {userId: String}
				AND type = 'draft'
		`,
		{
			params: {
				appId,
				userId
			},
			parse: ['options']
		}
	);
}

async function getCodeSnippetById(codeSnippetId: string) {
	return await dbGaio()
		.query<CodeSnippetEntity>(
			`
				SELECT *
				FROM code_snippet
				WHERE codeSnippetId = {codeSnippetId: String}
			`,
			{
				params: {
					codeSnippetId
				},
				parse: ['options']
			}
		)
		.then((results) => {
			const item = results[0];

			if (item) return item;

			return null;
		});
}

async function getCodeSnippetByIdDecoded(codeSnippetId: string) {
	return await dbGaio()
		.query<CodeSnippetEntity>(
			`
				SELECT *
				FROM code_snippet
				WHERE codeSnippetId = {codeSnippetId: String}
			`,
			{
				params: {
					codeSnippetId
				},
				parse: ['options']
			}
		)
		.then((results) => results[0]);
}

async function updateCodeSnippet({
	appId,
	codeSnippet,
	codeSnippetId,
	type,
	flowId,
	userId
}: SaveCodeSnippetInput) {
	return await dbGaio().exec(
		`
			ALTER TABLE code_snippet
			UPDATE
				codeSnippet = {codeSnippet: String},
				updatedAt = {updatedAt: DateTime},
				updatedBy = {updatedBy: String}
			WHERE codeSnippetId = {codeSnippetId: String}
				AND appId = {appId: String}
				AND flowId = {flowId: String}
				AND type = {type : String}
		`,
		{
			params: {
				appId,
				codeSnippet,
				codeSnippetId,
				flowId,
				type,
				updatedAt: currentDatetime().replace('T', ' ').replace('Z', ''),
				updatedBy: userId
			}
		}
	);
}

async function upsertCodeSnippet({
	appId,
	codeSnippet,
	codeSnippetId,
	type,
	flowId,
	userId,
	options
}: SaveCodeSnippetInput) {
	return await dbGaio().updateOrInsert({
		primaryKeys: ['codeSnippetId', 'appId'],
		table: 'code_snippet',
		values: {
			type,
			appId,
			flowId,
			codeSnippet,
			codeSnippetId,
			createdBy: userId,
			updatedBy: userId,
			options: JSON.stringify(options),
			updatedAt: currentDatetime().replace('T', ' ').replace('Z', '')
		}
	});
}

async function deleteCodeSnippetsByAppId(appId: string) {
	return await dbGaio().exec(
		`
			DELETE
			FROM code_snippet
			WHERE appId = {appId: String}
		`,
		{
			params: {
				appId
			}
		}
	);
}

async function deleteCodeSnippetById(codeSnippetId: string) {
	return await dbGaio().exec(
		`
			DELETE
			FROM code_snippet
			WHERE codeSnippetId = {codeSnippetId: String}
		`,
		{
			params: {
				codeSnippetId
			}
		}
	);
}

async function deleteCodeSnippetsOfUserDrafts(userId: string) {
	return await dbGaio().exec(
		`
			DELETE
			FROM code_snippet
			WHERE createdBy = {userId: String}
				AND type = 'draft'
		`,
		{
			params: {
				userId
			}
		}
	);
}

export default {
	deleteCodeSnippetsByAppId,
	getAllCodeSnippetsByAppId,
	deleteCodeSnippetsOfUserDrafts,
	deleteCodeSnippetById,
	getCodeSnippetById,
	createCodeSnippet,
	getAllDraftUserId,
	updateCodeSnippet,
	upsertCodeSnippet,
	getCodeSnippetByIdDecoded
};
