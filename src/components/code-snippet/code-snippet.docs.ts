import type { RouteOpenAPIDocs } from '@/types/http/route.docs';
import { z } from 'zod';

export const codeSnippetDocs = {
	deleteCodeSnippet: {
		description: 'Delete a code snippet by its ID',
		responses: {
			200: {
				content: {
					application: {
						schema: {
							properties: {
								data: {
									properties: {
										success: { type: 'boolean' }
									},
									type: 'object'
								},
								message: { type: 'string' },
								timestamp: { type: 'string' }
							}
						}
					}
				},
				description: 'Code snippet deleted'
			}
		}
	},

	getCodeSnippet: {
		description: 'Get a code snippet by its ID',
		responses: {
			200: {
				content: {
					application: {
						schema: {
							properties: {
								data: {
									properties: {
										appId: { type: 'string' },
										codeSnippet: { type: 'string' },
										codeSnippetId: { type: 'string' },
										flowId: { type: 'string' },
										type: { type: 'string' }
									},
									type: 'object'
								},
								message: { type: 'string' },
								timestamp: { type: 'string' }
							},
							type: 'object'
						}
					}
				},
				description: 'Code snippet found'
			}
		}
	},

	saveCodeSnippet: {
		description:
			'Save a code snippet - Body: `{ "codeSnippetData": { "appId": "appid", "codeSnippetId": "codesnippetid", "flowId": "flowid", "options": {}, "type": "type" }}`',
		responses: {
			200: {
				content: {
					application: {
						schema: {
							properties: {
								data: { type: 'string' },
								message: { type: 'string' },
								timestamp: { type: 'string' }
							},
							type: 'object'
						}
					}
				},
				description: 'Code snippet saved'
			}
		}
	}
} satisfies RouteOpenAPIDocs;

export const codeSnippetSaveRequestSchema = z
	.object({
		appId: z.string(),
		codeSnippetId: z.string(),
		flowId: z.string(),
		options: z.object({}).passthrough(),
		type: z.string()
	})
	.passthrough();
