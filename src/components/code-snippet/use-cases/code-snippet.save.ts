import { dbGaio } from '@/db/db.gaio';
import { clickDate } from '@/utils/helpers';
import type { GenericType } from '@gaio/shared/types';

type CodeSnippetsSaveDTO = {
	codeSnippetId: string;
	appId: string;
	flowId: string;
	codeSnippet: string;
	type: string;
	options: unknown;
};

export async function codeSnippetSave({
	data,
	userId
}: { data: CodeSnippetsSaveDTO; userId: string }) {
	const { appId, codeSnippet, codeSnippetId, flowId, type, options } = data;

	let updateValues: {
		table: string;
		values: GenericType;
		primaryKeys: string[];
	};

	if (type === 'draft') {
		updateValues = {
			primaryKeys: ['appId', 'codeSnippetId', 'createdBy'],
			table: 'code_snippet',
			values: {
				appId,
				codeSnippet,
				codeSnippetId,
				createdAt: clickDate(new Date()),
				createdBy: userId,
				flowId,
				options: JSON.stringify(options || {}),
				type,
				updatedAt: clickDate(new Date()),
				updatedBy: userId
			}
		};
	} else {
		updateValues = {
			primaryKeys: ['appId', 'codeSnippetId', 'flowId'],
			table: 'code_snippet',
			values: {
				appId,
				codeSnippet,
				codeSnippetId,
				createdAt: clickDate(new Date()),
				createdBy: userId,
				flowId,
				options: JSON.stringify(options || {}),
				type,
				updatedAt: clickDate(new Date()),
				updatedBy: userId
			}
		};
	}

	return await dbGaio().updateOrInsert(updateValues);
}
