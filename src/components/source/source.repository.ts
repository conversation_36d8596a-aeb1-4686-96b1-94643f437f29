import { dbGaio } from '@/db/db.gaio';
import type { CredentialsType, SourceType, UserType } from '@gaio/shared/types';

async function getSources() {
	return await dbGaio('getSources').query(
		`SELECT sourceId, sourceName, client
			FROM source`
	);
}

async function deleteSource(sourceId: string) {
	return dbGaio('deleteSource').query(
		`ALTER TABLE source 
			DELETE WHERE sourceId = {sourceId: String}`,
		{ params: { sourceId } }
	);
}

async function getSourceById(sourceId: string): Promise<CredentialsType> {
	return (await dbGaio('getSource')
		.query<{ sourceId: unknown; credentials: CredentialsType }>(
			`SELECT sourceId, credentials
				FROM source
				WHERE sourceId = { sourceId: String }`,
			{
				params: { sourceId },
				parse: ['credentials']
			}
		)
		.then((res) => {
			return res
				.map((o) => {
					const { credentials } = o;
					return {
						sourceId: o.sourceId,
						...credentials
					};
				})
				.reduce((acc, val) => Object.assign(acc, val), {});
		})) as CredentialsType;
}
async function getSourceByUser({ userId }: UserType) {
	return await dbGaio('getSourceByUser')
		.query<SourceType>(
			`SELECT source.client as client,
					source.sourceName as sourceName,
					source.sourceId as sourceId,
					source.credentials as credentials
				FROM source
				INNER JOIN tag ON tag.refId = source.sourceId
				WHERE tag.type = 'source'
				AND tag.userId = { userId: String }`,
			{
				params: { userId },
				parse: ['credentials']
			}
		)
		.then((res) => {
			return res.map((o) => {
				const { credentials } = o;
				const { database: databaseName, schemaName } = credentials;
				return {
					client: o.client,
					databaseName,
					sourceId: o.sourceId,
					sourceName: o.sourceName,
					sourceType: 'source',
					schemaName
				};
			});
		});
}

async function getSourcesInTagByType(sourceId: string) {
	return await dbGaio('getSourcesInTagByType').query(
		`SELECT source.sourceId, source.sourceId as refId, source.sourceName as name, 'source' AS type
			FROM source
			INNER JOIN tag ON tag.refId = source.sourceId
			WHERE source.sourceId = {sourceId: String}
			AND tag.type = 'source'`,
		{
			params: { sourceId }
		}
	);
}

async function getSourcesToTagControl(userId?: string) {
	if (userId) {
		return await dbGaio('getSourcesToTagControl').query(
			`SELECT source.sourceId, source.sourceId as refId, source.sourceName as name, 'source' AS type
				FROM source
				INNER JOIN tag ON tag.refId = source.sourceId
				WHERE tag.userId = {userId: String}
				AND tag.type = 'source'`,
			{
				params: { userId }
			}
		);
	}
	return await dbGaio('getSourcesToTagControl').query(
		`SELECT sourceId, sourceId as refId, sourceName as name, 'source' AS type
            FROM source`
	);
}

async function saveSource(sourceData: SourceType, contextUser: UserType) {
	return await dbGaio('saveSource').exec(
		`ALTER TABLE source
			UPDATE sourceName = {sourceName: String},
				credentials = {credentials: String},
				client = {client: String},
				modifiedBy = {modifiedBy: String},
				updatedAt = NOW()
			WHERE sourceId = {sourceId: String}`,
		{
			params: {
				client: sourceData.client,
				credentials: sourceData.credentials,
				modifiedBy: contextUser.userId,
				sourceId: sourceData.sourceId,
				sourceName: sourceData.sourceName
			},
			stringify: ['credentials']
		}
	);
}

async function createSource(sourceData: SourceType, contextUser: UserType) {
	return await dbGaio('createSource').insert('source', [
		{
			client: sourceData.client,
			createdBy: contextUser.userId,
			credentials: JSON.stringify(sourceData.credentials),
			modifiedBy: contextUser.userId,
			sourceId: sourceData.sourceId,
			sourceName: sourceData.sourceName
		}
	]);
}

async function getSourceCredentials(sourceId: string) {
	return await dbGaio('getSourceCredentials')
		.query<{ credentials: CredentialsType; client: string }>(
			`SELECT credentials, client
				FROM source 
				WHERE sourceId = {sourceId: String}
				LIMIT 1`,
			{ params: { sourceId }, parse: ['credentials'] }
		)
		.then((res) => {
			if (res[0] && res[0].credentials) {
				return {
					...res[0].credentials,
					client: res[0].client
				};
			}
		});
}

export default {
	createSource,
	deleteSource,
	getSourceById,
	getSourceByUser,
	getSourceCredentials,
	getSources,
	getSourcesInTagByType,
	getSourcesToTagControl,
	saveSource
};
