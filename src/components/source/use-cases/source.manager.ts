import SourceRepository from '@/components/source/source.repository';
import TagRepository from '@/components/tag/tag.repository';
import type { SourceType, UserType } from '@gaio/shared/types'
import { getId } from '@gaio/shared/utils';
import { dbConnections } from '@/db/db.connections'
import { defineConnectionName } from '@/utils/helpers'

export function listSources() {
	return SourceRepository.getSources();
}

export function removeDbConnectionInstance(sourceId: string) {
	const connectionName = defineConnectionName({
		sourceType: 'source',
		sourceId,
	})
	delete dbConnections[connectionName]
}

export async function deleteSource(sourceId: string) {
	removeDbConnectionInstance(sourceId)
	await SourceRepository.deleteSource(sourceId);
	return {
		status: 'done'
	};
}

export async function getSourceDataById(sourceId: string) {
	return SourceRepository.getSourceById(sourceId).then((res) => {
		delete res.password;
		delete res.user;
		return res;
	});
}

export async function upsertSource(sourceData: any, userContext: UserType) {
	if (sourceData.sourceId) {
		await SourceRepository.saveSource(sourceData, userContext);
	} else {
		const sourceId = getId();
		await SourceRepository.createSource(
			{
				client: sourceData.client,
				createdBy: userContext.userId,
				credentials: sourceData.credentials,
				modifiedBy: userContext.userId,
				sourceId,
				sourceName: sourceData.sourceName
			},
			userContext
		);
		await TagRepository.insertTagPermission([
			{
				createdBy: userContext.userId,
				modifiedBy: userContext.userId,
				refId: sourceId,
				role: 'view',
				type: 'source',
				userId: userContext.userId
			}
		]);
	}

	return {
		status: 'done'
	};
}
