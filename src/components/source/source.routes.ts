import {
	deleteSource,
	getSourceDataById,
	listSources, 
	removeDbConnectionInstance,
	upsertSource
} from '@/components/source/use-cases/source.manager'
import { tablePreviewSqlQuery } from '@/components/table/use-cases/table.preview-sql-query'
import jwtGuard from '@/server/middleware/jwt.guard'
import { readBody } from '@/server/middleware/readBody'
import type { SourceType } from '@gaio/shared/types'
import { Hono } from 'hono'

const app = new Hono()
	.get('/data/:sourceId', jwtGuard('admin').isAuth, async (c) => {
		const sourceId = c.req.param('sourceId')

		return c.json(await getSourceDataById(sourceId))
	})

	.get('/list', jwtGuard('admin').isAuth, async (c) => {
		return c.json(await listSources())
	})

	.post('/remove', jwtGuard('admin').isAuth, async (c) => {
		const { sourceId } = await readBody<{ sourceId: string }>(c)

		return c.json(await deleteSource(sourceId))
	})

	.post('/save', jwtGuard('admin').isAuth, async (c) => {
		const { sourceData, user } = await readBody<{ sourceData: unknown }>(c)

		return c.json(await upsertSource(sourceData, user))
	})

	.post('/test', jwtGuard('admin').isAuth, async (c) => {
		const { sourceData, user } = await readBody<{ sourceData: SourceType }>(c)

		if (user.role !== 'admin') return c.json({ message: 'denied' })

		try {
			removeDbConnectionInstance(sourceData.sourceId)
			const preview = await tablePreviewSqlQuery({
				client: sourceData.client,
				params: [],
				query: sourceData.client !== 'oracle' ? 'select 1 as result' : 'select 1 as result from dual',
				sourceId: sourceData.sourceId
			})

			if (Array.isArray(preview)) {
				return c.json(preview[0])
			}
			return c.json(preview)
		} catch (e) {
			return c.json({
				error: true,
				code: e.code,
				message: e.message,
				status: e.status
			})
		}
	})

export default app
