import { HTTPException } from 'hono/http-exception';
import { aml, automlRest } from './automl.core';

export async function progressFromUseCase(jobId: string) {
	try {
		return await automlRest.get(aml(`3/Jobs/${jobId}`));
	} catch (error) {
		if (error instanceof HTTPException) {
			throw error;
		}

		throw new HTTPException(500, {
			cause: 'internalErrorGettingAutomlProgress',
			message: 'Internal server error getting automl progress'
		});
	}
}
