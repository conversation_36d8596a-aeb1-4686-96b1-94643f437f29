import * as qs from 'node:querystring';
import { env } from '@/utils/env';

export const automlRest = {
	delete: async (url: string) => {
		const response = await fetch(url, { method: 'DELETE' });
		return await response.json();
	},
	get: async (url: string, headers = {}) => {
		try {
			const response = await fetch(url, { headers, method: 'GET' });
			return await response.json();
		} catch (e) {
			console.log(`get automl: ${e.message}`);
			throw e;
		}
	},
	post: async (
		url: string,
		body = {},
		headers = { 'Content-Type': 'application/json' }
	) => {
		try {
			const contentType = headers['Content-Type'];
			let serializedBody: XMLHttpRequestBodyInit = undefined;

			if (contentType === 'multipart/form-data') {
				serializedBody = qs.stringify(body);
			}

			if (contentType === 'application/json') {
				serializedBody = JSON.stringify(body);
			}

			if (contentType === 'application/x-www-form-urlencoded') {
				serializedBody = qs.stringify(body);
			}

			const response = await fetch(url, {
				body: serializedBody,
				headers,
				method: 'POST'
			});

			return await response.json();
		} catch (e) {
			console.log(`post automl: ${e.message}`);
			throw e;
		}
	}
};

export async function checkAutoMLStatus() {
	try {
		return await automlRest.get(aml('3/About'));
	} catch {
		throw new Error('AutoML is offline');
	}
}

export const aml = (endpoint: string) => `${env.H2O_API_BASE_URL}/${endpoint}`;
