import { join } from 'node:path';
import { modelsFolder } from '@/components/task/use-cases/runners/runner.tools';
import type { AutoMLTaskType } from '@gaio/shared/types';
import { ensureDirSync, readdirSync } from 'fs-extra';
import { HTTPException } from 'hono/http-exception';
import { aml, automlRest, checkAutoMLStatus } from './automl.core';
import { listModelByNameUseCase } from './automl.list-model-by-name';

export async function resultFromUseCase(taskData: AutoMLTaskType) {
	try {
		await checkAutoMLStatus();

		const path = modelsFolder(taskData.appId, taskData.id);
		ensureDirSync(path);

		const models = readdirSync(path).filter((file) => !file.endsWith('.json'));

		if (models.length === 0) {
			return {
				error: 'noModelFound'
			};
		}

		const modelData = await automlRest.post(
			aml('/99/Models.bin/not_in_use'),
			{
				dir: join(path, models[0]),
				force: true
			},
			{
				'Content-Type': 'application/json'
			}
		);

		const modelName = modelData.models[0].model_id.URL;

		return await listModelByNameUseCase({
			appId: taskData.appId,
			modelName: modelName,
			taskDataId: taskData.id
		});
	} catch (err) {
		console.error('error', err);
		if (err instanceof HTTPException) {
			throw err;
		}

		throw new HTTPException(500, {
			cause: 'internalErrorGettingAutomlResult',
			message: 'Internal error getting automl result'
		});
	}
}
