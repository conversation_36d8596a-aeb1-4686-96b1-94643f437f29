import { progressFromUseCase } from '@/components/automl/automl.progress-from';
import { resultFromUseCase } from '@/components/automl/automl.result-from';
import jwtGuard from '@/server/middleware/jwt.guard';
import { readBody } from '@/server/middleware/readBody';
import type { AutoMLTaskType } from '@gaio/shared/types';
import { Hono } from 'hono';

const app = new Hono()
	.post('result-from', jwtGuard('user').isAuth, async (c) => {
		const { taskData } = await readBody<{ taskData: AutoMLTaskType }>(c);

		return c.json(await resultFromUseCase(taskData));
	})

	.post('progress', jwtGuard('user').isAuth, async (c) => {
		const { jobId } = await readBody<{ jobId: string }>(c);

		return c.json(await progressFromUseCase(jobId));
	});

export default app;
