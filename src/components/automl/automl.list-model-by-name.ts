import { join } from 'node:path';
import { modelsFolder } from '@/components/task/use-cases/runners/runner.tools';
import { HTTPException } from 'hono/http-exception';
import { aml, automlRest } from './automl.core';

export async function listModelByNameUseCase({
	appId,
	taskDataId,
	modelName
}: {
	appId: string;
	taskDataId: string;
	modelName: string;
}) {
	try {
		const file = Bun.file(
			join(modelsFolder(appId, taskDataId), 'models_metadata.json')
		);
		const doesFileExists = await file.exists();

		if (!doesFileExists) {
			throw new HTTPException(404, {
				cause: 'modelsMetadataNotFound',
				message: 'Models metadata not found'
			});
		}

		return await automlRest.get(aml(modelName)).then(async (res) => {
			const model = res.models[0];

			return {
				algo: model.algo_full_name,
				model: model.model_id,
				output: model.output,
				response: model.response_column_name,
				summary: await file.json()
			};
		});
	} catch (error) {
		console.log('error', error);
		if (error instanceof HTTPException) {
			throw error;
		}

		throw new HTTPException(500, {
			cause: 'internalErrorListingModelsByName',
			message: 'Internal server error listing models by name'
		});
	}
}
