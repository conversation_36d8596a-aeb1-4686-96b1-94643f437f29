import { dbGaio } from '@/db/db.gaio';
import { currentDatetime64 } from '@/utils/helpers';
import type { TaskJobType, TaskLogType } from '@gaio/shared/types';
import { getId } from '@gaio/shared/utils';

async function createTaskLog(
	appId: string,
	flowId: string,
	userId: string,
	logFrom: string
): Promise<TaskLogType> {
	const taskLogId = getId();
	const startedAt = currentDatetime64();
	const endedAt = currentDatetime64();
	await dbGaio('insertTaskLog').insert('task_log', [
		{
			logFrom,
			aborted: false,
			appId: appId,
			startedAt,
			endedAt,
			flowId,
			status: 'started',
			taskLogId,
			tasks: JSON.stringify({}),
			userId: userId
		}
	]);
	const result = await findTaskLog(taskLogId);
	return result;
}

async function findTaskLog(taskLogId: string): Promise<TaskLogType> {
	return await dbGaio('listTaskLog')
		.query('SELECT * FROM task_log WHERE taskLogId = {taskLogId: String}', {
			params: { taskLogId },
			parse: ['tasks']
		})
		.then((res) => res[0] as TaskLogType);
}

async function finalizeTaskLog(
	taskLogId: string,
	status: string,
	aborted: boolean
) {
	const endedAt = currentDatetime64();
	await dbGaio('finalizeTaskLogStatus').exec(
		`ALTER TABLE task_log
			UPDATE status = {status: String}, endedAt = {endedAt: Datetime64}, aborted = {aborted: Boolean}
			WHERE taskLogId = {taskLogId: String}`,
		{
			params: {
				aborted,
				endedAt,
				status,
				taskLogId
			}
		}
	);
}

async function getLogsListByAppId(
	appId: string,
	userId: string,
	logFrom: string,
	all = false
) {
	let whereUser = '';

	if (!all && ['studio', 'dashboard'].includes(logFrom)) {
		whereUser = 'AND userId = {userId: String}';
	}

	return await dbGaio('listTaskLogByUserAndApp').query(
		`SELECT taskLogId,
						appId,
						flowId,
						userId,
						startedAt,
						endedAt,
						aborted,
						status,
			 			logFrom,
						tasks,
						user.name as userName,
						user.userId as userId
		 FROM task_log
			 LEFT JOIN user
		 ON task_log.userId = user.userId
		 WHERE appId = {appId: String}
			 AND
			 logFrom = {logFrom : String}
			 ${whereUser}
		 ORDER BY startedAt DESC LIMIT 10`,
		{
			params: {
				appId,
				logFrom,
				userId
			},
			parse: ['tasks']
		}
	);
}

async function updateSingleTaskLog(
	taskLog: TaskLogType,
	taskJobItems: Record<string, TaskJobType>
) {
	await dbGaio('updateTaskLogData').exec(
		`ALTER TABLE task_log
			UPDATE tasks = {tasks: String}, endedAt = {endedAt: Datetime}
			WHERE taskLogId = {taskLogId: String}`,
		{
			params: {
				endedAt: currentDatetime64(),
				taskLogId: taskLog.taskLogId,
				tasks: taskJobItems
			},
			stringify: ['tasks']
		}
	);
}

async function deleteTaskLogByAppId(appId: string) {
	return await dbGaio('deleteTaskLogByApp').exec(
		'DELETE FROM task_log WHERE appId = {appId: String}',
		{
			params: {
				appId
			}
		}
	);
}

export default {
	createTaskLog,
	deleteTaskLogByAppId,
	finalizeTaskLog,
	getLogsListByAppId,
	updateSingleTaskLog
};
