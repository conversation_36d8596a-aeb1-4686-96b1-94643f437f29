import apiHookRepository from '@/components/api-hook/api-hook.repository';
import AppRepository from '@/components/app/app.repository';
import FlowRepository from '@/components/flow/flow.repository';
import { prepareFlowAndRun } from '@/components/task/use-cases/task.runner';
import { dbGaio } from '@/db/db.gaio';
import type { DatabaseConnectionQuery } from '@/db/db.hub';
import { DbService } from '@/db/db.service';
import type { ParamType, TaskType } from '@gaio/shared/types';
import { getBucketNameFromAppId, getId } from '@gaio/shared/utils';
import type { Context } from 'hono';
import { HTTPException } from 'hono/http-exception';
import { verify } from 'hono/jwt';

export type RestfulApiUseCaseQueryParams = {
	orderBy?: string;
	limit?: string;
	offset?: string;
	page?: string;
	[key: string]: string;
};

function hasSQLInjectionOrderBy(orderBy: string) {
	// Define a regular expression pattern to match common SQL injection keywords
	const sqlInjectionRegex =
		/(union|select|from|where|drop|delete|truncate|alter|create|exec|execute|insert|update|set|database|schema|table|column|grant|revoke|commit|rollback|transaction|declare|cast|convert|script|function|procedure|trigger|index|constraint|backup|restore|merge|label|goto)/i;

	// Check if the orderBy parameter matches the SQL injection pattern
	return sqlInjectionRegex.test(orderBy);
}

async function dropTmpTables(
	appId: string,
	sessionid: string,
	dbConnection: DatabaseConnectionQuery
) {
	const tables = await dbGaio().query(
		`
			SELECT table
			FROM system.columns
			WHERE database = 'bucket_${appId}'
			AND startsWith(table, 'tmp_gaio${sessionid}')
			GROUP BY table
		`
	);

	for (const table of tables) {
		await dbConnection
			.query(
				`
					DROP TABLE IF EXISTS bucket_${appId}.${table}
				`
			)
			.catch(() =>
				console.warn('[REST] Failed to drop temporary tables', {
					asError: true
				})
			);
	}

	return;
}

export async function processRestfulApi({
	appId,
	flowId,
	tableName,
	queryAndBody
}: {
	appId: string;
	flowId: string;
	tableName: string;
	queryAndBody: RestfulApiUseCaseQueryParams;
}) {
	if (!flowId && !tableName) {
		throw new HTTPException(400, {
			cause: 'invalidRequest',
			message: 'Invalid Request'
		});
	}

	const sessionid = getId();

	// 1. execute flow, if any
	if (flowId && appId) {
		const result = await FlowRepository.getFlowToRestfullApi(flowId, appId);

		if (!result) {
			throw new HTTPException(404, {
				cause: 'flowNotFound',
				message: 'Flow not found'
			});
		}

		const params = result.params || [];

		for (const [key, value] of Object.entries(queryAndBody)) {
			params.forEach((param: ParamType) => {
				if (param.paramName === key) {
					param.paramValue = value;
				}
			});
		}

		await prepareFlowAndRun({
			appId,
			flowId,
			params,
			sessionid,
			logFrom: 'studio',
			userId: 'user:restful-api'
		});
	}

	// 2. return table, if any
	if (tableName) {
		return await getTableData({
			tableName,
			queryAndBody,
			appId,
			sessionid
		});
	}

	return {
		message: 'success'
	};
}

async function getTableData({
	tableName,
	queryAndBody,
	appId,
	sessionid
}: {
	tableName: string;
	queryAndBody: RestfulApiUseCaseQueryParams;
	appId: string;
	sessionid: string;
}): Promise<{
	data: unknown[];
	statistics?: {
		elapsed: number;
		rows_read: number;
		bytes_read: number;
	};
}> {
	const appData = await AppRepository.getRepoIdAppToRestfulById(appId);

	if (!appData || !appData.repoId) {
		throw new HTTPException(401, {
			cause: 'invalidAccess',
			message: 'invalidAccess'
		});
	}

	const { limit, offset, orderBy, page } = queryAndBody;

	let orderByStatement = '';
	let pagination = '';

	if (orderBy) {
		if (!hasSQLInjectionOrderBy(orderBy)) {
			orderByStatement = `ORDER BY ${orderBy}`;
		}
	}

	if (limit && Number(limit) > 0) {
		let paginationOffset = offset ?? 0;

		if (page) {
			if (Number(page) > 1) {
				paginationOffset = Number(page) - Number(limit);
			}

			if (Number(page) <= 0) {
				return {
					data: []
				};
			}
		}

		pagination = `LIMIT ${limit} OFFSET ${paginationOffset}`;
	} else {
		pagination = 'LIMIT 1000';
	}

	const sql = `
			SELECT *
				FROM ${tableName}
				${orderByStatement}
				${pagination}
		`;

	const task = {
		appId,
		repoId: appData.repoId,
		tableName,
		databaseName: getBucketNameFromAppId(appId),
		client: 'clickhouse',
		sourceType: 'bucket',
		type: 'table'
	} as TaskType;

	const db = await new DbService().connect(task, {
		sessionid: sessionid
	});

	return await db
		.query(sql)
		.then(async (results) => {
			await dropTmpTables(appId, sessionid, db);
			return results;
		})
		.catch(async () => {
			await dropTmpTables(appId, sessionid, db);

			throw new HTTPException(500, {
				cause: 'errorRunningRestfulApi',
				message: 'Error Running Restful Api'
			});
		});
}

export async function verifyRestfullToken(c: Context, assignedKeys: string[]) {
	const bearerToken = c.req.header('Authorization');

	if (!bearerToken) {
		throw new HTTPException(401, {
			cause: 'invalidAuthorizationHeader',
			message: 'Make sure to use the Authorization header'
		});
	}

	const token = bearerToken.split(' ')[1];

	if (!token) {
		throw new HTTPException(401, {
			cause: 'invalidBearer',
			message: 'Use Bearer Authorization'
		});
	}

	const secretList =
		await apiHookRepository.getApiKeySecretsOfIds(assignedKeys);

	let checkSecret = false;

	for (const secret of secretList) {
		try {
			await verify(token, secret);
			checkSecret = true;
			break;
		} catch {}
	}

	if (!checkSecret) {
		throw new HTTPException(401, {
			cause: 'invalidBearerToken',
			message: 'Token Invalid'
		});
	}

	return {
		token
	};
}
