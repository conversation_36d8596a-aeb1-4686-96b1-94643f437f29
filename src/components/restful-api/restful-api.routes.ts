import apiHookRepository from '@/components/api-hook/api-hook.repository';
import {
	type RestfulApiUseCaseQueryParams,
	processRestfulApi,
	verifyRestfullToken
} from '@/components/restful-api/restful-api.core';
import type { GenericType } from '@gaio/shared/types';
import { Hono } from 'hono';
import { HTTPException } from 'hono/http-exception';

const app = new Hono().on(['GET', 'POST'], '/:apiId/:endpoint', async (c) => {
	const { apiId, endpoint } = c.req.param();
	const apiData = await apiHookRepository.getApiById(apiId, endpoint);

	if (!apiData) {
		throw new HTTPException(404, {
			cause: 'apiNotFound',
			message: 'Api not found'
		});
	}

	if (!apiData.assignedKeys.length) {
		throw new HTTPException(401, {
			cause: 'invalidApiKey',
			message: 'Invalid API Key'
		});
	}

	await verifyRestfullToken(c, apiData.assignedKeys);

	const queryParams = c.req.query() as RestfulApiUseCaseQueryParams;

	let body: GenericType = null;

	if (c.req.method === 'POST') {
		body = await c.req.json();

		if (!body) {
			throw new HTTPException(400, {
				cause: 'invalidBody',
				message: 'Invalid Body'
			});
		}
	}

	const queryAndBody = Object.assign({}, queryParams, body);

	return c.json(
		await processRestfulApi({
			flowId: apiData.flowId,
			appId: apiData.appId,
			tableName: apiData.tableName,
			queryAndBody
		})
	);
});

export default app;
