import { mkdir, unlink } from 'node:fs/promises';
import { join } from 'node:path';
import { mapAssetsFilesFolderPath } from '@/components/content/use-cases/content.core';
import type { GenericType } from '@gaio/shared/types';
import { HTTPException } from 'hono/http-exception';

/**
 * Saves a JSON metadata object to a file using Bun.write
 * @param options - Object containing metadata and file information
 * @param options.metadata - The JSON object to save
 * @param options.fileName - Name of the file to save (without extension)
 * @returns Promise with success message
 */
export async function saveMetadata({
	geoJSON,
	fileName
}: { geoJSON: GenericType; fileName: string }) {
	const folderPath = join(mapAssetsFilesFolderPath(''));
	const fullFileName = `${fileName}.json`;

	// Ensure directory exists
	await mkdir(folderPath, { recursive: true });

	try {
		// Convert metadata object to JSON string
		const jsonData = JSON.stringify(geoJSON, null, 2);

		// Write to file
		await Bun.write(join(folderPath, fullFileName), jsonData);

		return {
			success: true,
			message: 'Metada<PERSON> saved successfully'
		};
	} catch (error) {
		throw new Error(
			`Failed to save metadata: ${error instanceof Error ? error.message : 'Unknown error'}`
		);
	}
}

/**
 * Reads a JSON metadata file and returns a Bun stream file
 * @param options - Object containing file information
 * @param options.fileName - Name of the file to read (without extension)
 * @returns Promise with the Bun file stream
 */
export async function readMetadata({ fileName }: { fileName: string }) {
	const fullFileName = `${fileName}.json`;
	const filePath = join(mapAssetsFilesFolderPath(), fullFileName);

	const file = Bun.file(filePath);
	const doesFileExist = await file.exists();

	if (!doesFileExist) {
		throw new HTTPException(404, {
			cause: 'fileNotFound',
			message: `Metadata file not found: ${fullFileName}`
		});
	}

	try {
		// Return the file stream directly
		return file.stream();
	} catch (error) {
		throw new Error(
			`Failed to read metadata: ${error instanceof Error ? error.message : 'Unknown error'}`
		);
	}
}

/**
 * Deletes a metadata file
 * @param options - Object containing file information
 * @param options.fileName - Name of the file to delete (without extension)
 * @returns Promise with success message
 */
export async function deleteMetadata({ fileName }: { fileName: string }) {
	const fullFileName = `${fileName}.json`;
	const filePath = join(mapAssetsFilesFolderPath(), fullFileName);

	const file = Bun.file(filePath);
	const doesFileExist = await file.exists();

	if (!doesFileExist) {
		throw new HTTPException(404, {
			cause: 'fileNotFound',
			message: `Metadata file not found: ${fullFileName}`
		});
	}

	try {
		// Delete the file
		await unlink(filePath);

		return {
			success: true,
			message: 'Metadata deleted successfully'
		};
	} catch (error) {
		throw new Error(
			`Failed to delete metadata: ${error instanceof Error ? error.message : 'Unknown error'}`
		);
	}
}
