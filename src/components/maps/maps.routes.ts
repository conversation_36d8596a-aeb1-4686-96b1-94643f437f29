import jwtGuard from '@/server/middleware/jwt.guard';
import { type Context, Hono } from 'hono';
import { HTTPException } from 'hono/http-exception';
import mapsRepository from './maps.repository';

import {
	deleteMetadata,
	readMetadata,
	saveMetadata
} from '@/components/maps/map.content';
import { readBody } from '@/server/middleware/readBody';
import type { GenericType, MapType } from '@gaio/shared/types';

const app = new Hono();

app.post('/save', jwtGuard('user').isAuth, async (c) => {
	const { appId, mapId, options, shared, user } = await readBody<MapType>(c);

	return c.json(
		await mapsRepository.upsertMapsMetadata({
			appId: appId,
			mapId: mapId,
			userId: user.userId,
			shared: shared,
			options: options
		})
	);
});

app.get('/list/:appId', jwtGuard('user').isAuth, async (c) => {
	const { appId } = c.req.param();

	return c.json(await mapsRepository.getAllMaps(appId));
});

app.delete('/delete/:mapId', jwtGuard('user').isAuth, async (c: Context) => {
	const { mapId } = c.req.param();
	const { appid: appId } = c.req.header();

	await mapsRepository.deleteMapById(mapId, appId);

	try {
		await deleteMetadata({
			fileName: mapId
		});
	} catch {
		return c.json({
			success: true,
			message: 'Map deleted successfully, but failed to delete map file'
		});
	}

	return c.json({
		success: true,
		message: 'Map deleted successfully'
	});
});

app.post('/upload/:id', async (c) => {
	const { id } = c.req.param();

	try {
		const { geoJSON } = await readBody<{ geoJSON: GenericType } & {}>(c);

		const res = await saveMetadata({ geoJSON, fileName: id });

		return c.json({
			message: res.message,
			timestamp: new Date().toISOString()
		});
	} catch (err) {
		console.log(err);
		if (err instanceof HTTPException) {
			return c.json(
				{
					details: err.message,
					message: err.cause,
					timestamp: new Date().toISOString()
				},
				err.status
			);
		}

		return c.json(
			{
				message: 'internalErrorUploadingGeojson',
				timestamp: new Date().toISOString()
			},
			{ status: 500 }
		);
	}
});

app.get('/read/:id', async (c) => {
	try {
		const { id } = c.req.param();
		const stream = await readMetadata({ fileName: id });

		return c.body(stream, { status: 200 });
	} catch (err) {
		if (err instanceof HTTPException) {
			return c.json(
				{
					details: err.message,
					message: err.cause,
					timestamp: new Date().toISOString()
				},
				err.status
			);
		}

		return c.json(
			{
				details: 'Internal error reading file',
				message: 'internalErrorReadingFile',
				timestamp: new Date().toISOString()
			},
			{ status: 500 }
		);
	}
});

export default app;
