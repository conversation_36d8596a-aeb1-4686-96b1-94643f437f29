import { join } from 'node:path';
import { mapAssetsFilesFolderPath } from '@/components/content/use-cases/content.core';
import { dbGaio } from '@/db/db.gaio';
import { currentDatetime } from '@/utils/helpers';
import type { GenericType, MapType } from '@gaio/shared/types';
import { remove } from 'fs-extra';

async function upsertMapsMetadata({
	appId,
	mapId,
	options,
	userId,
	shared
}: {
	appId: string;
	mapId: string;
	userId: string;
	shared: boolean;
	options: GenericType;
}) {
	try {
		await dbGaio().updateOrInsert({
			primaryKeys: ['mapId'],
			table: 'map',
			values: {
				mapId,
				appId,
				options: JSON.stringify(options || {}),
				shared,
				createdBy: userId,
				updatedBy: userId,
				updatedAt: currentDatetime().replace('T', ' ').replace('Z', '')
			}
		});

		return { message: 'Map metadata saved successfully' };
	} catch (error) {
		throw new Error(
			`Failed to upsert map metadata: ${error instanceof Error ? error.message : 'Unknown error'}`
		);
	}
}

async function getAllMaps(appId: string): Promise<MapType[]> {
	try {
		const query = `
			SELECT * FROM map
			WHERE shared = true
			OR appId = {appId: String}
		`;

		return await dbGaio().query(query, {
			params: { appId },
			parse: ['options']
		});
	} catch (error) {
		throw new Error(
			`Failed to fetch maps: ${error instanceof Error ? error.message : 'Unknown error'}`
		);
	}
}

async function deleteMapById(mapId: string, currentAppId: string) {
	try {
		const query = `
			ALTER TABLE map
			DELETE
			WHERE mapId = {mapId: String}
			AND appId = {appId: String}
		`;

		// table register
		await dbGaio().query(query, { params: { mapId, appId: currentAppId } });

		// file
		await remove(join(mapAssetsFilesFolderPath(''), `${mapId}.json`));

		return {
			message: 'Map deleted successfully'
		};
	} catch (error) {
		throw new Error(
			`Failed to delete map: ${error instanceof Error ? error.message : 'Unknown error'}`
		);
	}
}

export default {
	upsertMapsMetadata,
	deleteMapById,
	getAllMaps
};
