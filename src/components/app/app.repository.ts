import SerialRepository from '@/components/serial/serial.repository'
import { dbGaio } from '@/db/db.gaio'
import type { AppEntity } from '@/db/entities'
import type { AppType, FormType, ParamType, TagTypePermission, UserType } from '@gaio/shared/types'
import { getId } from '@gaio/shared/utils'
import { uniqBy } from 'lodash-es'

const validateAppId = (appId: string) => {
	return /^app:\d+$/.test(appId)
}

async function getAllApps(user: UserType) {
	const result = await dbGaio().query<AppType>(
		`SELECT app.appId          AS appId,
						app.appToken       AS appToken,
						app.repoId         AS repoId,
						app.appName        AS appName,
						app.appDescription AS appDescription,
						app.options        AS options,
						app.updatedAt      AS updatedAt,
						tag.role           AS role
		 FROM app
						INNER JOIN
					tag ON tag.refId = app.appId
		 WHERE tag.userId = {userId: String}
		 ORDER BY
			 app.updatedAt DESC`,

		{
			params: {
				userId: user.userId
			},
			parse: ['options']
		}
	)
	if (!result || !Array.isArray(result)) {
		throw new Error('Query failed or returned unexpected result')
	}
	return result.flat(2)
}

async function getAppsInTagByType(appId: string) {
	const result = await dbGaio('getAppsInTagByType').query<TagTypePermission[]>(
		`SELECT DISTINCT ap.appId,
										 ap.appId   AS refId,
										 ap.appName AS name,
										 'app'      AS type
		 FROM app ap
						LEFT JOIN
					tag ta ON ta.refId = ap.appId AND ta.type = 'app'
		 WHERE ap.appId = {appId: String}`,
		{
			params: {
				appId
			}
		}
	)

	if (!result || !Array.isArray(result)) {
		throw new Error('Query failed or returned unexpected result')
	}
	return result.flat(2)
}

async function getAppsToTagControl(userId?: string) {
	let result: TagTypePermission[] = []
	if (userId)
		result = await dbGaio('getAppsToTagControl').query<TagTypePermission>(
			`SELECT DISTINCT ap.appId,
											 ta.userId,
											 ta.role,
											 ap.appId   AS refId,
											 ap.appName AS name,
											 'app'      AS type
			 FROM app ap
							LEFT JOIN
						tag ta ON ta.refId = ap.appId AND ta.type = 'app'
			 WHERE ta.userId = {userId: String}`,
			{
				params: {
					userId
				}
			}
		)
	else
		result = await dbGaio('getAppsToTagControl').query<TagTypePermission>(
			`SELECT appId,
							appId   AS refId,
							appName AS name,
							'app'   AS type
			 FROM app`
		)
	if (!result || !Array.isArray(result)) {
		throw new Error('Query failed or returned unexpected result')
	}
	return result.flat(2)
}

async function getAppsByIds(listOfIds: string[], user: UserType) {
	const result = await dbGaio('getAppsByIds').query<AppType>(
		`SELECT app.appId          AS appId,
						app.appToken       AS appToken,
						app.repoId         AS repoId,
						app.appName        AS appName,
						app.appDescription AS appDescription,
						app.options        AS options,
						app.updatedAt      AS updatedAt,
						tag.role           AS role
		 FROM app
						INNER JOIN
					tag ON tag.refId = app.appId
		 WHERE tag.userId = {userId: String}
			 AND appId in ({ids: Array(String)})
		 ORDER BY
			 app.updatedAt DESC`,

		{
			params: {
				ids: listOfIds,
				userId: user.userId
			},
			parse: ['options']
		}
	)
	if (!result || !Array.isArray(result)) {
		throw new Error('Query failed or returned unexpected result')
	}
	return result.flat(2)
}

async function getOptionsOfAppId(appId: string) {
	if (validateAppId(appId)) {
		const result = await dbGaio().query<AppType>(
			`SELECT options
			 FROM app
			 WHERE appId = {appId: String}`,
			{
				params: {
					appId
				},
				parse: ['options']
			}
		)
		if (!result || !Array.isArray(result)) {
			throw new Error('Query failed or returned unexpected result')
		}

		if (result.length === 0) return {}

		return result[0].options
	}
	throw new Error('Invalid appId')
}

async function getSingleAppById(appId: string) {
	if (validateAppId(appId)) {
		const result = await dbGaio().query<AppType>(
			`SELECT *
			 FROM app
			 WHERE appId = {appId: String} `,
			{
				params: {
					appId
				},
				parse: ['options', 'params', 'forms']
			}
		)
		if (!result || !Array.isArray(result)) {
			throw new Error('Query failed or returned unexpected result')
		}
		return result[0]
	}
	throw new Error('Invalid appId')
}

async function getRepoIdAppToRestfulById(appId: string): Promise<AppType> {
	if (validateAppId(appId)) {
		const result = await dbGaio().query<AppType>(
			`SELECT repoId
			 FROM app
			 WHERE appId = {appId: String} `,
			{
				params: {
					appId
				}
			}
		)
		if (!result || !Array.isArray(result)) {
			throw new Error('Query failed or returned unexpected result')
		}
		return result[0]
	}
	throw new Error('Invalid appId')
}

async function getAppIdByAppToken(appToken: string): Promise<AppType> {
	const result = await dbGaio().query<AppType>(
		`SELECT appId
		 FROM app
		 WHERE appToken = {appToken: String} `,
		{
			params: {
				appToken
			}
		}
	)
	if (!result || !Array.isArray(result)) {
		throw new Error('Query failed or returned unexpected result')
	}
	return result[0]
}

async function getAppParamsById(appId: string) {
	if (validateAppId(appId)) {
		const result = await dbGaio().query<AppType>(
			`SELECT params
			 FROM app
			 WHERE appId = {appId: String} `,
			{
				params: {
					appId
				},
				parse: ['params']
			}
		)

		return result[0].params
	}
	throw new Error('Invalid appId')
}

async function createApp(app: Partial<AppType>) {
	const serialNumber = await SerialRepository.getAppSerial()

	app.appId = `app:${serialNumber}`

	if (app.params && Array.isArray(app.params)) {
		app.params = app.params.filter((param: ParamType) => param.paramName !== 'userId')
	}

	await dbGaio().insert('app', [
		{
			...app,
			appStatus: 'active',
			appToken: app.appToken || getId(32),
			options: JSON.stringify(app.options)
		}
	])

	return app
}

async function mergeApp(app: Partial<AppEntity>, user: UserType) {
	return await dbGaio().exec(
		`ALTER TABLE
			app
		UPDATE
			appName = {appName: String},
			appDescription = {appDescription: String},
			options = {options : String},
			modifiedBy = {modifiedBy: String},
			appToken = {appToken: String}
		WHERE
			appId = {appId: String}`,
		{
			params: {
				appDescription: app.appDescription,
				appId: app.appId,
				appName: app.appName,
				modifiedBy: user.userId,
				options: app.options,
				appToken: app.appToken || getId(32)
			},
			stringify: ['options']
		}
	)
}

async function updateAllAppData(appId: string, appData: AppType, user: UserType) {
	// we cant update here appToken, since it is used by replicate service

	await dbGaio().exec(
		`ALTER TABLE
			app
		UPDATE
			appName = {appName: String},
			appDescription = {appDescription: String},
			options = {options : String},
			params = {params: String},
			forms = {forms: String},
			modifiedBy = {modifiedBy: String}
		WHERE
			appId = {appId: String}`,
		{
			params: {
				appId,
				appName: `Copy - ${appData.appName}`,
				appDescription: appData.appDescription,
				options: appData.options,
				params: appData.params,
				forms: appData.forms,
				modifiedBy: user.userId
			},
			stringify: ['options', 'params', 'forms']
		}
	)
}

async function deleteApp(appId: string) {
	return await dbGaio().exec(
		`DELETE
		 FROM app
		 WHERE appId = {appId: String}`,
		{
			params: {
				appId
			}
		}
	)
}

async function mergeAppOptions(userId: string, appId: string, options: Record<string, unknown>) {
	return await dbGaio().exec(
		`ALTER TABLE
			app
		UPDATE
			options = {options : String},
			modifiedBy = {modifiedBy: String}
		WHERE
			appId = {appId: String}`,
		{
			params: {
				appId,
				modifiedBy: userId,
				options
			},
			stringify: ['options']
		}
	)
}

async function updateParams(appId: string, userId: string, params: ParamType[]) {
	params = uniqBy(
		params.filter((param) => param.paramName !== 'userId'),
		'paramName'
	)

	return await dbGaio('updateParams').exec(
		`ALTER TABLE app
		UPDATE params = {params: String},
			modifiedBy = {modifiedBy: String}
		WHERE appId = {appId: String}`,
		{
			params: {
				appId,
				modifiedBy: userId,
				params
			},
			stringify: ['params']
		}
	)
}

async function updateForms(appId: string, userId: string, forms: FormType[]) {
	const saveFormList = uniqBy(forms, 'formId')

	return await dbGaio('updateForms').exec(
		`ALTER TABLE
			app
		UPDATE
			forms = {forms: String},
			modifiedBy = {modifiedBy: String}
		WHERE
			appId = {appId: String}`,
		{
			params: {
				appId,
				forms: saveFormList,
				modifiedBy: userId
			},
			stringify: ['forms']
		}
	)
}

export async function appShared() {
	return await dbGaio('appShared').query(
		`SELECT a.appId,
						a.appName,
						a.options,
						a.repoId,
						p.sees
		 FROM app a
						LEFT JOIN pass p ON p.appId = a.appId
		`,
		{
			parse: ['options']
		}
	)
}

export async function getAppNames(appIds: string[]): Promise<{ appId: string; appName: string }[]> {
	return await dbGaio('getAppNames').query(
		`SELECT appId, appName
		 FROM app
		 WHERE appId in ({appIds: Array(String)})`,
		{
			params: {
				appIds: appIds
			}
		}
	)
}

async function updateAiModelAtAllApps(aiManagerId: string) {
	return await dbGaio('defineAiModelAtAllApps').query(
		`ALTER TABLE app
		UPDATE options = replaceRegexpAll(
			options,
			'"aiManagerId":"[^"]*"',
				'"aiManagerId":"${aiManagerId}"'
			)
		WHERE position (options, '"aiManagerId"') > 0`
	)
}

export default {
	appShared,
	createApp,
	deleteApp,
	getAllApps,
	getAppParamsById,
	getAppsByIds,
	getAppsInTagByType,
	getAppsToTagControl,
	getSingleAppById,
	mergeApp,
	mergeAppOptions,
	updateForms,
	updateParams,
	getRepoIdAppToRestfulById,
	getOptionsOfAppId,
	updateAiModelAtAllApps,
	getAppNames,
	updateAllAppData,
	getAppIdByAppToken
}
