import AppRepository from "@/components/app/app.repository";
import FlowRepository from "@/components/flow/flow.repository";
import PassRepository from "@/components/pass/pass.repository";
import SourceRepository from "@/components/source/source.repository";
import type { AppType, SourceType, UserType } from "@gaio/shared/types";
import { getBucketNameFromAppId } from "@gaio/shared/utils";

type SourceTypeWithSourceType = {
  sourceType: string;
  shared?: boolean;
} & SourceType;

async function generateBucketSourceData(app: Partial<AppType>, user: UserType) {
  const sourceList = (await SourceRepository.getSourceByUser(
    user,
  )) as SourceTypeWithSourceType[];
  const bucketAccess = await PassRepository.getListOfAppsBucketHasAccess(
    app.appId,
  );

  const sourceBase = [
    {
      client: "clickhouse",
      databaseName: getBucketNameFromAppId(app.appId),
      repoId: app.repoId,
      sourceName: getBucketNameFromAppId(app.appId),
      sourceType: "bucket",
      shared: false,
    },
  ] as SourceTypeWithSourceType[];

  const appNames = await AppRepository.getAppNames(bucketAccess as string[]);

  for (const sharedAppId of bucketAccess as string[]) {
    const sourceName = appNames.find((app) => app.appId === sharedAppId);

    sourceBase.push({
      client: "clickhouse",
      databaseName: getBucketNameFromAppId(sharedAppId),
      repoId: app.repoId,
      sourceType: "bucket",
      shared: true,
      sourceName: sourceName?.appName || getBucketNameFromAppId(sharedAppId),
    });
  }

  return sourceBase.concat(sourceList);
}

export async function appInit(
  appId: string,
  user: UserType,
  logFrom: "studio" | "dashboard",
) {
  const app = await AppRepository.getSingleAppById(appId);
  let flowList = await FlowRepository.getFlowListByAppId(appId);
  const sourceList = await generateBucketSourceData(app, user);

  if (logFrom === "dashboard") {
    flowList = flowList.filter((o) =>
      ["smart", "infoPub"].includes(o.flowType),
    );
  }

  if (user.options.portalTabs) {
    flowList = flowList.filter(
      (flow) =>
        user.options.portalTabs.includes(flow.flowId) &&
        flow.flowType === "infoPub",
    );
  }

  return {
    app,
    flowList,
    sourceList,
  };
}
