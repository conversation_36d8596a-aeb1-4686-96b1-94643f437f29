import type { PortalType } from '@gaio/shared/types';
import UserRepository from '@/components/user/user.repository';
import TagRepository from '@/components/tag/tag.repository';
import { HTTPException } from 'hono/http-exception';

export async function listPortal(appId: string) {
	return await UserRepository.listPortalUsers(appId);
}

export async function savePortal(portalData: PortalType) {
	const user = await UserRepository.savePortalUser(portalData);

	await TagRepository.upsertUserAppPermission(
		user.userId,
		user.options.portalAppId,
		'view'
	);

	return { success: true };
}

export async function deletePortal(token: string) {
	const user = await UserRepository.deleteSingleUserPortal(token);

	if (!user) {
		throw new HTTPException(404, { message: 'User not found' });
	}

	await TagRepository.removeTag(user.userId, user.options.portalAppId, 'app');
	return { success: true };
}
