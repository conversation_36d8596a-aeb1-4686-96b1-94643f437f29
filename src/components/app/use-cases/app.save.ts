import { mkdir } from 'node:fs/promises';
import { join } from 'node:path';
import AppRepository from '@/components/app/app.repository';
import { appFilesFolderPath } from '@/components/content/use-cases/content.core';
import FlowRepository from '@/components/flow/flow.repository';
import TagRepository from '@/components/tag/tag.repository';
import type { AppSaveObject } from '@/types/http/dtos/app.dtos';
import type { AppType, UserType } from '@gaio/shared/types';
import { getId } from '@gaio/shared/utils';
import { HTTPException } from 'hono/http-exception';
import { createAppUserAtBucket } from './app.bucket';

export async function appSave(app: AppSaveObject, user: UserType) {
	try {
		return !app.appId
			? await createApp(app, user)
			: await AppRepository.mergeApp(app, user);
	} catch (err) {
		console.log(err);
		if (err instanceof HTTPException) {
			throw err;
		}

		throw new HTTPException(500, {
			cause: err,
			message: 'Internal Server Error'
		});
	}
}

export async function createApp(
	app: AppSaveObject,
	user: UserType
): Promise<AppType> {
	const firstFlowId = getId();

	const newApp = await AppRepository.createApp({
		...app,
		options: { ...app.options, studioFlowStart: firstFlowId }
	});

	await TagRepository.upsertUserAppPermission(
		user.userId,
		newApp.appId,
		'edit'
	);

	await FlowRepository.createFlow(
		{
			appId: newApp.appId,
			flowDescription: '',
			flowId: firstFlowId,
			flowName: 'My first process',
			flowType: 'dataPrep'
		},
		user.userId
	);

	const folderPath = join(
		appFilesFolderPath,
		newApp.appId.replace('app:', ''),
		'assets'
	);

	await mkdir(join(folderPath, 'inputs'), { recursive: true });
	await mkdir(join(folderPath, 'outputs'), { recursive: true });
	await mkdir(join(folderPath, 'content'), { recursive: true });

	await createAppUserAtBucket(newApp.repoId, newApp.appId, user.userId);

	return newApp;
}
