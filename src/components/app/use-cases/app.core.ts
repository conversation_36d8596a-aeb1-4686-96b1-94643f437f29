import AppRepository from '@/components/app/app.repository';
import TagRepository from '@/components/tag/tag.repository';
import type { UserType } from '@gaio/shared/types';

export async function appRef(token: string, user: UserType) {
	const appData = await AppRepository.getAppIdByAppToken(token);

	const hasPermission = await TagRepository.userHasPermission(
		user.userId,
		appData.appId,
		'app'
	);

	if (hasPermission?.refId) {
		return appData;
	}

	return {};
}
