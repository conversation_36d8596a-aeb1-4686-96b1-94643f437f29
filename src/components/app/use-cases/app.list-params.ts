import AppRepository from '@/components/app/app.repository';
import { HTTPException } from 'hono/http-exception';

export async function flowListParamsUseCase(appId: string) {
	try {
		return await AppRepository.getAppParamsById(appId);
	} catch (err) {
		if (err instanceof HTTPException) {
			throw err;
		}

		throw new HTTPException(500, {
			cause: 'internalErrorListingFlowParams',
			message: 'Internal error listing flow params'
		});
	}
}
