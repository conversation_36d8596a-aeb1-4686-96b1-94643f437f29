import { join } from 'node:path';
import AiThreadRepository from '@/components/ai-thread/ai-thread.repository';
import ApiHookRepository from '@/components/api-hook/api-hook.repository';
import AppRepository from '@/components/app/app.repository';
import CodeSnippetRepository from '@/components/code-snippet/code-snippet.repository';
import { appFilesFolderPath } from '@/components/content/use-cases/content.core';
import CronRepository from '@/components/cron/cron.repository';
import {
	BULLMQ_CRON_QUEUE,
	gaioBullMQOptions,
	removeCronJobFromRedis
} from '@/components/cron/cron.schedules.tools';
import FlowRepository from '@/components/flow/flow.repository';
import MapsRepository from '@/components/maps/maps.repository';
import MetaViewRepository from '@/components/meta-view/meta-view.repository';
import MetaRepository from '@/components/meta/meta.repository';
import PassRepository from '@/components/pass/pass.repository';
import SurveyRepository from '@/components/survey/survey.repository';
import TagRepository from '@/components/tag/tag.repository';
import TaskLogRepository from '@/components/task-log/task-log.repository';
import TimeCapsuleRepository from '@/components/time-capsule/time-capsule.repository';
import { repositoryInstance } from '@/db/connections/db.clickhouse';
import { getBucketNameFromAppId } from '@gaio/shared/utils';
import { getAppNumberReference } from '@gaio/shared/utils/libs/helpers';
import { Queue } from 'bullmq';
import { remove } from 'fs-extra';

export async function appDelete(appId: string) {
	const { repoId } = await AppRepository.getRepoIdAppToRestfulById(appId);

	await AppRepository.deleteApp(appId);

	await deleteFlowByAppId(appId);
	await removeMapAndGeoJsonFiles(appId);
	await deletePassAndAppSees(appId, repoId);
	await deleteContentFolder(appId);

	await CronRepository.deleteCron({ appId });
	await MetaRepository.deleteMetaByAppId(appId);
	await MetaViewRepository.deleteMetaViewByAppId(appId);
	await TaskLogRepository.deleteTaskLogByAppId(appId);
	await CodeSnippetRepository.deleteCodeSnippetsByAppId(appId);
	await CronRepository.deleteCron({ appId });
	await ApiHookRepository.deleteApiHooksByAppId(appId);
	await TimeCapsuleRepository.deleteTimeCapsuleByAppId(appId);
	await TagRepository.deleteTagByAppId(appId);
	await TaskLogRepository.deleteTaskLogByAppId(appId);
	await AiThreadRepository.deleteAiThreadByAppId(appId);
	await SurveyRepository.removeAllSurveyByAppId(appId);

	return { status: 'done' };
}

async function deleteContentFolder(appId: string) {
	await remove(join(appFilesFolderPath, getAppNumberReference(appId)));
}

async function removeMapAndGeoJsonFiles(appId: string) {
	const mapList = await MapsRepository.getAllMaps(appId);

	for (const map of mapList.filter((map) => map.appId === appId)) {
		await MapsRepository.deleteMapById(map.mapId, appId);
	}
}

async function deletePassAndAppSees(appId: string, repoId: string) {
	const withAccess = await PassRepository.getAppPassThatHasAppIdAccess(appId);

	for (const app of withAccess) {
		await PassRepository.saveSees(
			app.appId,
			app.repoId,
			app.sees.filter((s) => s !== appId) as string[]
		);
	}

	await PassRepository.deletePassByAppId(appId);

	await repositoryInstance(repoId).query(
		`DROP DATABASE IF EXISTS ${getBucketNameFromAppId(appId)}`
	);

	await repositoryInstance(repoId).query(
		`DROP USER IF EXISTS user_${getAppNumberReference(appId)}`
	);
}

async function deleteFlowByAppId(appId: string) {
	const flows = await FlowRepository.getFlowListByAppId(appId);
	const flowsIds = flows.map((flow) => flow.flowId);

	const flowQueue = new Queue(BULLMQ_CRON_QUEUE, gaioBullMQOptions);

	for (const flowId of flowsIds) {
		await removeCronJobFromRedis({
			appId,
			queue: flowQueue,
			refId: flowId,
			refType: 'flow'
		});
	}

	await flowQueue.close();

	await FlowRepository.deleteFlowByAppId(appId);
}
