import AppRepository from '@/components/app/app.repository';
import { userTokenWithPermissions } from '@/components/auth/auth.core';
import FlowRepository from '@/components/flow/flow.repository';
import type { AppType, UserType } from '@gaio/shared/types';

export async function appListSmartFlows(user: UserType) {
	return await FlowRepository.getSmartFlowsByUser(user.userId);
}

export async function appList(appIds: string[], user: UserType) {
	let apps = [] as AppType[];

	if (!appIds || !appIds.length) {
		apps = await AppRepository.getAllApps(user);
	} else {
		apps = await AppRepository.getAppsByIds(appIds, user);
	}

	const { accessToken: token } = await userTokenWithPermissions(user);

	return {
		apps,
		token
	};
}
