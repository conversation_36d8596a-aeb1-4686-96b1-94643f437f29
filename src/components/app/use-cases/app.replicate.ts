import { constants, access, copyFile, cp } from 'node:fs/promises';
import { join } from 'node:path';
import apiHookRepository from '@/components/api-hook/api-hook.repository';
import codeSnippetRepository from '@/components/code-snippet/code-snippet.repository';
import {
	appFilesFolderPath,
	mapAssetsFilesFolderPath
} from '@/components/content/use-cases/content.core';
import flowRepository from '@/components/flow/flow.repository';
import mapsRepository from '@/components/maps/maps.repository';
import metaViewRepository from '@/components/meta-view/meta-view.repository';
import metaRepository from '@/components/meta/meta.repository';
import passRepository from '@/components/pass/pass.repository';
import { setupSharedApps } from '@/components/setting/settings.app-share';
import { dbGaio } from '@/db/db.gaio';
import type { GenericType, MetaViewType, UserType } from '@gaio/shared/types';
import { getId, getAppNumberReference } from '@gaio/shared/utils';
import appRepository from '../app.repository';
import { createApp } from './app.save';

export async function appReplicate(appId: string, user: UserType) {
	const oldApp = await appRepository.getSingleAppById(appId);
	const newApp = await createApp(
		{
			...oldApp,
			appName: `Copy - ${oldApp.appName}`,
			appId: null
		},
		user
	);

	if (newApp.appId) {
		await appRepository.updateAllAppData(newApp.appId, oldApp, user);
		await createNewMetas(newApp.appId, oldApp.appId, user);

		const newCodeSnippetIds = await createNewCodeSnippets(
			newApp.appId,
			oldApp.appId,
			user
		);

		const newMapIds = await createNewMaps(newApp.appId, oldApp.appId, user);

		await createNewFlows(
			newApp.appId,
			oldApp.appId,
			user,
			newCodeSnippetIds,
			newMapIds
		);

		await createNewApiAndApiKeys(newApp.appId, oldApp.appId, user);
		await createNewCronJobs(newApp.appId, oldApp.appId, user);
		await createAppShare(newApp.appId, oldApp.appId, oldApp.repoId, user);
		await duplicateFiles(newApp.appId, oldApp.appId);
	}

	return {};
}

async function createNewFlows(
	newAppId: string,
	oldAppId: string,
	user: UserType,
	newCodeSnippetIds: Map<string, string>,
	newMapIds: Map<string, string>
) {
	await flowRepository.deleteFlowByAppId(newAppId);
	const flowList = await flowRepository.getFlowListByAppId(oldAppId);

	for (const flow of flowList) {
		for (const task of flow.workflow.nodes) {
			if (task.codeSnippetId) {
				task.codeSnippetId = newCodeSnippetIds.get(task.codeSnippetId);
			}

			if (task.reportMap?.id && newMapIds.has(task.reportMap.id)) {
				task.reportMap.id = newMapIds.get(task.reportMap.id);
			}
		}

		const workflow = JSON.stringify(flow.workflow).replaceAll(
			`bucket_${getAppNumberReference(oldAppId)}`,
			`bucket_${getAppNumberReference(newAppId)}`
		);

		const newWorkflow = workflow.replaceAll(oldAppId, newAppId);

		await flowRepository.createFlow(
			{
				...flow,
				appId: newAppId,
				workflow: JSON.parse(newWorkflow)
			},
			user.userId
		);
	}
}

async function createNewMetas(
	newAppId: string,
	oldAppId: string,
	user: UserType
) {
	// Metas
	await metaRepository.deleteMetaByAppId(newAppId);
	const metaList = await metaRepository.getAllMetaByAppId(oldAppId);

	for (const meta of metaList) {
		const newMetaId = getId();
		const oldMetaId = meta.metaId;

		meta.fields = meta.fields
			? JSON.parse(
					JSON.stringify(meta.fields).replaceAll(
						`bucket_${getAppNumberReference(oldAppId)}`,
						`bucket_${getAppNumberReference(newAppId)}`
					)
				)
			: [];

		await metaRepository.insertMeta(
			{
				...meta,
				metaId: newMetaId,
				appId: newAppId
			},
			user.userId
		);

		// Meta Views
		await metaViewRepository.deleteMetaViewByAppId(newAppId);
		const metaViewList = await metaViewRepository.getMetaViewsByUserAndMetaId(
			user.userId,
			oldMetaId
		);

		for (const metaView of metaViewList) {
			await metaViewRepository.createMetaView(
				{
					...metaView,
					appId: newAppId,
					metaId: newMetaId
				} as MetaViewType,
				user
			);
		}
	}
}

async function createNewCodeSnippets(
	newAppId: string,
	oldAppId: string,
	user: UserType
) {
	await codeSnippetRepository.deleteCodeSnippetsByAppId(newAppId);
	const codeSnippetList =
		await codeSnippetRepository.getAllCodeSnippetsByAppId(oldAppId);

	const newCodeSnippetIds = new Map();

	for (const codeSnippet of codeSnippetList) {
		const codeSnippetId = codeSnippet.codeSnippetId?.startsWith('env_')
			? `env_${getAppNumberReference(newAppId)}`
			: getId();

		newCodeSnippetIds.set(codeSnippet.codeSnippetId, codeSnippetId);

		await codeSnippetRepository.createCodeSnippet({
			...codeSnippet,
			codeSnippetId,
			options: codeSnippetId.startsWith('env_')
				? {
						pythonVersion: '3.10.15'
					}
				: (codeSnippet.options as unknown as GenericType) || {},
			appId: newAppId,
			userId: user.userId
		});
	}

	return newCodeSnippetIds;
}

async function createNewApiAndApiKeys(
	newAppId: string,
	oldAppId: string,
	user: UserType
) {
	const apiHooks = await apiHookRepository.getAllAppHooks(oldAppId);

	const preserveNewApiKeys = new Map();

	for (const apiHook of apiHooks.apiKeyList) {
		const apiKeyId = getId();

		preserveNewApiKeys.set(apiHook.apiKeyId, apiKeyId);

		await apiHookRepository.saveApiKey(
			{
				...apiHook,
				appId: newAppId,
				apiKeyId
			},
			true,
			user.userId
		);
	}

	for (const api of apiHooks.apiList) {
		api.assignedKeys = (api.assignedKeys || []).map((key) =>
			preserveNewApiKeys.get(key)
		);

		await apiHookRepository.saveApi(
			{
				...api,
				apiId: getId(),
				appId: newAppId
			},
			user.userId
		);
	}
}

async function createNewCronJobs(
	newAppId: string,
	oldAppId: string,
	user: UserType
) {
	// those are created
	await dbGaio().exec(
		'ALTER TABLE cron DELETE WHERE appId = {newAppId: String}',
		{
			params: { newAppId }
		}
	);
	await dbGaio().exec(
		`INSERT INTO 
			cron
				(
					appId, 
					refId, 
					refType, 
					cron, 
					cronStatus, 
					cronBase, 
					createdBy, 
					createdAt, 
					updatedBy, 
					updatedAt
				)
				SELECT 
					'${newAppId}' AS appId, 
					refId, 
					refType,
					cron, 
					'inactive' AS cronStatus, 
					cronBase, 
					'${user.userId}' AS createdBy, 
					now() AS createdAt, 
					'${user.userId}' AS updatedBy, 
					now() AS updatedAt 
				FROM cron 
				WHERE appId = {oldAppId: String}`,
		{
			params: { oldAppId }
		}
	);
}

async function createNewMaps(
	newAppId: string,
	oldAppId: string,
	user: UserType
) {
	const mapList = await mapsRepository.getAllMaps(oldAppId);

	const newMapIds = new Map();

	for (const map of mapList.filter((map) => map.appId === oldAppId)) {
		const newMapId = getId();

		newMapIds.set(map.mapId, newMapId);

		map.options.title = `Copy - ${map.options.title}`;

		await mapsRepository.upsertMapsMetadata({
			appId: newAppId,
			mapId: newMapId,
			userId: user.userId,
			shared: false,
			options: map.options
		});

		const folderPath = mapAssetsFilesFolderPath('');
		const oldMapFileName = `${map.mapId}.json`;

		const oldMapFilePath = join(folderPath, oldMapFileName);
		const newMapFilePath = join(folderPath, `${newMapId}.json`);

		try {
			await access(oldMapFilePath, constants.F_OK);
			await copyFile(oldMapFilePath, newMapFilePath);
		} catch {
			console.warn(`Map file ${oldMapFilePath} not found, skipping...`);
		}
	}

	return newMapIds;
}

async function createAppShare(
	newAppId: string,
	oldAppId: string,
	repoId: string,
	user: UserType
) {
	const sees = await passRepository.getListOfAppsBucketHasAccess(oldAppId);

	if (sees.length > 0) {
		setupSharedApps(
			{
				appId: newAppId,
				repoId,
				sees,
				revokeList: []
			},
			user
		);
	}
}

async function duplicateFiles(newAppId: string, oldAppId: string) {
	const oldAppFilesFolder = join(
		appFilesFolderPath,
		getAppNumberReference(oldAppId)
	);
	const newAppFilesFolder = join(
		appFilesFolderPath,
		getAppNumberReference(newAppId)
	);

	try {
		await cp(oldAppFilesFolder, newAppFilesFolder, { recursive: true });
	} catch (error) {
		console.warn(
			`Failed to duplicate app files from ${oldAppFilesFolder}:`,
			error.message
		);
	}
}
