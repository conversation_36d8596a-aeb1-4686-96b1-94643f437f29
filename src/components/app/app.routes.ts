import { appRef } from "@/components/app/use-cases/app.core";
import { appDelete } from "@/components/app/use-cases/app.delete";
import { appInit } from "@/components/app/use-cases/app.init";
import {
  appList,
  appListSmartFlows,
} from "@/components/app/use-cases/app.list";
import { flowListParamsUseCase } from "@/components/app/use-cases/app.list-params";
import { appSave } from "@/components/app/use-cases/app.save";
import { updateAppForms } from "@/components/app/use-cases/app.update-forms";
import { updateAppOptions } from "@/components/app/use-cases/app.update-options";
import { updateAppParams } from "@/components/app/use-cases/app.update-params";
import jwtGuard from "@/server/middleware/jwt.guard";
import { readBody } from "@/server/middleware/readBody";
import { readValidatedBody } from "@/server/middleware/readValidatedBody";
import { appSaveRequestSchema } from "@/types/http/dtos/app.dtos";
import type {
  AppOptionsType,
  FormType,
  ParamType,
  PortalType,
} from "@gaio/shared/types";
import { type Context, Hono } from "hono";
import { addAppToUserIfUserHasNoApps } from "../user/use-cases/user.prepare";
import { appReplicate } from "./use-cases/app.replicate";
import { deletePortal, listPortal, savePortal } from "./use-cases/app.portal";

const app = new Hono()
  .post("/init", jwtGuard("user").isAuth, async (c: Context) => {
    const { appId, user } = await readBody<{ appId: string }>(c);
    const logFrom = c.get("logFrom");
    return c.json(await appInit(appId, user, logFrom));
  })

  .post("/replicate", jwtGuard("user").isAuth, async (c) => {
    const { appId, user } = await readBody<{ appId: string }>(c);
    return c.json(await appReplicate(appId, user));
  })

  .post("/save", jwtGuard("dev").isAuth, async (c) => {
    const { app, user } = await readValidatedBody(c, appSaveRequestSchema);

    return c.json(await appSave(app, user));
  })

  .get("/refer/:appId", jwtGuard("user").isAuth, async (c: Context) => {
    const user = c.get("user");
    const appId = c.req.param("appId");
    return c.json(await appRef(appId, user));
  })

  .post("/list", jwtGuard("user").isAuth, async (c) => {
    const { appIds, user } = await readBody<{ appIds: string[] }>(c);
    await addAppToUserIfUserHasNoApps(user);
    return c.json(await appList(appIds, user));
  })

  .get("/list-smart-flows", jwtGuard("user").isAuth, async (c: Context) => {
    const user = c.get("user");
    return c.json(await appListSmartFlows(user));
  })

  .post("/update-options", jwtGuard("dev").isAuth, async (c) => {
    const { appId, options, user } = await readBody<{
      appId: string;
      options: AppOptionsType;
    }>(c);
    return c.json(await updateAppOptions(appId, options, user));
  })

  .post("/list-params-from-app", jwtGuard("user").isAuth, async (c) => {
    const { appId } = await readBody<{ appId: string }>(c);

    return c.json(await flowListParamsUseCase(appId));
  })

  .post("/update-params", jwtGuard("dev").isAuth, async (c) => {
    const { appId, params, user } = await readBody<{
      appId: string;
      params: Array<ParamType>;
    }>(c);
    return c.json(await updateAppParams(appId, params, user));
  })

  .post("/update-forms", jwtGuard("dev").isAuth, async (c) => {
    const { appId, forms, user } = await readBody<{
      appId: string;
      forms: FormType[];
    }>(c);
    return c.json(await updateAppForms(appId, forms, user));
  })

  .post("/delete", jwtGuard("dev").isAuth, async (c) => {
    const { appId } = await readBody<{ appId: string }>(c);
    return c.json(await appDelete(appId));
  })

  .get("/portal/:appId", jwtGuard("user").isAuth, async (c) => {
    const appId = c.req.param("appId");
    return c.json(await listPortal(appId));
  })

  .post("/portal/save", jwtGuard("user").isAuth, async (c) => {
    const { portalData } = await readBody<{
      portalData: PortalType;
    }>(c);
    return c.json(await savePortal(portalData));
  })

  .post("/portal/delete", jwtGuard("user").isAuth, async (c) => {
    const { token } = await readBody<{ token: string }>(c);
    return c.json(await deletePortal(token));
  });

export default app;
