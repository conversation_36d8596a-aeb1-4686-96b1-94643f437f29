import { contentDeletefile } from '@/components/content/use-cases/content.delete-file';
import { contentGetCsvInfo } from '@/components/content/use-cases/content.get-csv-info';
import { getContentDirectoryTree } from '@/components/content/use-cases/content.get-tree';
import { contentMoveFile } from '@/components/content/use-cases/content.move-file';
import { contentReadFile } from '@/components/content/use-cases/content.read-file';
import { contentUploadFile } from '@/components/content/use-cases/content.upload-file';
import { contentUploadProfilePic } from '@/components/content/use-cases/content.upload-profile-pic';
import accessGuard from '@/server/middleware/access.guard';
import jwtGuard from '@/server/middleware/jwt.guard';
import { Hono } from 'hono';
import { HTTPException } from 'hono/http-exception';

const app = new Hono()
	.get('/read/:appId/:path{.+}', jwtGuard('user').isAuth, async (c) => {
		const { appId, path } = c.req.param();

		try {
			const stream = await contentReadFile({ appId, path });
			c.header('X-Frame-Options', 'SAMEORIGIN');
			c.header('Content-Security-Policy', 'frame-ancestors *');
			
			return c.body(stream, { status: 200 });
		} catch (err) {
			if (err instanceof HTTPException) {
				return c.json(
					{
						details: err.message,
						message: err.cause,
						timestamp: new Date().toISOString()
					},
					err.status
				);
			}

			return c.json(
				{
					details: 'Internal error reading file',
					message: 'internalErrorReadingFile',
					timestamp: new Date().toISOString()
				},
				{ status: 500 }
			);
		}
	})

	.get(
		'/tree/:appId',
		jwtGuard('user').isAuth,
		accessGuard({ checkLevel: 'soft', checkType: 'path' }).hasAppsAccess,
		async (c) => {
			const { appId } = c.req.param();

			try {
				const dirTree = await getContentDirectoryTree(appId);

				return c.json(
					{
						data: dirTree,
						message: 'ok',
						timestamp: new Date().toISOString()
					},
					{ status: 200 }
				);
			} catch {
				return c.json(
					{
						message: 'internalErrorReadingFile',
						timestamp: new Date().toISOString()
					},
					{ status: 500 }
				);
			}
		}
	)

	.get('/get-csv-info/:appId/:path{.+}', jwtGuard('user').isAuth, async (c) => {
		const { appId, path } = c.req.param();
		const { page, pageSize } = c.req.query();

		try {
			const pageToFetch = Number.isNaN(Number(page)) ? 1 : Number(page);
			const itemsToFetch = Number.isNaN(Number(pageSize))
				? 100
				: Number(pageSize);

			const data = await contentGetCsvInfo({
				appId: appId,
				page: pageToFetch,
				pageSize: itemsToFetch,
				path
			});

			return c.json(
				{
					data,
					message: 'fileInformationRetrieved',
					timestamp: new Date().toISOString()
				},
				{ status: 200 }
			);
		} catch (err) {
			if (err instanceof HTTPException) {
				return c.json(
					{
						details: err.message,
						message: err.cause,
						timestamp: new Date().toISOString()
					},
					err.status
				);
			}

			console.log('err', err);

			return c.json(
				{
					details: 'Internal error reading file',
					message: 'internalErrorReadingFile',
					timestamp: new Date().toISOString()
				},
				{ status: 500 }
			);
		}
	})

	.post(
		'/upload/:appId/:path{.+}',
		jwtGuard('user').isAuth,
		accessGuard({ checkLevel: 'soft', checkType: 'path' }).hasAppsAccess,
		async (c) => {
			const { appId, path } = c.req.param();

			try {
				const body = await c.req.parseBody();

				const res = await contentUploadFile({ appId, body, path });

				return c.json({
					message: res.message,
					timestamp: new Date().toISOString()
				});
			} catch (err) {
				console.log(err);
				if (err instanceof HTTPException) {
					return c.json(
						{
							details: err.message,
							message: err.cause,
							timestamp: new Date().toISOString()
						},
						err.status
					);
				}

				return c.json(
					{
						message: 'internalErrorUploadingFile',
						timestamp: new Date().toISOString()
					},
					{ status: 500 }
				);
			}
		}
	)

	.patch(
		'/move/:appId',
		jwtGuard('user').isAuth,
		accessGuard({ checkLevel: 'soft', checkType: 'path' }).hasAppsAccess,
		async (c) => {
			const { appId } = c.req.param();

			try {
				const { oldPath, newPath } = await c.req.json<{
					oldPath: string;
					newPath: string;
				}>();

				const res = await contentMoveFile({ appId, newPath, oldPath });

				return c.json(
					{
						message: res.message,
						timestamp: new Date().toISOString()
					},
					{ status: 200 }
				);
			} catch (err) {
				if (err instanceof HTTPException) {
					return c.json(
						{
							details: err.message,
							message: err.cause,
							timestamp: new Date().toISOString()
						},
						err.status
					);
				}

				return c.json(
					{
						details: 'Internal error moving file',
						message: 'internalErrorMovingFile',
						timestamp: new Date().toISOString()
					},
					{ status: 500 }
				);
			}
		}
	)

	.delete(
		'/delete/:appId/:path{.+}',
		jwtGuard('user').isAuth,
		accessGuard({ checkLevel: 'soft', checkType: 'path' }).hasAppsAccess,
		async (c) => {
			const { appId, path } = c.req.param();

			const res = await contentDeletefile({ appId, path });

			return c.json({
				message: res.message,
				timestamp: new Date().toISOString()
			});
		}
	)

	.post('/:userId/upload-user-pic', jwtGuard('user').isAuth, async (c) => {
		const { userId } = c.req.param();

		try {
			const path = c.req.path;
			const url = c.req.url;

			const baseUrl = url.split(path)[0];

			const body = await c.req.parseBody();

			const res = await contentUploadProfilePic({ baseUrl, body, userId });

			return c.json({
				output: res,
				timestamp: new Date().toISOString()
			});
		} catch (err) {
			console.log('err', err);
			if (err instanceof HTTPException) {
				return c.json(
					{
						details: err.message,
						message: err.cause,
						timestamp: new Date().toISOString()
					},
					err.status
				);
			}

			return c.json(
				{
					message: 'internalErrorUploadingFile',
					timestamp: new Date().toISOString()
				},
				{ status: 500 }
			);
		}
	});

export default app;
