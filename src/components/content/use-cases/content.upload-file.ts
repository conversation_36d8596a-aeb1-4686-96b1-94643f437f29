import { mkdir } from 'node:fs/promises';
import { join } from 'node:path';
import { HTTPException } from 'hono/http-exception';
import { assetsFolderPath } from './content.core';

export async function contentUploadFile({
	body,
	appId,
	path
}: {
	body: { [x: string]: string | File };
	appId: string;
	path: string;
}) {
	const file = body.file;

	if (!file) {
		const folderPath = join(assetsFolderPath(appId), path);
		await mkdir(folderPath, { recursive: true });

		return { message: 'directoryCreated' };
	}

	if (!(file instanceof File)) {
		throw new HTTPException(400, {
			cause: 'invalidFile',
			message: 'Invalid File'
		});
	}

	const buffer = await file.arrayBuffer();

	const preFileExtension = file.name.split('.');
	const fileExtension = preFileExtension.pop();

	if (
		![
			'csv',
			'txt',
			'tsv',
			'xls',
			'xlsx',
			'jpg',
			'jpeg',
			'png',
			'webp',
			'md',
			'json',
			'html'
		].includes(fileExtension)
	) {
		throw new HTTPException(400, {
			cause: 'invalidFileType',
			message: 'Invalid File Type'
		});
	}

	await Bun.write(join(assetsFolderPath(appId), path), Buffer.from(buffer));

	return { message: 'fileUploaded' };
}
