import { join } from 'node:path';
import { contentFolder } from '@/components/task/use-cases/runners/runner.tools';

export const userPicsFolderPath = join(contentFolder, 'user');
export const appFilesFolderPath = join(contentFolder, 'apps');
export const mapFilesFolderPath = join(contentFolder, 'maps');

export const assetsFolderPath = (appId: string) => {
	return join(appFilesFolderPath, appId, 'assets');
};

export const mapAssetsFilesFolderPath = (basePath = '') => {
	return join(mapFilesFolderPath, basePath);
};

export const userPicExtension = '.png';
export const userPicUrl = (baseUrl: string, userId: string) =>
	`${baseUrl}/api/content/user/${userId}`;
