import directoryTree, { type DirectoryTree } from 'directory-tree';
import { assetsFolderPath } from './content.core';
import { ensureDir } from 'fs-extra'

const normalizePath = (
	obj: DirectoryTree<Record<string, unknown>>,
	modifyFn: (appId: string, path: string) => string,
	appId: string
) => {
	if (obj && typeof obj === 'object') {
		if (typeof obj.path === 'string') {
			obj.path = modifyFn(appId, obj.path);
		}

		if (Array.isArray(obj.children)) {
			obj.children.forEach((child) => normalizePath(child, modifyFn, appId));
		}
	}
};

const removeBasePath = (appId: string, path: string) => {
	const updatedPath = path.replace(assetsFolderPath(appId), '');
	return updatedPath.length === 0 ? '/' : updatedPath;
};

export async function getContentDirectoryTree(appId: string) {
	await ensureDir(assetsFolderPath(appId))
	const dirTree = directoryTree(assetsFolderPath(appId), {
		attributes: ['ctime', 'mtime', 'size'],
		normalizePath: true
	});

	normalizePath(dirTree, removeBasePath, appId);

	return dirTree;
}
