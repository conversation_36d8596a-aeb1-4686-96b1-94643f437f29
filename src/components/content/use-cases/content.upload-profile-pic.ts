import { unlink } from 'node:fs/promises';
import { join, parse } from 'node:path';
import UserRepository from '@/components/user/user.repository';
import { dbGaio } from '@/db/db.gaio';
import { currentDatetime } from '@/utils/helpers';
import { HTTPException } from 'hono/http-exception';
import {
	userPicExtension,
	userPicUrl,
	userPicsFolderPath
} from './content.core';

export async function contentUploadProfilePic({
	body,
	userId,
	baseUrl
}: {
	body: { [x: string]: string | File };
	userId: string;
	baseUrl: string;
}) {
	const file = body['file'];

	if (!(file instanceof File)) {
		throw new HTTPException(400, {
			cause: 'invalidFile',
			message: 'Invalid File'
		});
	}
	const buffer = await file.arrayBuffer();

	if (!['image/jpeg', 'image/png', 'image/webp'].includes(file.type)) {
		throw new HTTPException(400, {
			cause: 'invalidFileType',
			message: 'Invalid File Type'
		});
	}

	const maxSizeMB = 5;
	const maxSizeBytes = maxSizeMB * 1024 * 1024;

	if (file.size > maxSizeBytes) {
		throw new HTTPException(400, {
			cause: 'fileSizeTooBig',
			message: 'File size too big'
		});
	}

	//! WORKAROUND for file extension - file.type is not reliable
	//? Use "sharp" dependency for file conversion?
	// const fileExtension = file.type.split('/')[1]
	const fileExtension = 'png';

	//* Delete user OLD pics
	const oldPicsPaths = [
		join(userPicsFolderPath, `${userId}.jpeg`),
		join(userPicsFolderPath, `${userId}.png`),
		join(userPicsFolderPath, `${userId}.webp`)
	];

	for await (const path of oldPicsPaths) {
		const pic = Bun.file(path);
		const doesPicExists = await pic.exists();

		if (doesPicExists && parse(path).ext !== fileExtension) {
			await unlink(path).catch(() => {});
		}
	}

	await Bun.write(
		join(userPicsFolderPath, `${userId}.${fileExtension}`),
		Buffer.from(buffer)
	);

	const user = await UserRepository.getUserById(userId);
	const userOptions = {
		...user.options,
		profilePic: `${userId}.${fileExtension}`
	};
	await dbGaio().exec(
		`
			ALTER TABLE
				user
			UPDATE
				options = {options: String},
				modifiedBy = {modifiedBy: String},
				updatedAt = {updatedAt: String}
			WHERE
				userId = {userId: String}
		`,
		{
			params: {
				modifiedBy: userId,
				options: JSON.stringify(userOptions),
				updatedAt: currentDatetime().replace('T', ' ').replace('Z', ''),
				userId: userId
			}
		}
	);

	return {
		message: 'fileUploaded',
		profilePic: `${userPicUrl(baseUrl, userId)}${userPicExtension}`
	};
}
