import { join } from 'node:path';
import type { FieldType } from '@gaio/shared/types';
import { getAppNumberReference } from '@gaio/shared/utils/libs/helpers';
import { $ } from 'bun';
import { HTTPException } from 'hono/http-exception';
import { assetsFolderPath } from './content.core';

function detectDelimiter(input: string): string {
	const delimiters = [
		',',
		';',
		'\t',
		'|',
		'||',
		'@|',
		'::',
		':',
		'/',
		'\\',
		'~',
		'^'
	];

	const delimiterCounts: { [key: string]: number } = {};

	for (const delimiter of delimiters) {
		delimiterCounts[delimiter] = input.split(delimiter).length - 1;
	}

	let bestDelimiter = '';
	let maxCount = 0;

	for (const [delimiter, count] of Object.entries(delimiterCounts)) {
		if (count > maxCount) {
			bestDelimiter = delimiter;
			maxCount = count;
		}
	}

	return maxCount > 0 ? bestDelimiter : ',';
}

export async function contentGetCsvInfo({
	appId,
	path,
	page,
	pageSize
}: {
	appId: string;
	path: string;
	page: number;
	pageSize: number;
}) {
	const _debugStart = Date.now();
	const pathChunks = path.split('/');
	const filePath = join(
		assetsFolderPath(getAppNumberReference(appId)),
		...pathChunks
	);

	const fileNameExtension = pathChunks.pop().split('.').pop();

	if (!['csv', 'tsv', 'txt'].includes(fileNameExtension.toLowerCase())) {
		throw new HTTPException(400, {
			cause: 'invalidFileType',
			message: 'Invalid file type'
		});
	}

	const csvFile = Bun.file(filePath);
	const fileExists = await csvFile.exists();

	if (!fileExists) {
		throw new HTTPException(404, {
			cause: 'fileNotFound',
			message: 'File not found'
		});
	}

	// TODO: Should I limit the pageSize?

	const csvTotalLinesCount =
		Number((await $`wc -l ${filePath}`.text()).trim().split(' ')[0]) - 1;

	const csvFirstLine =
		await $`mlr --csvlite --allow-ragged-csv-input head -n 1 ${filePath}`.text();
	const delimiter = detectDelimiter(csvFirstLine);

	const totalPageCount = Math.ceil(csvTotalLinesCount / pageSize);
	const offset = (page - 1) * pageSize;

	let batch = [];
	let fields = [];

	if (page <= totalPageCount) {
		batch =
			await $`mlr -S --fs '${delimiter}' --icsv --ojson --allow-ragged-csv-input filter 'NR > ${offset.toString()}' then head -n ${pageSize.toString()} then clean-whitespace ${filePath}`.json();
		fields =
			await $`mlr --fs "${delimiter}" --icsv --ojson --allow-ragged-csv-input head -n 500 then summary -a field_type ${filePath}`.json();
	}

	let countColumns = 1;
	fields = fields.map((field: { field_name: string; field_type: string }) => {
		const finalizeField: Partial<FieldType> = {};

		finalizeField.columnName = field.field_name
			? field.field_name.trim()
			: `column_${countColumns}`;

		if (field.field_type.includes('float')) {
			finalizeField.dataType = 'Nullable(Float64)';
			finalizeField.columnLength = 2;
		} else if (field.field_type === 'string') {
			finalizeField.dataType = 'Nullable(String)';
		} else if (field.field_type === 'int') {
			finalizeField.dataType = 'Nullable(Int64)';
		} else if (field.field_type === 'float') {
			finalizeField.dataType = 'Nullable(Float64)';
			finalizeField.columnLength = 2;
		} else {
			finalizeField.dataType = 'Nullable(String)';
		}

		countColumns++;

		return finalizeField;
	});

	const _debugEnd = Date.now();
	return {
		batch,
		currentPage: page,
		meta: {
			delimiter,
			fields
		},
		pageSize,
		totalCount: csvTotalLinesCount,
		totalPageCount
	};
}
