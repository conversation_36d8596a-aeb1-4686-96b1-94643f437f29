import { join } from 'node:path';
import { HTTPException } from 'hono/http-exception';
import { assetsFolderPath } from './content.core';

export async function contentReadFile({
	path,
	appId
}: { path: string; appId: string }) {
	const pathChunks = path.split('/');
	const contentPath = join(assetsFolderPath(appId), ...pathChunks);

	const content = Bun.file(contentPath);
	const doesContentExists = await content.exists();

	if (!doesContentExists) {
		throw new HTTPException(404, {
			cause: 'fileNotFound',
			message: 'File not found'
		});
	}

	return content.stream();
}
