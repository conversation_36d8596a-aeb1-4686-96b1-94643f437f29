import { existsSync } from 'node:fs';
import { rename, unlink } from 'node:fs/promises';
import { join } from 'node:path';
import { HTTPException } from 'hono/http-exception';
import { assetsFolderPath } from './content.core';

export async function contentMoveFile({
	appId,
	oldPath,
	newPath
}: {
	appId: string;
	oldPath: string;
	newPath: string;
}) {
	const fullOldPath = join(assetsFolderPath(appId), oldPath);
	const fullNewPath = join(assetsFolderPath(appId), newPath);

	const oldContent = Bun.file(fullOldPath);
	const newContent = Bun.file(fullNewPath);

	const doesOldContentExists = await oldContent.exists();

	if (!doesOldContentExists || !existsSync(fullOldPath)) {
		throw new HTTPException(404, {
			cause: 'fileNotFound',
			message: 'File not found'
		});
	}

	const oldContentName = oldContent.name.split('/').pop();
	const newContentName = newContent.name.split('/').pop();

	if (oldContentName !== newContentName) {
		await rename(fullOldPath, fullNewPath);

		return {
			message: 'fileRenamedSuccessfully'
		};
	}

	await Bun.write(fullNewPath, Buffer.from(await oldContent.arrayBuffer()));

	await unlink(fullOldPath);

	return {
		message: 'fileMovedSuccessfully'
	};
}
