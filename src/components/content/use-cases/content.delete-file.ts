import { lstat, rmdir, unlink } from 'node:fs/promises';
import { join } from 'node:path';
import { HTTPException } from 'hono/http-exception';
import { assetsFolderPath } from './content.core';

export async function contentDeletefile({
	path,
	appId
}: { path: string; appId: string }) {
	const pathChunks = path.split('/');
	const contentPath = join(assetsFolderPath(appId), ...pathChunks);

	try {
		const fileStat = await lstat(contentPath).catch(() => {
			throw new HTTPException(404, {
				cause: 'fileOrFolderNotFound',
				message: 'File or Folder not found'
			});
		});

		if (fileStat.isDirectory()) {
			await rmdir(contentPath, { recursive: true });

			return { message: 'folderDeletedSuccessfully' };
		}

		await unlink(contentPath);

		return { message: 'fileDeletedSuccessfully' };
	} catch (err) {
		if (err instanceof HTTPException) {
			throw err;
		}

		throw new HTTPException(500, {
			cause: err,
			message: 'internalErrorDeletingFile'
		});
	}
}
