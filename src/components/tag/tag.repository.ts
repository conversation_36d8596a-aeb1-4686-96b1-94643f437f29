import { dbGaio } from '@/db/db.gaio';
import type { TagTypePermission, UserType } from '@gaio/shared/types';

async function changeTagRoleByUser(
	tagData: TagTypePermission,
	userContext: UserType
) {
	return await dbGaio('changeTagRoleByUser').exec(
		`ALTER TABLE tag
		UPDATE role = {role : String},
			modifiedBy = {modifiedBy: String},
			updatedAt = now()
		WHERE userId = {userId: String}
			AND refId = {refId: String}
			AND type = {type : String}
		`,
		{
			params: {
				modifiedBy: userContext.userId,
				refId: tagData.refId,
				role: tagData.role,
				type: tagData.type,
				userId: tagData.userId
			}
		}
	);
}

async function userHasPermission(
	userId: string,
	refId: string,
	type: string
): Promise<TagTypePermission> {
	return await dbGaio('userHasPermission')
		.query<TagTypePermission>(
			`SELECT refId, role FROM tag 
					WHERE userId = {userId: String} 
						AND refId = {refId: String} 
						AND type = {type: String}
					LIMIT 1`,
			{
				params: {
					refId,
					type,
					userId
				}
			}
		)
		.then((res) => res[0]);
}

async function upsertUserAppPermission(
	userId: string,
	appId: string,
	role = 'view'
) {
	const hasPermission = await userHasPermission(userId, appId, 'app');
	
	if (hasPermission?.refId) {
		return await dbGaio('updateTagRole').exec(
			`ALTER TABLE tag
				UPDATE role = {role : String},
				modifiedBy = {modifiedBy: String}
			WHERE refId = {refId: String} AND type = 'app' AND userId = {userId: String}`,
			{
				params: {
					modifiedBy: userId,
					role,
					refId: hasPermission.refId,
					userId
				}
			}
		);
	}

	return await dbGaio('insertTagPermission').insert('tag', [
		{
			createdBy: userId,
			modifiedBy: userId,
			refId: appId,
			role,
			type: 'app',
			userId
		}
	]);
}

async function insertTagPermission(tagData: TagTypePermission[]) {
	await dbGaio('insertTagPermission').insert(
		'tag',
		tagData.map((tag) => ({
			createdBy: tag.userId,
			modifiedBy: tag.userId,
			refId: tag.refId,
			role: tag.role || 'view',
			type: tag.type,
			userId: tag.userId
		}))
	);
	return {};
}

async function removeTag(userId: string, refId: string, type: string) {
	return await dbGaio('removeTag').exec(
		`ALTER TABLE tag
		DELETE
		WHERE userId = {userId: String}
			AND refId = {refId: String}
			AND type = {type : String}`,
		{
			params: {
				refId,
				type,
				userId
			}
		}
	);
}

async function deleteTagByAppId(appId: string) {
	return await dbGaio('deleteTagByAppId').exec(
		'DELETE FROM tag WHERE refId = {appId: String}',
		{
			params: {
				appId
			}
		}
	);
}

async function deleteTagByUserId(userId: string) {
	return await dbGaio('deleteTagByUserId').exec(
		'DELETE FROM tag WHERE userId = {userId: String}',
		{
			params: {
				userId
			}
		}
	);
}

export default {
	changeTagRoleByUser,
	deleteTagByAppId,
	insertTagPermission,
	removeTag,
	upsertUserAppPermission,
	deleteTagByUserId,
	userHasPermission
};
