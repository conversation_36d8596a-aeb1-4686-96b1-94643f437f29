import { dbGaio } from '@/db/db.gaio';
import { currentDatetime } from '@/utils/helpers';
import type { ApiKeyType, ApiType } from '@gaio/shared/types';
import { getId } from '@gaio/shared/utils';
import { createId } from '@paralleldrive/cuid2';
import { HTTPException } from 'hono/http-exception';

async function getAllAppHooks(appId: string) {
	const apiList = await dbGaio().query<ApiType>(
		`SELECT * FROM api
						WHERE appId = {appId: String}`,
		{
			params: {
				appId
			},
			parse: ['options', 'assignedKeys']
		}
	);

	const apiKeyList = await dbGaio().query<ApiKeyType>(
		`SELECT * FROM api_key
						WHERE appId = {appId: String}`,
		{
			params: {
				appId
			}
		}
	);

	if (
		!apiList ||
		!Array.isArray(apiList) ||
		!apiKeyList ||
		!Array.isArray(apiKeyList)
	) {
		throw new Error('Query failed or returned unexpected result');
	}

	return {
		apiList,
		apiKeyList: apiKeyList.map((o) => {
			delete o.secret;
			return o;
		})
	};
}

async function saveApi(apiData: ApiType, userId: string) {
	try {
		apiData.apiId = apiData.apiId ?? getId(12);

		await dbGaio().updateOrInsert({
			primaryKeys: ['apiId'],
			table: 'api',
			values: {
				...apiData,
				apiId: apiData.apiId,
				createdBy: userId,
				updatedBy: userId,
				updatedAt: currentDatetime().replace('T', ' ').replace('Z', '')
			}
		});
		return apiData;
	} catch (err) {
		console.log(err);
		throw err;
	}
}

async function saveApiKey(
	apiKeyData: ApiKeyType,
	regenerate: boolean,
	userId: string
) {
	try {
		// make sure user is not defining a custom secret
		delete apiKeyData.secret;

		// secret is always generated for new or when requested
		if (regenerate || !apiKeyData.apiKeyId) {
			apiKeyData.secret = createId();
		}

		apiKeyData.apiKeyId = apiKeyData.apiKeyId ?? getId(12);

		await dbGaio().updateOrInsert({
			primaryKeys: ['apiKeyId'],
			table: 'api_key',
			values: {
				...apiKeyData,
				apiKeyId: apiKeyData.apiKeyId,
				createdBy: userId,
				updatedBy: userId,
				updatedAt: currentDatetime().replace('T', ' ').replace('Z', '')
			}
		});

		return apiKeyData;
	} catch (err) {
		console.log(err);
		throw err;
	}
}

async function deleteApi(apiId: string) {
	try {
		return await dbGaio().exec(
			'ALTER TABLE api DELETE WHERE apiId = {apiId: String}',
			{
				params: {
					apiId
				}
			}
		);
	} catch (err) {
		console.log(err);
		throw err;
	}
}

async function deleteApiKey(apiKeyId: string) {
	try {
		return await dbGaio().exec(
			'ALTER TABLE api_key DELETE WHERE apiKeyId = {apiKeyId: String}',
			{
				params: {
					apiKeyId
				}
			}
		);
	} catch (err) {
		console.log(err);
		throw err;
	}
}

async function getApiById(apiId: string, endpoint: string) {
	try {
		return await dbGaio()
			.query<ApiType>(
				`SELECT * FROM api
						WHERE apiId = {apiId: String}
						AND endpoint = {endpoint: String}`,
				{
					params: {
						apiId,
						endpoint
					},
					parse: ['options', 'assignedKeys']
				}
			)
			.then((res) => res[0]);
	} catch {
		throw new HTTPException(404, {
			cause: 'apiNotFound',
			message: 'API not found'
		});
	}
}

async function getApiKeySecretsOfIds(assignedKeys: string[]) {
	try {
		return await dbGaio()
			.query<ApiKeyType>(
				`SELECT secret FROM api_key
						WHERE apiKeyId IN {apiKeyId: Array(String)}`,
				{
					params: {
						apiKeyId: assignedKeys
					}
				}
			)
			.then((res) => res.map((o) => o.secret));
	} catch {
		throw new HTTPException(404, {
			cause: 'apiKeyNotFound',
			message: 'API Key not found'
		});
	}
}

async function deleteApiHooksByAppId(appId: string) {
	await dbGaio().exec(
		`
			DELETE
			FROM api_key
			WHERE appId = {appId: String}
		`,
		{
			params: {
				appId
			}
		}
	);
	return await dbGaio().exec(
		`
			DELETE
			FROM api
			WHERE appId = {appId: String}
		`,
		{
			params: {
				appId
			}
		}
	);
}

export default {
	getAllAppHooks,
	saveApi,
	saveApiKey,
	deleteApi,
	deleteApiKey,
	getApiById,
	getApiKeySecretsOfIds,
	deleteApiHooksByAppId
};
