import apiHookRepository from '@/components/api-hook/api-hook.repository';
import jwtGuard from '@/server/middleware/jwt.guard';
import { readBody } from '@/server/middleware/readBody';
import type { ApiKeyType, ApiType, UserType } from '@gaio/shared/types';
import { Hono } from 'hono';
import { sign } from 'hono/jwt';

interface SaveApiRequest {
	apiData: ApiType;
	user: UserType;
}

interface SaveApiKeyRequest {
	apiKeyData: ApiKeyType;
	regenerate: boolean;
	user: UserType;
}

const app = new Hono()
	// list api and api keys
	.get('/list/:appId', jwtGuard('dev').isAuth, async (c) => {
		const { appId } = c.req.param();

		return c.json(
			(await apiHookRepository.getAllAppHooks(appId)) as {
				apiList: ApiType[];
				apiKeyList: ApiKeyType[];
			}
		);
	})

	// save api
	.post('/api', jwtGuard('dev').isAuth, async (c) => {
		const { apiData, user } = await readBody<SaveApiRequest>(c);

		return c.json(await apiHookRepository.saveApi(apiData, user.userId));
	})

	// save api key
	.post('/key', jwtGuard('dev').isAuth, async (c) => {
		const { apiKeyData, user, regenerate } =
			await readBody<SaveApiKeyRequest>(c);

		const savedApiKeyData = await apiHookRepository.saveApiKey(
			apiKeyData,
			regenerate,
			user.userId
		);

		const secrets = await apiHookRepository.getApiKeySecretsOfIds([
			savedApiKeyData.apiKeyId
		]);

		const token = await sign(
			{
				appId: savedApiKeyData.appId
			},
			secrets[0]
		);

		delete savedApiKeyData.secret;

		return c.json({
			apiKeyData: savedApiKeyData,
			token
		});
	})

	// delete api
	.delete('/api/:apiId', jwtGuard('dev').isAuth, async (c) => {
		const { apiId } = c.req.param();

		return c.json(await apiHookRepository.deleteApi(apiId));
	})

	// delete api key
	.delete('/key/:apiKeyId', jwtGuard('dev').isAuth, async (c) => {
		const { apiKeyId } = c.req.param();

		return c.json(await apiHookRepository.deleteApiKey(apiKeyId));
	});

export default app;
