export type MemberRegistrationData = {
	name: string
	email: string
	password: string
	dateOfBirth: string
	language: string
	signature?: string
	phone?: string
	optIn: boolean
	sponsorMemberId?: string
	leadId?: string
	source: string
	country: string
	companyName?: string
	employeeAmount?: number
	ipAddress?: string
	userAgent?: string
	browser?: string
	operatingSystem?: string
	contentId?: string
}

export type MemberRegistrationReponse = {
	signupId: string
	memberId: string | null
	ipAddress: string | null
	userAgent: string | null
	source: string | null
	contentId: string | null
	browser: string | null
	operatingSystem: string | null
	country: string | null
	createdAt: Date
	updatedAt: Date
}
