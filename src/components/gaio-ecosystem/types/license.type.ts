export interface DockerLicenseOptions {
	licenseId: string
	accessToken?: string
	macAddress?: string
	computerName?: string
	expiresAt?: string
	createdAt?: string
}

export interface LicenseValidationParams {
	licenseId: string
	amountUsers: number
}

export interface LicenseStatusResponse {
	active: boolean
	expiresAt?: Date
	daysUntilExpiration?: number
	userLimitExceeded?: boolean
	currentUsers?: number
	maxUsers?: number
}

export interface LicenseNotFoundError {
	message: string
	code: 'LICENSE_NOT_FOUND'
}
