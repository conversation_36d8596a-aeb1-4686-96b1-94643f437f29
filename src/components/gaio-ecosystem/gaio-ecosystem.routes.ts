import { readBody } from '@/server/middleware/readBody'
import { Hono } from 'hono'
import { confirmRecoveryTokenUseCase, forgotPassword, getLicense, loginEcosystem, registerMember, validateEcosystemEmail } from './use-cases/gaio-ecosystem.use-case'
import type { MemberRegistrationData } from './types/register-member'

const app = new Hono()
	.post('/validate-email', async (c) => {
		const { email } = await readBody<{
			email: string
		}>(c)
		return c.json(await validateEcosystemEmail(email))
	})

	.post('/login', async (c) => {
		const { email, password } = await readBody<{
			email: string
			password: string
		}>(c)
		return c.json(await loginEcosystem(email, password))
	})

	.post('/forgot-password', async (c) => {
		const { email } = await readBody<{
			email: string
		}>(c)
		return c.json(await forgotPassword(email))
	})

	.post('/reset-password', async (c) => {
		const { email, securityCode, newPassword } = await readBody<{
			email: string
			securityCode: string
			newPassword: string
		}>(c)
		return c.json(await confirmRecoveryTokenUseCase(email, securityCode, newPassword))
	})

	.post('/register-member', async (c) => {
		const body = await readBody<MemberRegistrationData>(c)
		return c.json(await registerMember(body))
	})

	.get('/license', async (c) => {
		return c.json(await getLicense())
	})

export default app
