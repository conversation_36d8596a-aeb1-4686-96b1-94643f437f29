import { env } from '@/utils/env'
import type { LicenseStatusResponse, LicenseNotFoundError } from '../types/license.type'

const LICENSE_SERVICE_URL = env.GAIO_LICENSE_SERVICE_URL
const DEFAULT_TIMEOUT = env.GAIO_LICENSE_SERVICE_TIMEOUT

export async function getLicenseStatus(licenseId: string, amountUsers: number): Promise<LicenseStatusResponse> {
	const url = `${LICENSE_SERVICE_URL}/licenses/${licenseId}/status?amountUsers=${amountUsers}`

	const controller = new AbortController()
	const timeoutId = setTimeout(() => controller.abort(), DEFAULT_TIMEOUT)

	try {
		const response = await fetch(url, {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json'
			},
			signal: controller.signal
		})

		clearTimeout(timeoutId)

		if (!response.ok) {
			if (response.status === 404) {
				const errorData = await response.json() as LicenseNotFoundError
				throw new Error(`LICENSE_NOT_FOUND: ${errorData.message}`)
			}
			throw new Error(`License service API error: ${response.status} - ${response.statusText}`)
		}

		const data = await response.json() as LicenseStatusResponse
		
		if (data.expiresAt && typeof data.expiresAt === 'string') {
			data.expiresAt = new Date(data.expiresAt)
		}

		return data
	} catch (error) {
		clearTimeout(timeoutId)
		
		if (error instanceof Error && error.name === 'AbortError') {
			throw new Error(`License service timeout after ${DEFAULT_TIMEOUT}ms`)
		}
		
		throw error
	}
}
