import { env } from '@/utils/env';
import type {
	ValidateEmailBody,
	ValidateEmailResponse
} from '../types/validate-email.type';
import type {
	AuthenticationUseCaseParams,
	AuthenticationUseCaseReturn
} from '../types/login.type';
import type {
	MemberRegistrationData,
	MemberRegistrationReponse
} from '../types/register-member';
import { HTTPException } from 'hono/http-exception';
import type { ContentfulStatusCode } from 'hono/utils/http-status';

const ECOSYSTEM_API_URL = env.GAIO_ECOSYSTEM_URL;
const DEFAULT_TIMEOUT = env.GAIO_ECOSYSTEM_TIMEOUT;

export async function validateEmail({ email }: ValidateEmailBody) {
	const body = JSON.stringify({ email: email });

	const response = await fetch(
		`${ECOSYSTEM_API_URL}/api/manager/validate-email`,
		{
			body: body,
			headers: { 'Content-Type': 'application/json' },
			method: 'POST'
		}
	).then(async (res) => {
		if (!res.ok) {
			throw new HTTPException(res.status as ContentfulStatusCode, {
				message: res.statusText
			});
		}
		return res.json();
	});

	return response as ValidateEmailResponse;
}

export async function confirmRecoveryTokenProvider(
	email: string,
	token: string
) {
	const body = JSON.stringify({ email: email, token: token });

	const response = await fetch(
		`${ECOSYSTEM_API_URL}/api/manager/confirm-recovery-token`,
		{
			body: body,
			headers: { 'Content-Type': 'application/json' },
			method: 'POST'
		}
	).then(async (res) => {
		if (!res.ok) {
			throw new HTTPException(res.status as ContentfulStatusCode, {
				message: res.statusText
			});
		}
		return res.json();
	});

	return response.recoveryAuthorizationToken as string;
}

export async function resetPasswordProvider(
	token: string,
	newPassword: string
) {
	const body = JSON.stringify({ password: newPassword });

	const response = await fetch(
		`${ECOSYSTEM_API_URL}/api/manager/reset-password`,
		{
			body: body,
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`
			},
			method: 'POST'
		}
	).then(async (res) => {
		console.log('res', res);
		if (!res.ok) {
			throw new HTTPException(res.status as ContentfulStatusCode, {
				message: res.statusText
			});
		}
		return res.json();
	});

	console.log('response', response);

	return response.token as string;
}

export async function forgotPasswordProvider({ email }: ValidateEmailBody) {
	const body = JSON.stringify({ email: email });

	const response = await fetch(
		`${ECOSYSTEM_API_URL}/api/manager/forgot-password`,
		{
			body: body,
			headers: { 'Content-Type': 'application/json' },
			method: 'POST'
		}
	).then(async (res) => {
		if (!res.ok) {
			throw new HTTPException(res.status as ContentfulStatusCode, {
				message: res.statusText
			});
		}
		return res.json();
	});
	return response as ValidateEmailResponse;
}

export async function loginProvider({
	email,
	password
}: AuthenticationUseCaseParams) {
	const body = JSON.stringify({ email: email, password: password });

	const response = await fetch(`${ECOSYSTEM_API_URL}/api/manager/login`, {
		body: body,
		headers: { 'Content-Type': 'application/json' },
		method: 'POST'
	}).then(async (res) => {
		if (!res.ok) {
			throw new HTTPException(res.status as ContentfulStatusCode, {
				message: res.statusText
			});
		}
		return res.json();
	});
	return response as AuthenticationUseCaseReturn;
}

export async function registerMemberProvider(data: MemberRegistrationData) {
	const body = JSON.stringify(data);

	const response = await fetch(`${ECOSYSTEM_API_URL}/api/manager/sign-up`, {
		body: body,
		headers: { 'Content-Type': 'application/json' },
		method: 'POST'
	}).then(async (res) => {
		const reponse = await res.json();
		console.log('reponse', reponse);
		if (!res.ok) {
			const message = reponse.error;
			console.log(' message', message);
			throw new HTTPException(res.status as ContentfulStatusCode, {
				message: message
			});
		}

		return reponse;
	});
	return response as MemberRegistrationReponse;
}

export async function newDockerLicenseProvider(
	token: string,
	macAddress: string,
	computerName: string
) {
	const response = await fetch(
		`${ECOSYSTEM_API_URL}/api/manager/new-docker-license`,
		{
			body: JSON.stringify({
				computerName: computerName,
				macAddress: macAddress
			}),
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`
			},
			method: 'POST'
		}
	).then(async (res) => {
		if (!res.ok) {
			throw new HTTPException(res.status as ContentfulStatusCode, {
				message: res.statusText
			});
		}
		return res.json();
	});
	return response as AuthenticationUseCaseReturn;
}
