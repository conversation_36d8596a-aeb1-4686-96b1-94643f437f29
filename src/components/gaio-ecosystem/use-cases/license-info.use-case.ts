import { validateLicenseForNewUser } from './license-validation.use-case'
import { HTTPException } from 'hono/http-exception'

export async function getLicenseInfo() {
	try {
		const licenseValidation = await validateLicenseForNewUser()
		
		return {
			canCreateUser: licenseValidation.canCreateUser,
			message: licenseValidation.message,
			licenseInfo: licenseValidation.licenseData
		}
	} catch (error) {
		if (error instanceof HTTPException) {
			throw error
		}
		
		throw new HTTPException(500, {
			cause: 'licenseInfoError',
			message: 'Failed to get license information'
		})
	}
}
