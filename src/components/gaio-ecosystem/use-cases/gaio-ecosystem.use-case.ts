import { HTTPException } from 'hono/http-exception';
import {
	validateEmail,
	loginProvider,
	newDockerLicenseProvider,
	registerMemberProvider,
	forgotPasswordProvider,
	confirmRecoveryTokenProvider,
	resetPasswordProvider
} from '../providers/gaio-ecosystem.provider';
import { getComputerInfo } from '@/utils/get-computer-info';
import settingRepository from '@/components/setting/setting.repository';
import type { MemberRegistrationData } from '../types/register-member';
import { getSetting } from '@/components/setting/settings.core';

export async function validateEcosystemEmail(email: string) {
	return await validateEmail({ email: email });
}

export async function confirmRecoveryTokenUseCase(
	email: string,
	securityCode: string,
	newPassword: string
) {
	const accessToken = await confirmRecoveryTokenProvider(email, securityCode);

	if (!accessToken) {
		throw new HTTPException(401, {
			cause: 'userNotFound',
			message: 'User not found'
		});
	}

	await resetPasswordProvider(accessToken, newPassword);

	return {
		data: true,
		timestamp: new Date().toISOString()
	};
}

export async function forgotPassword(email: string) {
	return await forgotPasswordProvider({ email: email });
}

export async function getLicense() {
	const license = await getSetting('gaio-docker-license');

	if (license?.options?.licenseId) {
		return {
			data: {
				licenseId: license.options.licenseId
			},
			timestamp: new Date().toISOString()
		};
	}

	throw new HTTPException(404, {
		cause: 'licenseNotFound',
		message: 'license not found'
	});
}

export async function registerMember(data: MemberRegistrationData) {
	const registrationResult = await registerMemberProvider(data);

	if (registrationResult?.memberId) {
		const loginResult = await loginEcosystem(data.email, data.password);

		if (loginResult.data)
			return {
				data: true,
				timestamp: new Date().toISOString()
			};
	}

	throw new HTTPException(400, {
		cause: 'memberRegistrationFailed',
		message: 'Failed to register member'
	});
}

export async function getComputerInfoAndCreateLicense(accessToken: string) {
	const computerInfo = await getComputerInfo();

	if (!computerInfo) {
		throw new HTTPException(500, {
			cause: 'computerInfoNotFound',
			message: 'Unable to retrieve computer information'
		});
	}

	// if (!computerInfo.macAddress) {
	// 	throw new HTTPException(500, {
	// 		cause: 'macAddressNotFound',
	// 		message: 'MAC address not found in computer information'
	// 	})
	// }

	// if (!computerInfo.computerName) {
	// 	throw new HTTPException(500, {
	// 		cause: 'computerNameNotFound',
	// 		message: 'Computer name not found in computer information'
	// 	})
	// }

	const license = await newDockerLicenseProvider(
		accessToken,
		computerInfo.macAddress,
		computerInfo.computerName
	);

	if (!license) {
		throw new HTTPException(500, {
			cause: 'licenseGenerationFailed',
			message: 'Failed to generate Docker license'
		});
	}

	const setting = await settingRepository.upsertSetting(
		{
			settingId: 'gaio-docker-license',
			options: license
		},
		{
			userId: 'docker-user'
		}
	);

	if (!setting) {
		throw new HTTPException(500, {
			cause: 'settingSaveFailed',
			message: 'Failed to save Docker license setting'
		});
	}
}

export async function loginEcosystem(email: string, password: string) {
	const userData = await loginProvider({ email: email, password: password });

	if (!userData) {
		throw new HTTPException(401, {
			cause: 'userNotFound',
			message: 'User not found'
		});
	}

	await getComputerInfoAndCreateLicense(userData.accessToken);

	return {
		data: true,
		timestamp: new Date().toISOString()
	};
}
