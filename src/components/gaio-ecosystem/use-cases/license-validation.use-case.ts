import { getLicenseStatus } from '../providers/license-service.provider';
import { getSetting } from '@/components/setting/settings.core';
import UserRepository from '@/components/user/user.repository';
import { HTTPException } from 'hono/http-exception';
import type { DockerLicenseOptions } from '../types/license.type';

export interface LicenseValidationResult {
	canCreateUser: boolean;
	message?: string;
	licenseData?: {
		licenseId: string;
		maxUsers?: number;
		currentUsers: number;
		active: boolean;
		expiresAt?: Date;
		daysUntilExpiration?: number;
		userLimitExceeded?: boolean;
	};
}

export async function validateLicenseForNewUser(): Promise<LicenseValidationResult> {
	try {
		const dockerLicense = await getSetting('gaio-docker-license');
		console.log('docker license', dockerLicense);

		if (!dockerLicense || !dockerLicense.options?.licenseId) {
			throw new HTTPException(404, {
				cause: 'licenseNotFound',
				message: 'Docker license not found'
			});
		}

		const licenseOptions = dockerLicense.options as DockerLicenseOptions;
		const licenseId = licenseOptions.licenseId;

		const currentUsers = await UserRepository.countUsers();

		const licenseStatus = await getLicenseStatus(licenseId, currentUsers + 1);

		const canCreateUser =
			licenseStatus.active && !licenseStatus.userLimitExceeded;

		let message: string | undefined;
		if (!licenseStatus.active) {
			message = 'License is not active';
		} else if (licenseStatus.userLimitExceeded) {
			message = `User limit exceeded. Current: ${licenseStatus.currentUsers}, Max: ${licenseStatus.maxUsers}`;
		} else if (
			licenseStatus.daysUntilExpiration &&
			licenseStatus.daysUntilExpiration <= 7
		) {
			message = `License expires in ${licenseStatus.daysUntilExpiration} days`;
		}

		return {
			canCreateUser,
			message,
			licenseData: {
				licenseId,
				maxUsers: licenseStatus.maxUsers,
				currentUsers,
				active: licenseStatus.active,
				expiresAt: licenseStatus.expiresAt,
				daysUntilExpiration: licenseStatus.daysUntilExpiration,
				userLimitExceeded: licenseStatus.userLimitExceeded
			}
		};
	} catch (error) {
		if (error instanceof HTTPException) {
			throw error;
		}

		if (error instanceof Error) {
			throw new HTTPException(500, {
				cause: 'licenseValidationError',
				message: `Failed to validate license: ${error.message}`
			});
		}

		throw new HTTPException(500, {
			cause: 'unknownError',
			message: 'Unknown error occurred during license validation'
		});
	}
}
