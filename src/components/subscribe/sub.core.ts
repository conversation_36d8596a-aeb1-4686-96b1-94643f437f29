import { subJob } from '@/components/subscribe/sub.job';
import { subApp } from '@/components/subscribe/subs.app';
import { gaioRedis } from '@/db/gaioRedis';
import type { Context } from 'hono';
import type {
	WSContext,
	WSMessageReceive
} from 'hono/dist/types/helper/websocket';

export function subCore(c: Context) {
	const channel = c.req.param('channel');

	if (!channel) {
		throw new Error('Channel not provided');
	}

	const typeChannel = channel.split('-')[0];
	const appId = channel.split('-')[1];
	const key = `${typeChannel}-${appId}*`;

	const redis = gaioRedis.sub();
	redis.psubscribe(key);

	return {
		onClose: () => {
			redis.punsubscribe(key);
		},
		onOpen(_: Event, ws: WSContext) {
			redis.on('pmessage', async (_, redisChannel, message) => {
				if (redisChannel.includes('type:app') && typeChannel === 'type:app') {
					const user = c.get('user');
					subApp(user, redisChannel, message, ws);
				} else if (redisChannel.includes('type:python:app')) {
					ws.send(message);
				} else if (redisChannel.includes('type:automl')) {
					ws.send(message);
				} else {
					subJob(redisChannel, channel, message, ws);
				}
			});
		},
		onMessage(event: MessageEvent<WSMessageReceive>, ws: WSContext) {
			const message = event.data.toString();
			const parsedMessage = JSON.parse(message);

			if (parsedMessage.type === 'ping') {
				ws.send(
					JSON.stringify({
						type: 'pong',
						timestamp: new Date().toISOString()
					})
				);
			}
		}
	};
}
