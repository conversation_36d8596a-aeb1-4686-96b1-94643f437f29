import { dbGaio } from '@/db/db.gaio';

async function getUserSerial() {
	return await dbGaio('upUserSerial')
		.exec(
			`INSERT INTO serial (serial, type) 
				VALUES(
					(SELECT serial + 1 as serial 
						FROM serial FINAL WHERE type = 'user'),
					'user'
				)`
		)
		.then(() =>
			dbGaio('getUserSerial')
				.query<{ serial: unknown }>(
					`SELECT serial FROM serial FINAL WHERE type = 'user'`
				)
				.then((res) => res[0].serial)
		);
}

async function getAppSerial() {
	return await dbGaio('upAppSerial')
		.exec(
			`INSERT INTO serial (serial, type) 
				VALUES(
					(SELECT serial + 1 as serial 
						FROM serial FINAL WHERE type = 'app'),
					'app'
				)`
		)
		.then(() =>
			dbGaio('getAppSerial')
				.query<{ serial: unknown }>(
					`SELECT serial FROM serial FINAL WHERE type = 'app'`
				)
				.then((res) => res[0].serial)
		);
}

export default {
	getAppSerial,
	getUserSerial
};
