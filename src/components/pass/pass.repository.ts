import { dbGaio } from '@/db/db.gaio';
import type { PassEntity } from '@/db/entities';

async function getListOfAppsBucketHasAccess(appId: string): Promise<string[]> {
	return await dbGaio('listPass')
		.query<{ sees: string[] }>(
			`SELECT sees
				FROM pass
				WHERE appId = {appId: String} LIMIT 1`,
			{
				params: {
					appId
				}
			}
		)
		.then((res) => (res[0] ? res[0].sees : []));
}

async function getAppPassThatHasAppIdAccess(
	appId: string
): Promise<PassEntity[]> {
	return await dbGaio('listPass').query<PassEntity>(
		`SELECT appId, repoId, sees
			FROM pass
			WHERE has(sees, {appId: String}) `,
		{
			params: {
				appId
			},
			parse: ['sees']
		}
	);
}

async function createRepoPassOnRepository(
	repoId: string,
	appId: string,
	password: string,
	userId: string
) {
	return await dbGaio('insertPass').insert('pass', [
		{
			appId,
			createdBy: userId,
			modifiedBy: userId,
			password,
			repoId,
			sees: []
		}
	]);
}

async function saveSees(appId: string, repoId: string, sees: string[]) {
	return await dbGaio('updatePass').exec(
		`ALTER TABLE pass 
			UPDATE sees = {sees: Array(String)} 
			WHERE appId = {appId: String} 
			AND repoId = {repoId: String}`,
		{
			params: {
				appId,
				repoId,
				sees
			}
		}
	);
}

async function getPassPassword(appId: string, repoId: string) {
	return await dbGaio('getPassCredentials').query<PassEntity>(
		`SELECT password 
			FROM pass 
			WHERE appId = {appId: String} 
			AND repoId = {repoId: String} LIMIT 1`,
		{
			params: {
				appId,
				repoId
			}
		}
	);
}

async function deletePassByAppId(appId: string) {
	return await dbGaio('deletePass').exec(
		'DELETE FROM pass WHERE appId = {appId: String}',
		{
			params: {
				appId
			}
		}
	);
}

export default {
	createRepoPassOnRepository,
	deletePassByAppId,
	getListOfAppsBucketHasAccess,
	getPassPassword,
	saveSees,
	getAppPassThatHasAppIdAccess
};
