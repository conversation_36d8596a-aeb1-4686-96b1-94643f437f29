import { dbGaio } from '@/db/db.gaio';
import { clickDate } from '@/utils/helpers';
import type { MetaViewType, UserType } from '@gaio/shared/types';

async function getMetaViewsByUserAndMetaId(userId: string, metaId: string) {
	return await dbGaio('getMetaViewsByUser').query(
		`SELECT meta_view.*, 'view' as powerType, 'powerView' as icon FROM meta_view
				LEFT JOIN app ON app.appId = meta_view.appId
			WHERE userId = {userId: String} AND metaId = {metaId: String}`,
		{
			params: {
				userId,
				metaId
			},
			parse: ['task', 'tags', 'options']
		}
	);
}

async function getMetaViewsByUser(userId: string) {
	return await dbGaio('getMetaViewsByUser').query(
		`
			SELECT 
			  meta_view.metaViewId AS metaViewId,
			  meta_view.userId AS userId,
			  meta_view.appId AS appId,
			  meta_view.repoId AS repoId,
			  meta_view.metaId AS metaId,
			  meta_view.label AS label,
			  meta_view.task AS task,
			  meta_view.tags AS tags,
			  meta_view.options AS options,
			  meta_view.shared AS shared,
			  meta_view.modifiedBy AS modifiedBy,
			  meta_view.createdBy AS createdBy,
			  meta_view.modifiedAt AS modifiedAt,
			  meta_view.createdAt AS createdAt,
			  meta_view.updatedAt AS updatedAt,
			  'view' AS powerType,
			  'powerView' AS icon
			FROM 
			  meta_view
			LEFT JOIN 
			  app ON app.appId = meta_view.appId
			INNER JOIN
			  meta ON meta.metaId = meta_view.metaId
			WHERE 
			  meta_view.userId = {userId: String}
			  AND meta.status <> 'inactive';	
		`,
		{
			params: {
				userId
			},
			parse: ['task', 'tags', 'options']
		}
	);
}

async function createMetaView(metaView: MetaViewType, userContext: UserType) {
	return await dbGaio('createMetaView').insert('meta_view', [
		{
			appId: metaView.appId,
			createdAt: clickDate(new Date()),
			createdBy: userContext.userId,
			label: metaView.label,
			metaId: metaView.metaId,
			metaViewId: metaView.metaViewId,
			modifiedBy: userContext.userId,
			options: JSON.stringify(metaView.options || {}),
			repoId: metaView.repoId,
			shared: metaView.shared,
			tags: JSON.stringify(metaView.tags || []),
			task: JSON.stringify(metaView.task || {}),
			updatedAt: clickDate(new Date()),
			userId: userContext.userId
		}
	]);
}

async function saveMetaView(metaView: MetaViewType, userContext: UserType) {
	return await dbGaio('saveMetaView').exec(
		`
		ALTER TABLE meta_view
			UPDATE label = {label: String}, 
				task = {task: String},
				tags = {tags: String},
				options = {options: String},
				shared = {shared: Boolean},
				modifiedBy = {modifiedBy: String},
				updatedAt = {updatedAt: String}
			WHERE metaViewId = {metaViewId: String}`,
		{
			params: {
				label: metaView.label,
				metaViewId: metaView.metaViewId,
				modifiedBy: userContext.userId,
				options: metaView.options || {},
				shared: metaView.shared,
				tags: metaView.tags || [],
				task: metaView.task || {},
				updatedAt: clickDate(new Date())
			},
			stringify: ['task', 'tags', 'options']
		}
	);
}

async function deleteMetaViewByAppId(appId: string) {
	return await dbGaio('deleteMetaViewByAppId').exec(
		'DELETE FROM meta_view WHERE appId = {appId: String}',
		{
			params: {
				appId
			}
		}
	);
}

export default {
	createMetaView,
	deleteMetaViewByAppId,
	getMetaViewsByUserAndMetaId,
	getMetaViewsByUser,
	saveMetaView
};
