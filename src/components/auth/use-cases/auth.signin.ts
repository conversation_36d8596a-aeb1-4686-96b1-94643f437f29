import { join } from 'node:path';
import {
	userPicExtension,
	userPicUrl,
	userPicsFolderPath
} from '@/components/content/use-cases/content.core';
import UserRepository from '@/components/user/user.repository';
import { HTTPException } from 'hono/http-exception';
import { userTokenWithPermissions } from '../auth.core';
import { addAppToUserIfUserHasNoApps } from '@/components/user/use-cases/user.prepare';

export async function signIn({
	email,
	password,
	baseUrl
}: { email: string; password: string; baseUrl: string }) {
	if (!email || !password) {
		throw new HTTPException(400, {
			cause: 'missingFields',
			message: 'missingFields'
		});
	}

	const user = await UserRepository.getUserByEmail(email);

	if (!user || !user.password) {
		throw new HTTPException(400, {
			cause: 'invalidUsernameOrPassword',
			message: 'username or password is invalid [1]'
		});
	}

	// check if password is correct
	const doesThePasswordMatch = await Bun.password.verify(
		password,
		user.password
	);

	if (!doesThePasswordMatch) {
		throw new HTTPException(400, {
			cause: 'invalidUsernameOrPassword',
			message: 'username or password is invalid [2]'
		});
	}

	const { accessToken, refreshToken } = await userTokenWithPermissions(user);

	await UserRepository.saveUserRefreshToken(user.userId, refreshToken);

	delete user.password;

	const userPic = Bun.file(join(userPicsFolderPath, `${user.userId}.png`));
	if (await userPic.exists()) {
		user.options = {
			...user.options,
			profilePic: `${userPicUrl(baseUrl, user.userId)}${userPicExtension}`
		};
	} else {
		user.options = { ...user.options, profilePic: null };
	}

	// add app to user if user has no apps
	await addAppToUserIfUserHasNoApps(user);

	return {
		refreshToken,
		token: accessToken,
		user: { ...user, refreshToken }
	};
}
