import UserRepository from '@/components/user/user.repository';
import type { UserType } from '@gaio/shared/types';
import { HTTPException } from 'hono/http-exception';
import { decodeJwtToken } from '../auth.core';

export async function signOut({
	accessToken,
	queryToken
}: { accessToken: string; queryToken?: string }) {
	try {
		let token = accessToken;

		if (!token) {
			if (queryToken) {
				token = `Bearer ${queryToken}`;
			} else {
				throw new HTTPException(400, {
					cause: 'invalidAuthorizationHeader',
					message: 'Make sure to use the Authorization header'
				});
			}
		}

		const jwt = token.split(' ')[1];
		if (!jwt) {
			throw new HTTPException(400, {
				cause: 'invalidBearerType',
				message: 'Use Bearer Authorization'
			});
		}

		const { payload } = await decodeJwtToken(jwt);

		const { userId } = payload as UserType;

		return await UserRepository.deleteUserRefreshToken(userId);
	} catch (err) {
		if (err instanceof HTTPException) {
			throw err;
		}

		throw new HTTPException(500, {
			cause: 'internalErrorSigningOut',
			message: 'Internal error signing out'
		});
	}
}
