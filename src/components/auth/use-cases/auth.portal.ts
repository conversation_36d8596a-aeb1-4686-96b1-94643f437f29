import UserRepository from "@/components/user/user.repository";
import { HTTPException } from "hono/http-exception";
import { userTokenWithPermissions } from "../auth.core";

export async function authPortal(portal: string) {
  const user = await UserRepository.getUserByEmail(portal);

  if (!user) {
    throw new HTTPException(404, { message: "User not found" });
  }

  delete user.password;

  const { accessToken, refreshToken } = await userTokenWithPermissions(user);

  const appId = user.options.portalAppId;

  return {
    token: user.email,
    user,
    appId,
    accessToken,
    refreshToken,
    options: user.options,
  };
}
