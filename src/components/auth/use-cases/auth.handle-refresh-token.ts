import UserRepository from '@/components/user/user.repository';
import { HTTPException } from 'hono/http-exception';
import { userTokenWithPermissions } from '../auth.core';

export async function handleRefreshToken({
	userId,
	refreshToken,
	userEmail
}: {
	userEmail: string;
	userId: string;
	refreshToken: string;
}) {
	try {
		const userRefreshToken = await UserRepository.getUserRefreshToken(userId);
		if (!userRefreshToken) {
			throw new HTTPException(401, {
				cause: 'invalidRefreshToken',
				message: 'Invalid refresh token [0]'
			});
		}

		if (userRefreshToken !== refreshToken.split('Bearer ')[1]) {
			throw new HTTPException(401, {
				cause: 'invalidRefreshToken',
				message: 'Invalid refresh token [1]'
			});
		}

		const { accessToken, refreshToken: newRefreshToken } =
			await userTokenWithPermissions({
				email: userEmail,
				userId: userId
			});

		await UserRepository.saveUserRefreshToken(userId, newRefreshToken);

		const user = await UserRepository.getUserByEmail(userEmail);

		delete user.password;

		return {
			refreshToken: newRefreshToken,
			token: accessToken,
			user
		};
	} catch (error) {
		throw new HTTPException(500, {
			cause: error,
			message: 'Internal Server Error'
		});
	}
}
