import { env } from '@/utils/env';
import type { TagEntity, UserType } from '@gaio/shared/types';
import { HTTPException } from 'hono/http-exception';
import { decode, sign, verify } from 'hono/jwt';
import { type JWTPayload, JwtTokenExpired } from 'hono/utils/jwt/types';
import UserRepository from '@/components/user/user.repository';
import { getId } from '@gaio/shared/utils';

const ACCESS_TOKEN_SECRET = env.ACCESS_TOKEN_SECRET;
const REFRESH_TOKEN_SECRET = env.REFRESH_TOKEN_SECRET;

export async function userTokenWithPermissions(user: UserType) {
	const userAppTags = await UserRepository.getUserTags(user.userId);

	const permissions = userAppTags.map((tag: TagEntity) => {
		return `${tag.type}::${tag.role}::${tag.refId}`;
	});

	return await generateTokens({
		permissions: permissions,
		userEmail: user.email,
		userId: user.userId
	});
}

export type SignInJWTPayload = {
	permissions: string[];
	userEmail: string;
	userId: string;
};

export async function signAccessToken({
	data,
	expiration
}: { data: Record<string, unknown>; expiration?: number }) {
	return await sign(
		{
			...data,
			createdAt: new Date().toISOString(),
			exp: expiration
		},
		ACCESS_TOKEN_SECRET
	);
}

async function signRefreshToken({
	data,
	expiration
}: { data: Record<string, unknown>; expiration?: number }) {
	return await sign(
		{
			...data,
			createdAt: new Date().toISOString(),
			exp: expiration
		},
		REFRESH_TOKEN_SECRET
	);
}

export async function generateTokens({
	userId,
	userEmail,
	permissions = []
}: SignInJWTPayload) {
	const sessionid = getId();
	return {
		accessToken: await signAccessToken({
			data: { permissions, userEmail, userId, sessionid }
			// expiration: Math.floor(Date.now() / 1000) + 5 // 5 seconds
			// expiration: Math.floor(Date.now() / 1000) + 60 * 30 // 30 minutes
			// expiration: Math.floor(Date.now() / 1000) + 60 * 60 * 24 * 7 // 7 day
		}),
		refreshToken: await signRefreshToken({
			data: { permissions, userEmail, userId, sessionid },
			expiration: Math.floor(Date.now() / 1000) + 60 * 60 * 24 * 7 // 30 days
		})
	};
}

export async function validateAccessToken(token: string) {
	try {
		return (await verify(token, ACCESS_TOKEN_SECRET)) as SignInJWTPayload &
			JWTPayload;
	} catch (error: unknown) {
		if (error instanceof JwtTokenExpired) {
			throw new HTTPException(401, {
				cause: 'expiredToken',
				message: 'Expired Token'
			});
		}

		throw new HTTPException(401, {
			cause: 'invalidAccessToken',
			message: 'Invalid Access Token'
		});
	}
}

export async function validateRefreshToken(token: string) {
	try {
		return (await verify(token, REFRESH_TOKEN_SECRET)) as SignInJWTPayload &
			JWTPayload;
	} catch (error: unknown) {
		if (error instanceof JwtTokenExpired) {
			throw new HTTPException(401, {
				cause: 'expiredToken',
				message: 'Expired Token'
			});
		}

		throw new HTTPException(401, {
			cause: 'invalidRefreshToken',
			message: 'Invalid Refresh Token'
		});
	}
}

export async function decodeJwtToken(token: string) {
	try {
		return decode(token);
	} catch (err: unknown) {
		console.error('jwt decode error', err);

		throw new HTTPException(500, {
			cause: 'internalErrorDecodingToken',
			message: 'Internal Error Decoding Token'
		});
	}
}
