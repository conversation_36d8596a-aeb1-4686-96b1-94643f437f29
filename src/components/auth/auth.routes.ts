import { signIn } from '@/components/auth/use-cases/auth.signin';
import { signOut } from '@/components/auth/use-cases/auth.signout';
import { readBody } from '@/server/middleware/readBody';
import type { AuthSignInRequestDTO } from '@/types/http/dtos/auth.dtos';
import { type Context, Hono } from 'hono';
import { HTTPException } from 'hono/http-exception';
import { validateRefreshToken } from './auth.core';
import { handleRefreshToken } from './use-cases/auth.handle-refresh-token';
import { authPortal } from './use-cases/auth.portal';

type Variables = {
	userId: string;
	userEmail: string;
};

const app = new Hono<{ Variables: Variables }>()
	.get(
		'/refresh-token',
		async (c, next) => {
			try {
				const bearerRefreshToken = c.req.header('Gaio-Refresh-Token');

				if (!bearerRefreshToken) {
					throw new HTTPException(401, {
						cause: 'invalidRefreshToken',
						message: 'Missing Refresh Token Header'
					});
				}

				const refreshToken = bearerRefreshToken.split(' ')[1];

				if (!refreshToken) {
					throw new HTTPException(401, {
						cause: 'invalidBearer',
						message: 'Use Bearer Authorization'
					});
				}

				const refreshTokenPayload = await validateRefreshToken(refreshToken);
				if (!refreshTokenPayload) {
					throw new HTTPException(401, {
						cause: 'invalidRefreshToken',
						message: 'Invalid Refresh Token'
					});
				}

				c.set('userId', refreshTokenPayload.userId);
				c.set('userEmail', refreshTokenPayload.userEmail);

				await next();
			} catch (error) {
				if (error instanceof HTTPException) {
					throw error;
				}

				throw new HTTPException(500, {
					cause: error,
					message: 'Internal Server Error'
				});
			}
		},
		async (c) => {
			const refreshToken = c.req.header('Gaio-Refresh-Token');
			const userId = c.get('userId');
			const userEmail = c.get('userEmail');

			return c.json(
				await handleRefreshToken({ refreshToken, userId, userEmail })
			);
		}
	)
	.post('/sign-in', async (c) => {
		const { email, password } = await readBody<AuthSignInRequestDTO>(c);
		const path = c.req.path;
		const url = c.req.url;

		const baseUrl = url.split(path)[0];

		return c.json(await signIn({ baseUrl, email, password }));
	})
	.delete('/sign-out', async (c) => {
		const accessToken = c.req.header('Authorization');
		const queryToken = c.req.query('token');

		await signOut({ accessToken, queryToken });

		return c.json(
			{
				message: 'Sign out successful',
				timestamp: new Date().toISOString()
			},
			{ status: 200 }
		);
	})
	.post('/portal', async (c: Context) => {
		const { portal } = await readBody<{ portal: string }>(c);

		return c.json(await authPortal(portal));
	});

export default app;
