import { preparePythonEnv } from '@/components/python/use-cases/python.task';
import jwtGuard from '@/server/middleware/jwt.guard';
import { readBody } from '@/server/middleware/readBody';
import type { EnvData } from '@gaio/shared/types';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { HTTPException } from 'hono/http-exception';
import { pythonDocs } from './python.docs';

export const app = new Hono().post(
	'/prepare',
	describeRoute(pythonDocs.prepare),
	jwtGuard('dev').isAuth,
	async (c) => {
		try {
			const { user, ...envData } = await readBody<EnvData>(c);

			await preparePythonEnv(envData, user.userId);

			return c.json({
				message: 'Python Env Prepared',
				timestamp: new Date().toISOString()
			});
		} catch (err) {
			if (err instanceof HTTPException) {
				throw err;
			}

			throw new HTTPException(500, {
				cause: 'internalErrorPreparingPythonEnv',
				message: 'Internal error preparing Python environment'
			});
		}
	}
);

export default app;
