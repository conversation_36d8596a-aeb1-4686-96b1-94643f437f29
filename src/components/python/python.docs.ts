import type { RouteOpenAPIDocs } from '@/types/http/route.docs';

export const pythonDocs = {
	getEnv: {
		description: 'Get a python env by appId, flowId & codeSnippetId',
		responses: {
			200: {
				content: {
					application: {
						schema: {
							properties: {
								message: { type: 'string' },
								output: {
									properties: {
										appId: { type: 'string' },
										codeSnippet: { type: 'string' },
										codeSnippetId: { type: 'string' },
										createdAt: { type: 'string' },
										createdBy: { type: 'string' },
										flowId: { type: 'string' },
										options: { type: 'object' },
										type: { type: 'string' },
										updatedAt: { type: 'string' },
										updatedBy: { type: 'string' }
									},
									type: 'object'
								},
								timestamp: { type: 'string' }
							},
							type: 'object'
						}
					}
				},
				description: 'Python environment found'
			},
			404: {
				content: {
					application: {
						schema: {
							properties: {
								cause: { type: 'string' },
								message: { type: 'string' }
							},
							type: 'object'
						}
					}
				},
				description: 'Python environment not found'
			}
		}
	},
	prepare: {
		description:
			'Prepare Python Env - Body: `{ "codeSnippetId": "env_27", "packages": "pandas\nnumpy", "pythonVersion": "3.10.15", "appId": "app:27" }`',
		responses: {
			200: {
				content: {
					application: {
						schema: {
							properties: {
								message: { type: 'string' },
								timestamp: { type: 'string' }
							},
							type: 'object'
						}
					}
				},
				description: 'Python Env Prepared'
			}
		}
	}
} satisfies RouteOpenAPIDocs;
