import { spawn } from 'node:child_process';
import { contentFolder } from '@/components/task/use-cases/runners/runner.tools';
import { dbGaio } from '@/db/db.gaio';
import type { CodeSnippetEntity } from '@/db/entities';
import { gaioRedis } from '@/db/gaioRedis';
import type { EnvData } from '@gaio/shared/types';
import { ensureDirSync } from 'fs-extra';

const CONTEXT_PATH = contentFolder;

export async function preparePythonEnv(envData: EnvData, user: unknown) {
	const publishId = `type:python-${envData.codeSnippetId}`;
	const envFolder = `${CONTEXT_PATH}/apps/${envData.appId.replace('app:', '')}/env/`;

	gaioRedis.pub.publish(publishId, '🐍 Saving requirement!');

	await saveEnv(envData, user);

	ensureDirSync(envFolder);
	ensureDirSync(`${envFolder}.env`);

	await new Promise((resolve, reject) => {
		Bun.write(
			Bun.file(`${envFolder}install.sh`),
			createBashScriptFromContent(envData)
		)
			.then(async () => {
				gaioRedis.pub.publish(publishId, '🐍 Requirement saved!');

				await runInShell(
					envData,
					'bash',
					[`${envFolder}install.sh`],
					publishId
				);

				gaioRedis.pub.publish(publishId, 'Env setup Done! 🐍 ...');

				resolve({ status: 'ok' });
			})
			.catch((err) => {
				reject({
					code: 'error',
					error: err
				});
			});
	});
}

async function saveEnv(envData: EnvData, user: unknown) {
	const envContent = await dbGaio().query<CodeSnippetEntity>(
		`
      SELECT appId from code_snippet where codeSnippetId = { codeSnippetId: String } AND type = 'env'`,
		{
			params: {
				codeSnippetId: envData.codeSnippetId
			}
		}
	);

	if (envContent?.[0]?.appId) {
		await dbGaio().exec(
			'ALTER TABLE code_snippet UPDATE codeSnippet = {codeSnippet: String} WHERE codeSnippetId = {codeSnippetId: String} AND appId = {appId: String}',
			{
				params: {
					appId: envData.appId,
					codeSnippet: envData.packages,
					codeSnippetId: envData.codeSnippetId
				}
			}
		);
	} else {
		await dbGaio().insert('code_snippet', [
			{
				appId: envData.appId,
				codeSnippet: envData.packages,
				codeSnippetId: envData.codeSnippetId,
				createdBy: user as string,
				flowId: envData.flowId,
				options: JSON.stringify({ pythonVersion: envData.pythonVersion }),
				type: 'env',
				updatedBy: user as string
			}
		]);
	}
}

function createBashScriptFromContent(envData: EnvData) {
	const userPackages = [];

	if (envData.packages) {
		const packagesArray = envData.packages.split('\n');
		for (const pkg of packagesArray) {
			if (pkg === '') continue;
			userPackages.push(`pip install ${pkg}`);
		}
	}

	return [
		'#!/bin/bash',
		`eval "$(pyenv init -)"`,
		`eval "$(pyenv virtualenv-init -)"`,
		`pyenv uninstall -f ${envData.codeSnippetId}`,
		`pyenv virtualenv ${envData.pythonVersion} ${envData.codeSnippetId}`,
		`pyenv local ${envData.pythonVersion}  ${envData.codeSnippetId}`,
		`pyenv activate ${envData.codeSnippetId}`,
		'pip install clickhouse_connect',
		'pip install psutil',
		...userPackages
	].join('\n');
}

async function runInShell(
	envData: EnvData,
	mainCommand: string,
	commands: string[],
	publishId: string
) {
	gaioRedis.pub.publish(publishId, '🚀 Started! 🐍 ...');
	const envPath = `${CONTEXT_PATH}/apps/${envData.appId.replace('app:', '')}/env/`;

	const terminal = spawn(mainCommand, commands, {
		cwd: envPath,
		detached: false,
		shell: true
	});

	let error = '';

	terminal?.stdout.on('data', (data: Buffer) => {
		const message = data.toString();
		gaioRedis.pub.publish(publishId, message);
	});

	terminal.stderr.on('data', (data) => {
		const base = Buffer.from(data);
		if (error.length > 4414) {
			error = '';
		}
		error += ` ${base.toString()} `;
	});

	return await new Promise((resolve, reject) => {
		terminal.on('exit', (code) => {
			if (code <= 0) {
				resolve({ status: true });
			} else {
				gaioRedis.pub.publish(publishId, '🚀 Error! 🐍');
				gaioRedis.pub.publish(publishId, error);
				gaioRedis.pub.publish(publishId, '🚀 Error! 🐍');
				reject({
					code: 'error',
					error: error.replace('password', '')
				});
			}
		});
	});
}
