import { dbGaio } from '@/db/db.gaio';
import type { SurveySaveRequestDTO } from '@/types/http/dtos/survey.dtos';
import { currentDatetime } from '@/utils/helpers';

type SaveSurveyUseCase = { userId: string } & SurveySaveRequestDTO;

export async function saveSurveyUseCase({
	data,
	status,
	token,
	userId,
	appId
}: SaveSurveyUseCase) {
	return dbGaio().upsert({
		table: 'survey',
		values: {
			appId,
			createdAt: currentDatetime().replace('T', ' ').replace('Z', ''),
			createdBy: userId,
			options: JSON.stringify(data),
			status,
			token,
			updatedAt: currentDatetime().replace('T', ' ').replace('Z', ''),
			updatedBy: userId
		},
		where: {
			appId,
			token
		}
	});
}
