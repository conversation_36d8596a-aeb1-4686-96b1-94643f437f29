import type { SurveyDataContent } from '@/components/survey/survey.core';
import type { BasicConnectionInfoDTO } from '@/db/db.service';
import { DbService } from '@/db/db.service';
import { HTTPException } from 'hono/http-exception';

type SubmitSurveyUseCase = {
	metaInfo: BasicConnectionInfoDTO;
	content: Array<SurveyDataContent>;
	uniqueIdField?: string;
	uniqueId?: unknown;
	databaseName: string;
	tableName: string;
};

type TableColumns = { columnName: string; dataType: string };

function refineFields(
	tableColumnsNames: TableColumns[],
	field: SurveyDataContent
) {
	if (typeof field.value !== 'number' && !field.value) return 'null';

	const column = tableColumnsNames.find(
		(column) => column.columnName === field.columnName
	);

	if (column.dataType.includes('Array')) {
		if (Array.isArray(field.value)) {
			return `[${field.value.map((value) => `'${value}'`).toString()}]`;
		}
	}

	if (column.dataType.includes('String')) {
		if (Array.isArray(field.value)) {
			return field.value
				.map((value) => {
					value = value.replace(/,/g, '');
					return value;
				})
				.toString();
		}

		// If column is number, converts to string or return 'null'
		if (
			['Float', 'Int', 'Decimal', 'UInt'].some((type) =>
				column.dataType.includes(type)
			)
		) {
			if (typeof field.value === 'number') return field.value;

			if (typeof field.value === 'string') {
				if (!isNaN(Number(field.value))) return Number(field.value);

				return 'null';
			}
		}

		return `'${field.value}'`;
	}
}

export async function submitSurveyUseCase({
	metaInfo,
	content,
	uniqueIdField,
	uniqueId,
	databaseName,
	tableName
}: SubmitSurveyUseCase) {
	const connection = await new DbService().connect({
		...metaInfo,
		sourceType: 'bucket'
	});

	const { data: tableColumns } = await connection.query<TableColumns>(
		`
			SELECT
				name AS columnName
				type AS dataType
			FROM system.columns
			WHERE
				database = '${databaseName}'
			AND
				table = '${tableName}'
		`
	);

	if (!Array.isArray(tableColumns)) {
		throw new HTTPException(404, {
			message: 'Table Columns not found'
		});
	}

	const tableColumnsNames = tableColumns.map((column) => column.columnName);

	const fields = content.filter(
		(content) =>
			content.type !== 'statement' &&
			tableColumnsNames.includes(content.columnName)
	);
	const values = fields.map((field) => refineFields(tableColumns, field));
	const fieldsColumns = fields.map((field) => field.columnName);

	if (fieldsColumns.length === 0) {
		throw new HTTPException(400, {
			cause: 'No valid columns found',
			message: 'No valid columns found'
		});
	}

	if (uniqueIdField && uniqueIdField !== 'none' && uniqueId) {
		fieldsColumns.push(uniqueIdField);
		values.push(`'${uniqueId}'`);
	}

	const query = await connection.query(
		`
			INSERT INTO ${databaseName}.${tableName}
				(${fieldsColumns.toString()})
			VALUES
				(${values.toString()})
		`
	);

	return query.data;
}
