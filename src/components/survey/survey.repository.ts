import { dbGaio } from '@/db/db.gaio';
import type { SurveyEntity } from '@/db/entities';

async function updateSurveyStatusByToken({
	status,
	token
}: Pick<SurveyEntity, 'status' | 'token'>) {
	return await dbGaio().exec(
		`
			ALTER TABLE survey
			UPDATE
				status = {status: String}
			WHERE
				token = {token: String}
		`,
		{
			params: {
				status,
				token
			}
		}
	);
}

async function listAllSurveyByAppId(appId: string) {
	return await dbGaio().findAll({
		table: 'survey',
		where: { appId }
	});
}

async function getSurveyByToken(token: string) {
	return await dbGaio().findOne({
		table: 'survey',
		where: { token }
	});
}

async function removeSurveyByToken(token: string) {
	return await dbGaio().delete({
		table: 'survey',
		where: { token }
	});
}

async function removeAllSurveyByAppId(appId: string) {
	return await dbGaio().delete({
		table: 'survey',
		where: { appId }
	});
}

export default {
	getSurveyByToken,
	listAllSurveyByAppId,
	removeSurveyByToken,
	updateSurveyStatusByToken,
	removeAllSurveyByAppId
};
