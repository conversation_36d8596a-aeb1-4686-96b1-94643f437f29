import {
	getSurveyUseCase,
	listAllSurveyUseCase,
	removeSurveyUseCase,
	updateSurveyStatusUseCase
} from '@/components/survey/survey.core';
import { saveSurveyUseCase } from '@/components/survey/use-cases/survey.save';
import { submitSurveyUseCase } from '@/components/survey/use-cases/survey.submit';
import type { BasicConnectionInfoDTO } from '@/db/db.service';
import jwtGuard from '@/server/middleware/jwt.guard';
import { readBody } from '@/server/middleware/readBody';
import type {
	SurveySaveRequestDTO,
	SurveySubmitRequestDTO,
	SurveyUpdateStatusRequestDTO
} from '@/types/http/dtos/survey.dtos';
import { Hono } from 'hono';

const app = new Hono()
	.post('/save', jwtGuard().isAuth, async (c) => {
		try {
			const { data, token, user, appId, status } =
				await readBody<SurveySaveRequestDTO>(c);
			const { userId } = user;

			const results = await saveSurveyUseCase({
				appId,
				data,
				status,
				token,
				userId
			});

			return c.json(
				{
					data: results,
					message: 'Survey saved',
					timestamp: new Date().toISOString()
				},
				{ status: 200 }
			);
		} catch (err) {
			return c.json(
				{
					error: err.message,
					message: 'Internal server error',
					timestamp: new Date().toISOString()
				},
				{ status: 500 }
			);
		}
	})

	.post('/update-status', jwtGuard().isAuth, async (c) => {
		try {
			const { token, status } = await readBody<SurveyUpdateStatusRequestDTO>(c);

			await updateSurveyStatusUseCase({ status, token });

			return c.json(
				{
					message: 'Survey status updated',
					timestamp: new Date().toISOString()
				},
				{ status: 200 }
			);
		} catch (err) {
			return c.json(
				{
					error: err.message,
					message: 'Internal server error',
					timestamp: new Date().toISOString()
				},
				{ status: 500 }
			);
		}
	})

	.post('/submit', async (c) => {
		try {
			const {
				appId,
				repoId,
				content,
				databaseName,
				tableName,
				uniqueId,
				uniqueIdField
			} = await readBody<SurveySubmitRequestDTO>(c);

			const metaInfo: BasicConnectionInfoDTO = {
				appId,
				repoId
			};

			const results = await submitSurveyUseCase({
				content,
				databaseName,
				metaInfo,
				tableName,
				uniqueId,
				uniqueIdField
			});

			return c.json(
				{
					data: results,
					message: 'Survey submitted',
					timestamp: new Date().toISOString()
				},
				{ status: 200 }
			);
		} catch (err) {
			return c.json(
				{
					error: err.message,
					message: 'Internal server error',
					timestamp: new Date().toISOString()
				},
				{ status: 500 }
			);
		}
	})

	.get('/list-all/:appId', jwtGuard().isAuth, async (c) => {
		try {
			const { appId } = c.req.param();

			const { data } = await listAllSurveyUseCase(appId);

			return c.json(
				{
					data,
					message: 'List of surveys',
					timestamp: new Date().toISOString()
				},
				{ status: 200 }
			);
		} catch (err) {
			return c.json(
				{
					error: err.message,
					message: 'Internal server error',
					timestamp: new Date().toISOString()
				},
				{ status: 500 }
			);
		}
	})

	.get('/load/:token', async (c) => {
		try {
			const { token } = c.req.param();

			const survey = await getSurveyUseCase(token);

			return c.json(
				{
					data: survey,
					message: 'Survey loaded',
					timestamp: new Date().toISOString
				},
				{ status: 200 }
			);
		} catch (err) {
			return c.json(
				{
					error: err.message,
					message: 'Internal server error',
					timestamp: new Date().toISOString()
				},
				{ status: 500 }
			);
		}
	})

	.delete('/remove/:token', jwtGuard().isAuth, async (c) => {
		try {
			const { token } = c.req.param();

			await removeSurveyUseCase(token);

			return c.json(
				{
					message: 'Survey removed',
					timestamp: new Date().toISOString()
				},
				{ status: 200 }
			);
		} catch (err) {
			return c.json(
				{
					error: err.message,
					message: 'Internal server error',
					timestamp: new Date().toISOString()
				},
				{
					status: 500
				}
			);
		}
	});

export default app;
