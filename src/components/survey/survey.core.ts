import SurveyRepository from '@/components/survey/survey.repository';
import type { SurveyUpdateStatusRequestDTO } from '@/types/http/dtos/survey.dtos';

export type SurveyDataContent = {
	id: string;
	type: string;
	title: string;
	options: Array<unknown>;
	required: boolean;
	showImage: true;
	columnName: string;
	description: string;
	value?: string | Array<string>;
};

export type SurveyData = {
	client: string;
	content: Array<SurveyDataContent>;
	databaseName: string;
	endOptions: {
		params: Array<string>;
		message: string;
	};
	endType: string;
	label: string;
	lang: object;
	primaryColor: string;
	repoId: number;
	secondaryColor: string;
	sourceType: string;
	tableName: string;
	title: string;
	token: string;
	type: string;
};

export async function updateSurveyStatusUseCase({
	status,
	token
}: SurveyUpdateStatusRequestDTO) {
	return await SurveyRepository.updateSurveyStatusByToken({ status, token });
}

export async function listAllSurveyUseCase(appId: string) {
	return await SurveyRepository.listAllSurveyByAppId(appId);
}

export async function getSurveyUseCase(token: string) {
	return await SurveyRepository.getSurveyByToken(token);
}

export async function removeSurveyUseCase(token: string) {
	return await SurveyRepository.removeSurveyByToken(token);
}
