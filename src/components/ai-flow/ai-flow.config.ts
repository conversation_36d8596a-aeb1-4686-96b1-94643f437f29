export const AI_CONFIG = {
	// AI SDK Model Configuration
	MODELS: {
		DEFAULT: 'gpt-4o',
		STREAMING: 'gpt-4o',
		STRUCTURED: 'gpt-4o-mini'
	},

	DEFAULT_SETTINGS: {
		tableSample: true,
		analytics: true,
		sampleLimit: 10,
		maxTokens: 4096,
		temperature: 0.7,
		// Always use structured responses by default
		responseFormat: 'json_schema'
	},

	ERROR_MESSAGES: {
		AI_MODEL_NOT_FOUND: (id: string) => `AI model with ID ${id} not found`,
		USER_MESSAGE_REQUIRED: 'User message is required',
		AI_COMPLETION_FAILED: (error: string) => `AI completion failed: ${error}`,
		THREAD_NOT_FOUND: (threadId: string) =>
			`Thread with ID ${threadId} not found`,
		INVALID_AGENT_TYPE: (type: string) => `Invalid agent type: ${type}`,
		SCHEMA_NOT_FOUND: (schema: string) => `Schema ${schema} not found`
	}
} as const;
