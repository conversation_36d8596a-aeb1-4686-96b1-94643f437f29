import { type Context, Hono } from 'hono';
import jwtGuard from '@/server/middleware/jwt.guard';
import { readBody } from '@/server/middleware/readBody';
import { talkWithAI as talkWithAIUseCase } from './use-cases/talk-with-ai.use-case';
import { improveAnalysis as improveAnalysisUseCase } from './use-cases/improve-analysis.use-case';
import { threadTalk as threadTalkUseCase } from './use-cases/thread-talk.use-case';
import type { TalkData, ImproveData } from './ai-flow.types';

const app = new Hono()

	.post('/talk', jwtGuard().isAuth, async (c: Context) => {
		try {
			const { talkData } = await readBody<{ talkData: TalkData }>(c);
			const userId = c.get('user').userId;

			if (!userId) {
				return c.json({ error: 'User ID is required' }, 400);
			}

			// Schema is now automatically detected based on user message and agent type
			const result = await talkWithAIUseCase(talkData, userId);
			return c.json(result);
		} catch (error) {
			console.error('[AI-FLOW] Error in /talk endpoint:', error);
			return c.json(
				{
					error: true,
					message:
						error instanceof Error ? error.message : 'Internal server error'
				},
				500
			);
		}
	})

	.post('/improve', jwtGuard().isAuth, async (c: Context) => {
		try {
			const { talkData, improveData } = await readBody<{
				talkData: TalkData;
				improveData: ImproveData;
			}>(c);
			const userId = c.get('user').userId;

			if (!userId) {
				return c.json({ error: 'User ID is required' }, 400);
			}

			// Schema is automatically detected based on agent type and message content
			const result = await improveAnalysisUseCase(
				talkData,
				improveData,
				userId
			);
			return c.json(result);
		} catch (error) {
			console.error('[AI-FLOW] Error in /improve endpoint:', error);
			return c.json(
				{
					error: true,
					message:
						error instanceof Error ? error.message : 'Internal server error'
				},
				500
			);
		}
	})

	.get(
		'/thread/:threadId/:aiManagerId',
		jwtGuard().isAuth,
		async (c: Context) => {
			try {
				const { threadId } = c.req.param();
				const result = await threadTalkUseCase({ threadId });
				return c.json(result);
			} catch (error) {
				console.error('[AI-FLOW] Error in /thread endpoint:', error);
				return c.json(
					{
						error: true,
						message:
							error instanceof Error ? error.message : 'Internal server error'
					},
					500
				);
			}
		}
	);

export default app;
