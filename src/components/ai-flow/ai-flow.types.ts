import type { AiMessageType } from '@gaio/shared/types';
import { z } from 'zod';

export type TalkData = {
	aiManagerId: string;
	threadId?: string;
	userMessage?: string;
	appId: string;
	repoId: string;
	tables?: string[];
	sharedData?: {
		tables: string[];
	};
	threadName?: string;
	messages?: AiMessageType[];
};

export type ImproveData = {
	type: 'REPORT' | 'MASTER';
	message: string;
};

export type TableMetadata = {
	table_description: string;
	table_columns: Array<{
		title: string;
		column_name: string;
		column_description: string;
		column_type: string;
		column_stats?: Record<string, unknown>;
	}>;
	table_sample: Record<string, unknown>[];
};

export type AIResponse = {
	content: unknown;
	messageId: string;
	tokens: {
		prompt: number;
		completion: number;
		total: number;
	};
	provider: string;
	threadId: string;
	sharedData?: Record<string, unknown>;
	isStructured?: boolean;
};

export type AIError = {
	error: true;
	message: string;
};

export type ThreadContext = {
	threadId: string;
	messages: AiMessageType[];
	metadata: {
		appId: string;
		threadName: string;
		createdAt: string;
		updatedAt: string;
	};
};

// Supported AI providers
export type SupportedProvider =
	| 'openai'
	| 'anthropic'
	| 'azure'
	| 'google'
	| 'groq'
	| 'mistral'
	| 'deepseek'
	| 'ollama';

export type AIClientConfig = {
	apiKey: string;
	model?: string;
	maxTokens?: number;
	temperature?: number;
	provider?: SupportedProvider;
	baseURL?: string; // For custom endpoints like Ollama
	region?: string; // For Azure
	resourceName?: string; // For Azure
	apiVersion?: string; // For Azure
};

export type AgentDelegationResponse = {
	type: 'agentNeeded';
	agentId: string;
	agentType: 'REPORT' | 'MASTER';
	reason: string;
};

export const CommonSchemas = {
	MASTER: z
		.object({
			type: z
				.enum(['agentNeeded', 'noAgent'])
				.describe(
					'Indicates whether to use another agent or provide direct response.'
				),
			message: z
				.string()
				.optional()
				.describe(
					'Message to the user - required when type is noAgent, omit when agentNeeded.'
				),
			agentId: z
				.string()
				.optional()
				.describe(
					'The ID of the agent to delegate to - required when type is agentNeeded, omit when noAgent.'
				)
		})
		.describe(
			'Response from MASTER agent - either direct response or agent delegation'
		),
	REPORT: z
		.object({
			type: z.literal('report'),
			results: z.array(
				z
					.object({
						id: z
							.string()
							.describe('A random string identifier for the report.'),
						query: z.string().describe('The ClickHouse query to be executed.'),
						reportType: z
							.enum([
								'line',
								'stackedBar',
								'stackedColumn',
								'area',
								'pie',
								'donut',
								'bar',
								'column',
								'table',
								'card'
							])
							.describe('The type of report representation for the query.'),
						message: z
							.string()
							.describe(
								'A description of what the query aims to show to the user.'
							),
						errorDescription: z
							.string()
							.describe(
								'A description of what the query aims to show to the user.'
							)
							.default(''),
						title: z.string().describe('The title of the query.')
					})
					.strict() // Ensures no additional properties
			)
		})
		.strict()
} as const;
