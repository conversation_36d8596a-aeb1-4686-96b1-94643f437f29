# AI-Flow Component Rules & Architecture

## Overview
The ai-flow component is a sophisticated **AI conversation orchestration system** for data analytics and business intelligence. It implements a **multi-agent AI architecture** that intelligently routes user requests between specialized AI agents based on context and intent.

## Core Architecture

### Multi-Agent System
- **MASTER Agent**: Router/orchestrator that analyzes user requests and determines delegation
  - Decides between direct response vs. agent delegation
  - Handles general data analysis questions
  - Guards against out-of-scope requests (health, legal, politics, etc.)

- **REPORT Agent**: Specialized SQL/ClickHouse expert for dashboard generation
  - Generates ClickHouse SQL queries for data visualization
  - Creates reports with different chart types (line, bar, pie, table, etc.)
  - Follows strict business rules for data aggregation and limits

### Key Components

#### Configuration (`ai-flow.config.ts`)
- Model settings (GPT-4o variants)
- Default parameters (tokens, temperature, response format)
- Error message constants
- **Always uses structured JSON responses by default**

#### Instructions (`ai-flow.instructions.ts`)
- **Detailed system prompts** for each agent type
- Business rules for SQL generation (limits, ordering, date handling)
- Report type specifications and column requirements
- **Strict JSON schema enforcement**

#### Types (`ai-flow.types.ts`)
- TypeScript interfaces for all AI interactions
- Zod schemas for response validation
- Thread context and metadata structures
- **Strong typing for AI responses**

#### Services
- **AI Client Service**: OpenAI SDK integration with structured response generation
- **Conversation Service**: Thread management, message history, persistence
- **Table Metadata Service**: Database schema and statistics enrichment
- **Schema Detection Service**: Automatic schema selection based on agent type

#### Use Cases
- **Talk with AI**: Main conversation flow with agent delegation
- **Improve Analysis**: Refinement of existing AI responses
- **Thread Talk**: Conversation history retrieval

## Key Patterns & Rules

### Request Flow
1. User message → MASTER agent evaluation
2. MASTER decides: direct response OR delegate to specialized agent
3. If delegation → Route to REPORT agent with context
4. Generate structured response with appropriate schema
5. Save conversation to thread history

### Data Context Management
- **Automatic table metadata injection** for new tables in conversation
- **Incremental sharing** - only sends metadata for new tables not previously shared
- **Sample data integration** with configurable limits
- **Column statistics** for numeric fields when analytics enabled

### Response Structure
- **Always JSON structured responses** using Zod schemas
- **Agent-specific output formats**:
  - MASTER: `{type: 'agentNeeded'|'noAgent', message?, agentId?}`
  - REPORT: `{type: 'report', results: [{id, query, reportType, message, title}]}`

### Error Handling
- **Graceful degradation** with structured error responses
- **Validation at multiple layers** (input, schema, AI response)
- **Comprehensive logging** for debugging

### Security & Validation
- **JWT authentication** on all endpoints
- **Input validation** using readBody middleware
- **Schema validation** for all AI responses
- **Scope limitations** - strict guard rails against non-data topics

## Development Guidelines

### Code Quality Rules
- **Clean, minimal code** - less is more
- **Strong typing** throughout with TypeScript
- **Consistent error handling** patterns
- **Modular service architecture**

### AI Integration Best Practices
- **Always use structured responses** with Zod schemas
- **Implement proper token management** and usage tracking
- **Handle streaming responses** for better UX
- **Validate AI outputs** before returning to client

### Database Integration
- **ClickHouse-specific** SQL generation rules
- **Metadata-driven** query building
- **Sample data limits** (10 rows for tables, 30 for charts)
- **Automatic ordering** rules based on column types

### Thread Management
- **Persistent conversation history** with unique thread IDs
- **Metadata tracking** (app context, creation time, names)
- **Incremental context building** - don't repeat shared data

## File Organization
```
ai-flow/
├── ai-flow.config.ts         # Configuration constants
├── ai-flow.instructions.ts   # AI system prompts
├── ai-flow.types.ts          # TypeScript types & Zod schemas
├── ai-flow.routes.ts         # HTTP endpoints
├── services/                 # Core business logic
│   ├── ai-client.service.ts  # OpenAI SDK integration
│   ├── conversation.service.ts # Thread management
│   ├── table-metadata.service.ts # Database context
│   └── schema-detection.service.ts # Response schemas
└── use-cases/                # Application workflows
    ├── talk-with-ai.use-case.ts
    ├── improve-analysis.use-case.ts
    └── thread-talk.use-case.ts
```

## Important Notes
- This is a **data analytics focused AI system** - not general purpose chat
- **Structured responses are mandatory** - never return raw text
- **Agent delegation is automatic** based on MASTER agent analysis
- **Thread persistence** maintains conversation context across requests
- **Table metadata enrichment** provides AI with database schema context 