import { createAIClientForManager } from '../services/ai-client.service';
import {
	saveThread,
	createUserMessage,
	createAssistantMessage,
	getOrCreateThread
} from '../services/conversation.service';
import { getSchemaForAgent } from '../services/schema-detection.service';
import type {
	TalkData,
	ImproveData,
	AIResponse,
	AIError
} from '../ai-flow.types';
import { AI_CONFIG } from '../ai-flow.config';
import { AI_INSTRUCTIONS } from '../ai-flow.instructions';

export async function improveAnalysis(
	talkData: TalkData,
	improveData: ImproveData,
	userId: string
): Promise<AIResponse | AIError> {
	try {
		validateImproveInput(improveData);

		// Get AI client
		const aiClient = await createAIClientForManager(talkData.aiManagerId);

		// Get instruction for the specific agent type
		const instruction = getInstructionForAgentType(improveData.type);

		// Create message for improvement
		const messages = [createUserMessage(improveData.message)];

		const threadContext = await getOrCreateThread(talkData);

		// add message at the beginning of the messages
		messages.unshift(...(threadContext.messages || []));

		// Get schema for the agent type
		const { schema } = getSchemaForAgent(improveData.type);

		// Generate response with schema
		const response = await aiClient.generateResponse(
			messages,
			instruction,
			talkData.threadId,
			{
				responseFormat: 'json_schema',
				jsonSchema: schema
			}
		);

		return response;
	} catch (error) {
		console.log('[ImproveAnalysis] Error:', error);
		return {
			error: true,
			message: error instanceof Error ? error.message : String(error)
		};
	}
}

function validateImproveInput(improveData: ImproveData): void {
	if (!improveData.message) {
		throw new Error('Improvement message is required');
	}

	if (!improveData.type) {
		throw new Error('Agent type is required for improvement');
	}
}

function getInstructionForAgentType(agentType: 'REPORT' | 'MASTER'): string {
	const instruction = AI_INSTRUCTIONS[agentType];

	if (!instruction) {
		throw new Error(AI_CONFIG.ERROR_MESSAGES.INVALID_AGENT_TYPE(agentType));
	}

	return instruction;
}

async function saveThreadWithImprovement(
	talkData: TalkData,
	messages: ReturnType<typeof createUserMessage>[],
	response: AIResponse,
	userId: string
): Promise<void> {
	const allMessages = [...messages, createAssistantMessage(response.content)];

	if (!talkData.threadId) {
		throw new Error('Thread ID is required for saving improvements');
	}

	await saveThread(talkData.threadId, talkData, allMessages, userId);
}
