import { getThreadHistory } from '../services/conversation.service';
import type { AiMessageType } from '@gaio/shared/types';

export type ThreadTalkData = {
	threadId: string;
};

export type ThreadTalkError = {
	error: true;
	message: string;
};

export async function threadTalk(
	threadData: ThreadTalkData
): Promise<AiMessageType[] | ThreadTalkError> {
	try {
		validateThreadInput(threadData);

		// Get thread messages from database
		const messages = await getThreadHistory(threadData.threadId);

		return messages;
	} catch (error) {
		console.log('[ThreadTalk] Error:', error);
		return {
			error: true,
			message: error instanceof Error ? error.message : String(error)
		};
	}
}

function validateThreadInput(threadData: ThreadTalkData): void {
	if (!threadData.threadId) {
		throw new Error('Thread ID is required');
	}

	if (
		typeof threadData.threadId !== 'string' ||
		threadData.threadId.trim() === ''
	) {
		throw new Error('Thread ID must be a non-empty string');
	}
}
