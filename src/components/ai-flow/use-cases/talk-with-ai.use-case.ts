import {
	type createAIClient,
	createAIClientForManager
} from '../services/ai-client.service';
import {
	getOrCreateThread,
	saveThread,
	createUserMessage,
	createAssistantMessage,
	createMetadataMessage,
	getNewTablesToShare,
	updateSharedTables
} from '../services/conversation.service';
import { getTablesMetadata } from '../services/table-metadata.service';
import { getSchemaForAgent } from '../services/schema-detection.service';
import type {
	TalkData,
	AIResponse,
	AIError,
	ThreadContext
} from '../ai-flow.types';
import type { AiMessageType } from '@gaio/shared/types';
import { AI_CONFIG } from '../ai-flow.config';
import { AI_INSTRUCTIONS } from '../ai-flow.instructions';

export async function talkWithAI(
	talkData: TalkData,
	userId: string
): Promise<AIResponse | AIError> {
	try {
		validateInput(talkData);

		// Get AI client
		const aiClient = await createAIClientForManager(talkData.aiManagerId);

		// Get or create thread context
		const threadContext = await getOrCreateThread(talkData);

		// console.log('nummber of messages', threadContext.messages.length);
		// console.log('reuse messages', threadContext.messages);

		// Prepare messages with history
		const messages = await prepareMessages(talkData, threadContext);

		// Get schema for MASTER agent
		const { schema } = getSchemaForAgent('MASTER');

		// Generate AI response with schema
		let response = await aiClient.generateResponse(
			messages,
			AI_INSTRUCTIONS.MASTER,
			threadContext.threadId,
			{
				responseFormat: 'json_schema',
				jsonSchema: schema
			}
		);

		// Handle agent delegation if needed
		if (shouldDelegateToAgent(response)) {
			response = await handleAgentDelegation(
				talkData,
				messages,
				response,
				aiClient
			);
		}

		// Save conversation to database
		const allMessages = [...messages, createAssistantMessage(response.content)];

		// console.log('save messages', allMessages);

		await saveThread(threadContext.threadId, talkData, allMessages, userId);

		return {
			...response,
			threadId: threadContext.threadId,
			sharedData: talkData.sharedData
		};
	} catch (error) {
		console.log('[TalkWithAIUseCase] Error:', error);
		return {
			error: true,
			message: error instanceof Error ? error.message : String(error)
		};
	}
}

function validateInput(talkData: TalkData) {
	if (!talkData.userMessage) {
		throw new Error(AI_CONFIG.ERROR_MESSAGES.USER_MESSAGE_REQUIRED);
	}
}

async function prepareMessages(
	talkData: TalkData,
	threadContext: ThreadContext
) {
	const messages: AiMessageType[] = [];

	// Add conversation history
	messages.push(...threadContext.messages);

	// Add table metadata if needed (only for new tables)
	const newTables = getNewTablesToShare(talkData);

	if (newTables.length > 0 && messages.length === 0) {
		// only sent metadata at the first time
		// todo: if user request sent metadata again, send it again
		const metadata = await getTablesMetadata({
			...talkData,
			tables: newTables
		});

		if (metadata) {
			messages.push(createMetadataMessage(metadata));
			updateSharedTables(talkData, newTables);
		}
	}

	// Add current user message
	if (talkData.userMessage) {
		messages.push(createUserMessage(talkData.userMessage));
	}

	return messages;
}

function shouldDelegateToAgent(response: AIResponse): boolean {
	try {
		const content =
			typeof response.content === 'string'
				? JSON.parse(response.content)
				: response.content;
		return content?.type === 'agentNeeded';
	} catch {
		return false;
	}
}

async function handleAgentDelegation(
	talkData: TalkData,
	messages: AiMessageType[],
	response: AIResponse,
	aiClient: ReturnType<typeof createAIClient>
): Promise<AIResponse> {
	try {
		const content =
			typeof response.content === 'string'
				? JSON.parse(response.content)
				: response.content;

		// Validate the content structure
		if (!content?.agentId) {
			throw new Error('Invalid agent delegation response: missing agentId');
		}

		const agentType = content.agentId as 'REPORT' | 'MASTER';

		// Validate agent type
		if (!['REPORT', 'MASTER'].includes(agentType)) {
			throw new Error(`Invalid agent type: ${agentType}`);
		}

		const instruction =
			AI_INSTRUCTIONS[agentType as keyof typeof AI_INSTRUCTIONS];

		if (!instruction) {
			throw new Error(AI_CONFIG.ERROR_MESSAGES.INVALID_AGENT_TYPE(agentType));
		}

		if (messages.length > 0) {
			return await aiClient.generateResponse(
				messages,
				AI_INSTRUCTIONS.REPORT,
				response.threadId,
				{
					responseFormat: 'json_schema',
					jsonSchema: getSchemaForAgent(agentType).schema
				}
			);
		}

		return response;
	} catch (error) {
		console.error('[TalkWithAIUseCase] Agent delegation error:', error);
		return response;
	}
}
