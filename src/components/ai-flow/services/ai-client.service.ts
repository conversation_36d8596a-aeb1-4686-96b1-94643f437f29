import { generateText, streamText, generateObject } from 'ai';
import { createOpenAI } from '@ai-sdk/openai';
import { createAnthropic } from '@ai-sdk/anthropic';
import { createAzure } from '@ai-sdk/azure';
import { createGoogleGenerativeAI } from '@ai-sdk/google';
import { createGroq } from '@ai-sdk/groq';
import { createMistral } from '@ai-sdk/mistral';
import { createDeepSeek } from '@ai-sdk/deepseek';
import { ollama } from 'ollama-ai-provider';
import { z } from 'zod';
import { v4 as uuidv4 } from 'uuid';
import logger from '@/utils/logger';
import type { AiMessageType } from '@gaio/shared/types';
import type {
	AIResponse,
	AIClientConfig,
	AgentDelegationResponse
} from '../ai-flow.types';
import { AI_CONFIG } from '../ai-flow.config';
import { getAiModelById } from '@/components/ai-manager/ai-manager.core';

// Import the types from the shared types file
import type { SupportedProvider } from '../ai-flow.types';
import { getId } from '@gaio/shared/utils';

// Zod schema for agent delegation responses
const AgentDelegationSchema = z.object({
	type: z.literal('agentNeeded'),
	agentType: z.enum(['REPORT']),
	reason: z.string()
});

// Provider factory function
function createProviderInstance(config: AIClientConfig) {
	switch (config.provider) {
		case 'openai':
			return createOpenAI({
				apiKey: config.apiKey,
				...(config.baseURL && { baseURL: config.baseURL })
			});

		case 'anthropic':
			return createAnthropic({
				apiKey: config.apiKey
			});

		case 'azure':
			return createAzure({
				apiKey: config.apiKey,
				apiVersion: config.apiVersion || '2024-02-01',
				resourceName: config.resourceName,
				...(config.baseURL && { baseURL: config.baseURL })
			});

		case 'google':
			return createGoogleGenerativeAI({
				apiKey: config.apiKey
			});

		case 'groq':
			return createGroq({
				apiKey: config.apiKey
			});

		case 'mistral':
			return createMistral({
				apiKey: config.apiKey
			});

		case 'deepseek':
			return createDeepSeek({
				apiKey: config.apiKey
			});

		case 'ollama':
			// Ollama requires baseURL since it runs locally
			return ollama;

		default:
			throw new Error(`Unsupported provider: ${config.provider}`);
	}
}

export async function createAIClientForManager(aiManagerId: string) {
	const aiManagerData = await getAiModelById(aiManagerId);

	if (!aiManagerData) {
		throw new Error(AI_CONFIG.ERROR_MESSAGES.AI_MODEL_NOT_FOUND(aiManagerId));
	}

	const config: AIClientConfig = {
		provider: (aiManagerData.credentials?.supplier ||
			'openai') as SupportedProvider,
		apiKey: aiManagerData.credentials?.apiKey,
		model: aiManagerData.credentials?.model || AI_CONFIG.MODELS.DEFAULT,
		maxTokens: AI_CONFIG.DEFAULT_SETTINGS.maxTokens,
		temperature: AI_CONFIG.DEFAULT_SETTINGS.temperature,
		baseURL: aiManagerData.credentials?.baseURL,
		region: aiManagerData.credentials?.region,
		resourceName: aiManagerData.credentials?.resourceName
	};

	return createAIClient(config);
}

// Factory function to create AI client
export function createAIClient(config: AIClientConfig) {
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	const provider = createProviderInstance(config) as any; // AI SDK providers share compatible interfaces

	const formatMessages = (messages: AiMessageType[]) => {
		return messages.map((msg) => ({
			role: msg.role as 'user' | 'assistant' | 'system',
			content: msg.content
		}));
	};

	return {
		async generateResponse(
			messages: AiMessageType[],
			systemPrompt: string,
			threadId?: string,
			options?: {
				responseFormat?: 'text' | 'json_schema';
				jsonSchema?: z.ZodSchema<unknown>;
			}
		): Promise<AIResponse> {
			try {
				// Use structured response if JSON schema is provided
				if (options?.responseFormat === 'json_schema' && options.jsonSchema) {
					const structuredResult = await generateStructuredResponse(
						messages,
						systemPrompt,
						options.jsonSchema,
						threadId,
						provider,
						config
					);

					return {
						...structuredResult,
						isStructured: true
					};
				}

				// Default text response
				const result = await generateText({
					model: provider(config.model || AI_CONFIG.MODELS.DEFAULT),
					messages: formatMessages(messages),
					system: systemPrompt,
					maxTokens: config.maxTokens || AI_CONFIG.DEFAULT_SETTINGS.maxTokens,
					temperature:
						config.temperature || AI_CONFIG.DEFAULT_SETTINGS.temperature
				});

				return {
					content: result.text,
					tokens: {
						prompt: result.usage.promptTokens,
						completion: result.usage.completionTokens,
						total: result.usage.totalTokens
					},
					provider: 'openai',
					threadId: threadId || uuidv4(),
					isStructured: false
				};
			} catch (error) {
				console.log('error', error.message);
				logger.error('AI SDK completion error:', error);
				throw new Error(
					AI_CONFIG.ERROR_MESSAGES.AI_COMPLETION_FAILED(
						error instanceof Error ? error.message : String(error)
					)
				);
			}
		},

		async generateStructuredResponse<T>(
			messages: AiMessageType[],
			systemPrompt: string,
			schema: z.ZodSchema<T>,
			threadId?: string
		): Promise<AIResponse & { object: T }> {
			return generateStructuredResponse(
				messages,
				systemPrompt,
				schema,
				threadId,
				provider,
				config
			);
		},

		async *streamResponse(
			messages: AiMessageType[],
			systemPrompt: string,
			threadId?: string
		): AsyncGenerator<string, AIResponse, unknown> {
			try {
				const result = await streamText({
					model: provider(config.model || AI_CONFIG.MODELS.STREAMING),
					messages: formatMessages(messages),
					system: systemPrompt,
					maxTokens: config.maxTokens || AI_CONFIG.DEFAULT_SETTINGS.maxTokens,
					temperature:
						config.temperature || AI_CONFIG.DEFAULT_SETTINGS.temperature
				});

				let fullText = '';

				for await (const delta of result.textStream) {
					fullText += delta;
					yield delta;
				}

				const finalResult = await result.finishReason;
				const usage = await result.usage;

				return {
					content: fullText,
					tokens: {
						prompt: usage.promptTokens,
						completion: usage.completionTokens,
						total: usage.totalTokens
					},
					provider: 'openai',
					threadId: threadId || uuidv4()
				};
			} catch (error) {
				logger.error('AI SDK streaming error:', error);
				throw new Error(
					AI_CONFIG.ERROR_MESSAGES.AI_COMPLETION_FAILED(
						error instanceof Error ? error.message : String(error)
					)
				);
			}
		},

		async checkForAgentDelegation(
			messages: AiMessageType[],
			systemPrompt: string
		): Promise<AgentDelegationResponse | null> {
			try {
				const result = await generateStructuredResponse(
					messages,
					`${systemPrompt}\n\nIf this request needs delegation to a specialized agent, respond with the delegation object. Otherwise, respond with null.`,
					z.union([AgentDelegationSchema, z.null()]),
					undefined,
					provider,
					config
				);

				return result.object as AgentDelegationResponse | null;
			} catch (error) {
				logger.error('Agent delegation check failed:', error);
				return null;
			}
		}
	};
}

// Helper function for structured response generation
async function generateStructuredResponse<T>(
	messages: AiMessageType[],
	systemPrompt: string,
	schema: z.ZodSchema<T>,
	threadId: string | undefined,
	provider: ReturnType<typeof createOpenAI>,
	config: AIClientConfig
): Promise<AIResponse & { object: T }> {
	try {
		const result = await generateObject({
			model: provider(config.model || AI_CONFIG.MODELS.STRUCTURED),
			messages: messages.map((msg) => ({
				role: msg.role as 'user' | 'assistant' | 'system',
				content: msg.content
			})),
			system: systemPrompt,
			schema,
			maxTokens: config.maxTokens || AI_CONFIG.DEFAULT_SETTINGS.maxTokens,
			temperature: config.temperature || AI_CONFIG.DEFAULT_SETTINGS.temperature
		});

		return {
			content: result.object,
			messageId: getId(),
			object: result.object,
			tokens: {
				prompt: result.usage.promptTokens,
				completion: result.usage.completionTokens,
				total: result.usage.totalTokens
			},
			provider: 'openai',
			threadId: threadId || uuidv4()
		};
	} catch (error) {
		logger.error('AI SDK structured generation error:', error);
		throw new Error(
			AI_CONFIG.ERROR_MESSAGES.AI_COMPLETION_FAILED(
				error instanceof Error ? error.message : String(error)
			)
		);
	}
}
