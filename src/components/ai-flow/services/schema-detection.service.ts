import { CommonSchemas } from '../ai-flow.types';
import type { z } from 'zod';

/**
 * Get the appropriate schema based on agent type
 * Simple direct mapping: MASTER → ANALYSIS_RESPONSE, REPORT → REPORT_RESPONSE, etc.
 */
export function getSchemaForAgent(agentType: 'MASTER' | 'REPORT'): {
	schema: z.ZodSchema<unknown>;
	schemaName: keyof typeof CommonSchemas;
} {
	return {
		schema: CommonSchemas[agentType],
		schemaName: agentType
	};
}
