import MetaRepository from '@/components/meta/meta.repository';
import SettingsRepository from '@/components/setting/setting.repository';
import { tableRows } from '@/components/table/use-cases/table.rows';
import { tableStats } from '@/components/table/use-cases/table.stats';
import { tableEmpty } from '@/components/table/use-cases/table.empty';
import { getBucketNameFromAppId } from '@gaio/shared/utils';
import type { TalkData, TableMetadata } from '../ai-flow.types';
import type {
	AiManagerConfigType,
	CommonBuilderTaskType,
	ConnectionResultType
} from '@gaio/shared/types';

export async function getTablesMetadata(
	talkData: TalkData
): Promise<Record<string, TableMetadata> | undefined> {
	if (!talkData.tables?.length) return undefined;

	const aiManagerSettings = await getAIManagerSettings();
	const tablesMetadata: Record<string, TableMetadata> = {};

	for (const table of talkData.tables) {
		const metadata = await getTableMetadata(talkData, table, aiManagerSettings);
		if (metadata) {
			tablesMetadata[table] = metadata;
		}
	}

	return tablesMetadata;
}

async function getAIManagerSettings() {
	return (await SettingsRepository.getSetting('aiManagerSettings')) as {
		options: AiManagerConfigType;
	};
}

async function getTableMetadata(
	talkData: TalkData,
	table: string,
	aiManagerSettings: { options: AiManagerConfigType } | null
): Promise<TableMetadata | undefined> {
	const metaData = await MetaRepository.getMetaByTableAndAppId(
		table,
		talkData.appId
	);

	if (!metaData?.fields) return undefined;

	const tableSample = aiManagerSettings?.options?.tableSample
		? await getTableSample(talkData.appId, talkData.repoId, table)
		: [];

	const fields = await enrichFieldsWithStats(
		metaData.fields,
		talkData,
		table,
		aiManagerSettings
	);

	return {
		table_description: metaData.description,
		table_columns: fields
			.map((field) => ({
				title: field.title ? String(field.title) : '',
				column_name: field.columnName ? String(field.columnName) : '',
				column_description: field.description
					? String(field.description)
					: null,
				column_type: field.dataType ? String(field.dataType) : '',
				column_stats: field.columnStats
					? (field.columnStats as Record<string, unknown>)
					: {}
			}))
			.map((f) => {
				if (!f.column_description) {
					delete f.column_description;
				}
				return f;
			}),
		table_sample: tableSample
	};
}

async function enrichFieldsWithStats(
	fields: Record<string, unknown>[],
	talkData: TalkData,
	table: string,
	settings: { options: AiManagerConfigType } | null
) {
	if (!settings?.options?.analytics) return fields;

	for (const field of fields) {
		field.columnStats = await getColumnStats(
			talkData.appId,
			talkData.repoId,
			table,
			String(field.columnName),
			String(field.dataType)
		);
	}

	return fields;
}

async function getColumnStats(
	appId: string,
	repoId: string,
	tableName: string,
	columnName: string,
	dataType: string
) {
	try {
		const taskData = buildTaskData(appId, repoId, tableName, columnName);

		if (isNumericType(dataType)) {
			return await getNumericStats(taskData);
		}

		return await tableEmpty(taskData);
	} catch (error) {
		console.error('[TableMetadataService] Error in columnStats:', error);
		return {};
	}
}

function isNumericType(dataType: string): boolean {
	return (
		dataType.includes('Float') ||
		dataType.includes('Int') ||
		dataType.includes('Dec')
	);
}

function buildTaskData(
	appId: string,
	repoId: string,
	tableName: string,
	columnName: string
) {
	return {
		appId,
		databaseName: getBucketNameFromAppId(appId),
		tableName,
		repoId,
		columnName,
		sourceType: 'bucket',
		client: 'clickhouse'
	} as CommonBuilderTaskType;
}

async function getNumericStats(taskData: CommonBuilderTaskType) {
	const result = (await tableStats(taskData, [])) as ConnectionResultType;
	return result?.data?.[0] || {};
}

async function getTableSample(
	appId: string,
	repoId: string,
	tableName: string
) {
	const taskData = {
		appId,
		databaseName: getBucketNameFromAppId(appId),
		tableName,
		repoId,
		client: 'clickhouse',
		sourceType: 'bucket',
		schema: { limit: 10 }
	};

	const tableSample = (await tableRows(taskData, {
		params: []
	})) as ConnectionResultType;
	return tableSample.data || [];
}
