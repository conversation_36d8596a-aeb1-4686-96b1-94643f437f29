import { v4 as uuidv4 } from 'uuid';
import AiThreadRepository from '@/components/ai-thread/ai-thread.repository';
import type { AiMessageType, GenericType } from '@gaio/shared/types';
import type { TalkData, ThreadContext } from '../ai-flow.types';
import { getId } from '@gaio/shared/utils';

export async function getOrCreateThread(
	talkData: TalkData
): Promise<ThreadContext> {
	// If threadId exists, load from database
	if (talkData.threadId) {
		const threadData = await AiThreadRepository.getThreadMessages(
			talkData.threadId
		);

		if (threadData) {
			return {
				threadId: talkData.threadId,
				messages:
					threadData.messages.map((o: GenericType) => {
						return {
							role: o.isUser ? 'user' : 'assistant',
							messageId: o.messageId || getId(),
							content: o.isUser
								? o.content.message
								: typeof o.content === 'string'
									? o.content
									: JSON.stringify(o.content),
							metadata: o.metadata
						};
					}) || [],
				metadata: {
					appId: threadData.options?.appId || talkData.appId,
					threadName: threadData.options?.threadName || 'Conversation',
					createdAt: threadData.createdAt || new Date().toISOString(),
					updatedAt: threadData.updatedAt || new Date().toISOString()
				}
			};
		}
	}

	// Create new thread
	const newThreadId = uuidv4();
	const threadName =
		talkData.threadName ||
		talkData.userMessage?.substring(0, 50) ||
		'New Conversation';

	return {
		threadId: newThreadId,
		messages: [],
		metadata: {
			appId: talkData.appId,
			threadName,
			createdAt: new Date().toISOString(),
			updatedAt: new Date().toISOString()
		}
	};
}

export async function saveThread(
	threadId: string,
	talkData: TalkData,
	messages: AiMessageType[],
	userId: string
): Promise<void> {
	const threadData = {
		...talkData,
		messages,
		threadName: talkData.threadName || 'Conversation'
	};

	await AiThreadRepository.upsertAiThread(threadId, threadData, userId);
}

export function createUserMessage(content: string): AiMessageType {
	return {
		role: 'user',
		isChat: true,
		content,
		metadata: {
			gaioType: 'user',
			timestamp: new Date().toISOString()
		}
	};
}

export function createAssistantMessage(content: unknown): AiMessageType {
	return {
		role: 'assistant',
		isChat: true,
		content, // typeof content === 'string' ? content : JSON.stringify(content),
		metadata: {
			isChat: true,
			gaioType: 'assistant',
			timestamp: new Date().toISOString()
		}
	};
}

export function createMetadataMessage(metadata: GenericType): AiMessageType {
	return {
		role: 'assistant',
		isChat: false,
		content: JSON.stringify({
			type: 'table_metadata',
			title:
				'this metadata must be used at response analysis and Clickhouse queries',
			message: metadata
		}),
		metadata: {
			gaioType: 'internal',
			assistant: 'master',
			timestamp: new Date().toISOString()
		}
	};
}

export async function getThreadHistory(
	threadId: string
): Promise<AiMessageType[]> {
	const threadData = await AiThreadRepository.getThreadMessages(threadId);
	return threadData?.messages || [];
}

export function filterUserMessages(messages: AiMessageType[]): AiMessageType[] {
	return messages.filter((msg) => msg.metadata?.gaioType === 'user');
}

export function updateSharedTables(
	talkData: TalkData,
	newTables: string[]
): void {
	if (!talkData.sharedData) {
		talkData.sharedData = { tables: [] };
	}
	if (!talkData.sharedData.tables) {
		talkData.sharedData.tables = [];
	}

	talkData.sharedData.tables = [...talkData.sharedData.tables, ...newTables];
}

export function getNewTablesToShare(talkData: TalkData): string[] {
	if (!talkData.tables?.length) return [];

	const sharedTables = talkData.sharedData?.tables || [];
	return talkData.tables.filter((table) => !sharedTables.includes(table));
}
