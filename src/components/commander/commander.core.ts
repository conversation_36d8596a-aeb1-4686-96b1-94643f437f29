import MetaViewRepository from '@/components/meta-view/meta-view.repository';
import { dbGaio } from '@/db/db.gaio';
import type { GenericType, MetaViewType, UserType } from '@gaio/shared/types';
import { cloneDeep } from 'lodash-es';
import { commandLoadTables } from './command.core.table';
import { appQuery, metaQuery } from './command.queries';

const searchBase = {
	app: appQuery,
	meta: metaQuery
};

export async function createPowerView(
	metaViewData: MetaViewType,
	userContext: UserType
) {
	return MetaViewRepository.createMetaView(metaViewData, userContext);
}

export async function savePowerView(
	metaViewData: MetaViewType,
	userContext: UserType
) {
	return MetaViewRepository.saveMetaView(metaViewData, userContext);
}

export async function searchMetadata(
	searchItems: string[],
	searchTerm: string,
	appInfo: GenericType
) {
	if (searchItems[0] === 'table') {
		return commandLoadTables({ appInfo, searchItems, searchTerm });
	}

	if (['meta', 'app'].includes(searchItems[0])) {
		const queryString: string = searchItems
			.map((item) => searchBase[item])
			.join('UNION ALL');

		return dbGaio('metadata')
			.query(
				`SELECT * FROM (${queryString}) LIMIT 10 SETTINGS use_query_cache = true`,
				{
					params: { search: `%${searchTerm}%` },
					parse: ['options']
				}
			)
			.then((res) => {
				const dashboard = cloneDeep(res)
					.filter((item) => item.type === 'app')
					.map((item) => {
						item.type = 'dashboard';
						return item;
					});

				res = res.concat(dashboard);

				return res.map((item) => {
					if (item.type === 'meta') {
						item.icon = 'powerAll';
					}

					if (item.options) {
						item = {
							...item,
							...item.options
						};
					}

					if (item.type === 'app') {
						item.icon = 'flow';
					}

					return item;
				});
			});
	}
	return [];
}
