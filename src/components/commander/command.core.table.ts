import tableSchema from '@/components/table/use-cases/info-helpers/table.schema';
import { DbService } from '@/db/db.service';
import type {
	ConnectionResultType,
	GenericType,
	TaskType
} from '@gaio/shared/types';

export async function commandLoadTables({
	searchTerm,
	appInfo
}: {
	searchItems: string[];
	searchTerm: string;
	appInfo: GenericType;
}) {
	const taskData = {
		...appInfo,
		sourceType: 'bucket'
	} as const;

	const db = await new DbService().connect(taskData);
	const query = tableSchema(taskData as TaskType);
	let filter = '';

	if (searchTerm) {
		filter = `
			WHERE
				replaceRegexpAll(normalizeUTF8NFKD(LOWER(tableName)), '\\pM', '')
	    LIKE
				replaceRegexpAll(normalizeUTF8NFKD(LOWER('%${searchTerm}%')), '\\pM', '')
	  `;
	}
	const sql = `
		SELECT
			*
		FROM
			(${query}) ${filter}
		ORDER BY
			tableName ASC
		LIMIT
			20
		SETTINGS
			use_query_cache = true`;

	const result = (await db
		.query(sql)
		.then((res) => {
			if (!Array.isArray(res.data)) {
				return res;
			}

			res.data.forEach((item) => {
				item.sourceType = 'bucket';

				return item;
			});

			return res;
		})
		.catch((err: Error) => {
			return {
				error: true,
				message: err.message,
				query
			};
		})) as ConnectionResultType;

	return result.data.map((item) => {
		return {
			data: item,
			icon: 'table',
			label: item.tableName,
			type: 'table'
		};
	});
}
