import {
	createPowerView,
	savePowerView,
	searchMetadata
} from '@/components/commander/commander.core';
import jwtGuard from '@/server/middleware/jwt.guard';
import { readBody } from '@/server/middleware/readBody';
import type { GenericType, MetaViewType } from '@gaio/shared/types';
import { Hono } from 'hono';

const app = new Hono()
	.post('/search', jwtGuard('user').isAuth, async (c) => {
		const { searchItems, searchTerm, appInfo } = await readBody<{
			searchItems: string[];
			searchTerm: string;
			appInfo: GenericType;
		}>(c);
		return c.json(await searchMetadata(searchItems, searchTerm, appInfo));
	})

	.post('/save-view', jwtGuard('user').isAuth, async (c) => {
		const { metaViewData, user } = await readBody<{
			metaViewData: MetaViewType;
		}>(c);
		return c.json(await savePowerView(metaViewData, user));
	})

	.post('/create-view', jwtGuard('user').isAuth, async (c) => {
		const { metaViewData, user } = await readBody<{
			metaViewData: MetaViewType;
		}>(c);
		return c.json(await createPowerView(metaViewData, user));
	});

export default app;
