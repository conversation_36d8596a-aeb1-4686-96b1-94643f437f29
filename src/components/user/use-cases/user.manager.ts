import UserRepository from '@/components/user/user.repository'
import type { UserType } from '@gaio/shared/types'
import { isUniqEmail } from './user.update'
import TagRepository from '@/components/tag/tag.repository'
import CodeSnippetRepository from '@/components/code-snippet/code-snippet.repository'
import MetaRepository from '@/components/meta/meta.repository'
import { env } from '@/utils/env'
import { HTTPException } from 'hono/http-exception'
import { validateLicenseForNewUser } from '@/components/gaio-ecosystem/use-cases/license-validation.use-case'

export async function bulkSaveUser(userContext: UserType, users: UserType[]) {
	await Promise.all(
		users
			.filter((user) => user.userId)
			.map((user) => {
				return UserRepository.updateUserData(
					{
						email: user.email,
						lang: user.lang || 'en-US',
						name: user.name,
						role: user.role || 'user',
						status: user.status || 'inactive',
						tags: user.tags || [],
						userId: user.userId
					},
					userContext
				)
			})
	)
	return { status: 'done' }
}

export async function saveUser(userContext: UserType, userData: UserType) {
	const { name, status, password, tags, email, userId, role, options, lang } = userData

	if (userId) {
		const currentUser = await UserRepository.getUserById(userId)

		if (currentUser) {
			const updatedEmail = email

			if (updatedEmail !== currentUser.email) {
				const checkEmail = await UserRepository.emailExist(updatedEmail)

				if (checkEmail && checkEmail.length > 0) {
					throw new Error('userExists')
				}
			}

			await UserRepository.updateUserData(
				{
					email,
					lang,
					name,
					role,
					status,
					tags,
					userId
				},
				userContext
			)

			if (userId && password && password !== '') {
				await UserRepository.updatePassword({
					password: await Bun.password.hash(password),
					userId
				})
			}
		}
	} else {
		const checkIfUserAlreadyExist = await isUniqEmail(email)

		if (checkIfUserAlreadyExist && !checkIfUserAlreadyExist.valid) {
			throw new Error('userExists')
		}

		if (env.NODE_ENV === 'DOCKER') {
			const licenseValidation = await validateLicenseForNewUser()

			if (!licenseValidation.canCreateUser) {
				throw new HTTPException(403, {
					cause: 'licenseExceeded',
					message: licenseValidation.message || 'Cannot create new user due to license limitations'
				})
			}
		}

		await UserRepository.createUser({
			email,
			lang,
			name,
			options,
			password: await Bun.password.hash(password),
			role,
			tags
		})
	}

	return { status: 'done' }
}

export async function deleteUser(userContext: UserType, userId: string) {
	await UserRepository.deleteUser(userId, userContext)

	await TagRepository.deleteTagByUserId(userId)
	await MetaRepository.deleteMetaViewOfUser(userId)
	await CodeSnippetRepository.deleteCodeSnippetsOfUserDrafts(userId)

	return {
		status: 'done'
	}
}

export async function checkUsersExist() {
	const usersCount = await UserRepository.countUsers()
	return {
		data: { hasUsers: usersCount > 0 },
		timestamp: new Date().toISOString()
	}
}

export async function saveFirstUser(userData: UserType) {
	if (env.NODE_ENV !== 'DOCKER') {
		throw new HTTPException(401, { cause: 'unauthorizedEnvironment', message: 'You don`t have the permission' })
	}
	if (!userData) {
		throw new HTTPException(400, { message: 'User data is required' })
	}

	const usersCount = await UserRepository.countUsers()

	if (usersCount > 0) {
		return { status: 'done', hasExistingUsers: true }
	}

	const { name, password, tags, email, role, options, lang } = userData

	await UserRepository.createUser({
		email,
		lang,
		name,
		options,
		password: await Bun.password.hash(password),
		role,
		tags
	})

	return { status: 'done', hasExistingUsers: false }
}
