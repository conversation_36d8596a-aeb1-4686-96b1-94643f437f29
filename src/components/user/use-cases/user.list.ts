import UserRepository from '@/components/user/user.repository';

export async function listAllUsers() {
	const users = await UserRepository.getAllUsersAndGroups();

	return {
		groups: users
			.filter((user) => user.role === 'group')
			.map((user) => ({ name: user.name, userId: user.userId })),
		users: users
			.filter((user) => user.role !== 'portal' && user.role !== 'group')
			.map((user) => {
				return {
					email: user.email,
					lang: user.lang,
					name: user.name,
					options: user.options,
					role: user.role,
					status: user.status,
					tags: user.tags || [],
					userId: user.userId
				};
			})
	};
}
