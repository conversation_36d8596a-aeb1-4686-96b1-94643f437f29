import UserRepository from '@/components/user/user.repository';
import type { UserOptionsType, UserType } from '@gaio/shared/types';
import type { Context } from 'hono';

export type PasswordDataType = {
	currentPassword: string;
	newPassword: string;
	repeatPassword: string;
};

export async function updateUserOptions(
	user: UserType,
	options: UserOptionsType
) {
	return UserRepository.mergeUserOptions(user.userId, options);
}

export async function updateUserMetadata(c: Context) {
	const contextUser = c.get('user');
	const { userData } = await c.req.json();

	if (userData.userId === contextUser.userId) {
		return c.json(UserRepository.mergeUserMetadata(userData));
	}
	throw new Error('User not allowed to update metadata');
}

export async function updatePassword(
	contextUser: UserType,
	passwordData: PasswordDataType
) {
	try {
		if (passwordData.newPassword !== passwordData.repeatPassword) {
			return {
				message: 'passwordDoesNotMatch',
				status: 'fail'
			};
		}

		const currentUser = await UserRepository.getUserById(contextUser.userId);

		if (currentUser && currentUser.userId) {
			const isMatch = await Bun.password.verify(
				passwordData.currentPassword as string,
				currentUser.password
			);

			if (isMatch) {
				const hashNewPassword = await Bun.password.hash(
					passwordData.newPassword as string
				);

				await UserRepository.updatePassword({
					password: hashNewPassword,
					userId: contextUser.userId
				});

				return { message: 'success', status: 'ok' };
			}

			return {
				message: 'wrongCurrentPassword',
				status: 'fail'
			};
		}

		return {
			message: 'notAuthorized',
			status: 'fail'
		};
	} catch (e) {
		return {
			message: e.message,
			status: 'fail'
		};
	}
}

export async function isUniqEmail(email: string): Promise<{ valid: boolean }> {
	const anotherEmail = await UserRepository.emailExist(email);

	if (anotherEmail && anotherEmail.length > 0) {
		return {
			valid: false
		};
	}

	return {
		valid: true
	};
}
