import { listAiManager } from '@/components/ai-manager/ai-manager.repository';
import AppRepository from '@/components/app/app.repository';
import { appSave } from '@/components/app/use-cases/app.save';
import RepoRepository from '@/components/repo/repo.repository';
import UserRepository from '@/components/user/user.repository';
import type { UserType } from '@gaio/shared/types';
import type { Context } from 'hono';

export async function addAppToUserIfUserHasNoApps(user: UserType) {
	const userAppTags = await UserRepository.getUserAppTags(user.userId);

	if (!userAppTags.length) {
		const listRepos = await RepoRepository.getListOfRepoBase();
		const repoId = listRepos[0].repoId;

		const aiManagerList = await listAiManager();
		const aiManagerId = aiManagerList[0] ? aiManagerList[0].aiManagerId : null;

		await appSave(
			{
				appDescription: '',
				appId: null,
				appName: `App: ${user.name}`,
				appToken: '',
				forms: [],
				options: {
					userApp: true,
					color: 'blue',
					icon: 'users',
					creator: user.name,
					group: '',
					folderFlow: [],
					studioFlowStart: '',
					aiManagerId
				},
				params: [],
				repoId
			},
			user
		);
	}
}

export async function userPrepare(c: Context) {
	const user = c.get('user');
	return { apps: await AppRepository.getAllApps(user) };
}
