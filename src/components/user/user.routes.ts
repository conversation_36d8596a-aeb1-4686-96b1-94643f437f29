import { listAllUsers } from '@/components/user/use-cases/user.list';
import {
	bulkSaveUser,
	checkUsersExist,
	deleteUser,
	saveFirstUser,
	saveUser
} from '@/components/user/use-cases/user.manager';
import { userPrepare } from '@/components/user/use-cases/user.prepare';
import { resetTwoFactor } from '@/components/user/use-cases/user.two-factor';
import type { PasswordDataType } from '@/components/user/use-cases/user.update';
import {
	isUniqEmail,
	updatePassword,
	updateUserMetadata,
	updateUserOptions
} from '@/components/user/use-cases/user.update';
import jwtGuard from '@/server/middleware/jwt.guard';
import { readBody } from '@/server/middleware/readBody';
import type { UserOptionsType, UserType } from '@gaio/shared/types';
import { Hono } from 'hono';

const app = new Hono()
	.post('/prepare', jwtGuard('user').isAuth, async (c) => {
		return c.json(await userPrepare(c));
	})

	.post('/update-metadata', jwtGuard('user').isAuth, updateUserMetadata)

	.post('/check-email', jwtGuard().isAuth, async (c) =>
		c.json(isUniqEmail((await c.req.json()).email))
	)

	.post('/update-password', jwtGuard('user').isAuth, async (c) => {
		const { user, passwordData } = await readBody<{
			passwordData: PasswordDataType;
		}>(c);

		return c.json(await updatePassword(user, passwordData));
	})

	.post('/update-options', jwtGuard('user').isAuth, async (c) => {
		const { user, options } = await readBody<{ options: UserOptionsType }>(c);

		return c.json(await updateUserOptions(user, options));
	})

	// to-do
	// admin routes
	.get('/list', jwtGuard('admin').isAuth, async (c) => {
		return c.json(await listAllUsers());
	})

	.post('/save-user', jwtGuard('admin').isAuth, async (c) => {
		const { user, userData } = await readBody<{ userData: UserType }>(c);
		return c.json(await saveUser(user, userData));
	})

	.post('/save-first-user',  async (c) => {
		const {  userData } = await readBody<{ userData: UserType }>(c);
		return c.json(await saveFirstUser(userData));
	})

	.get('/check-users-exist', async (c) => {
		return c.json(await checkUsersExist());
	})

	.post('/bulk-save-user', jwtGuard('admin').isAuth, async (c) => {
		const { user, users } = await readBody<{ users: UserType[] }>(c);

		return c.json(await bulkSaveUser(user, users));
	})

	.post('/reset-two-factor', jwtGuard('admin').isAuth, async (c) => {
		const { user, userId } = await readBody<{ userId: string }>(c);

		return c.json(await resetTwoFactor(user, userId));
	})

	.post('/delete-user', jwtGuard('admin').isAuth, async (c) => {
		const { user, userId } = await readBody<{ userId: string }>(c);

		return c.json(await deleteUser(user, userId));
	});

export default app;
