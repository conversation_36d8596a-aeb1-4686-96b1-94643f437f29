export type UserEntity = {
	userId: string;
	name: string;
	email: string;
	password: string;
	lang: string;
	token: string;
	options: string;
	role: string;
	status: string;
	twoFactorKey: string;
	twoFactorSigned: string;
	twoFactorStatus: string;
	profileId: string;
	refreshToken: string;
	createdBy: string;
	modifiedBy: string;
	createdAt: string;
	updatedAt: string;
	deleted: boolean;
	tags: Array<unknown>;
};
