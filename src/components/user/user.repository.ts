import SerialRepository from '@/components/serial/serial.repository';
import { dbGaio } from '@/db/db.gaio';
import type { TagEntity, UserEntity } from '@/db/entities';
import type { UserOptionsType, UserType, PortalType } from '@gaio/shared/types';
import { HTTPException } from 'hono/http-exception';
import { safeBase64Encode } from '@gaio/shared/utils';
import { clickDate } from '@/utils/helpers';

// user is soft-deleted, always take this in mind

async function deleteAlllPortalUsers(appIdReference: string) {
	await dbGaio().query(
		'ALTER TABLE user DELETE WHERE startsWith(email, {email: String})',
		{
			params: { email: appIdReference }
		}
	);
}

async function deleteSingleUserPortal(token: string) {
	const user = await getUserByEmail(token);
	await dbGaio().query(
		'ALTER TABLE user DELETE WHERE email = {email: String}',
		{
			params: {
				email: token
			}
		}
	);

	return user;
}

async function createUser(userData: UserType) {
	const userExists = await getUserByEmail(userData.email);

	if (userExists) {
		throw new HTTPException(400, {
			cause: 'userAlreadyExists',
			message: 'user already exists'
		});
	}

	const userId = `user:${await SerialRepository.getUserSerial()}`;

	await dbGaio().exec(
		`
			INSERT INTO
				user (
					userId,
					name,
					email,
					password,
					role,
					status,
					options,
					tags,
					lang
        )
			VALUES (
				{userId: String},
				{name: String}, 
				{email: String}, 
				{password: String}, 
				{role: String}, 
				{status: String}, 
				{options: String},
				{tags: Array(String)},
				{lang: String}
			)
		`,
		{
			params: {
				email: userData.email,
				lang: userData.lang || 'en-US',
				name: userData.name,
				options: userData.options || {},
				password: userData.password,
				role: userData.role,
				status: 'active',
				tags: userData.tags || [],
				userId
			},
			stringify: ['options']
		}
	);

	// hash the password
	// const data = {
	// ...userData,
	// password: userData.password // Bun.password.hashSync(userData.password, 'bcrypt')
	// }

	return {};
}

async function getUserByEmail(email: string): Promise<UserType | null> {
	return await dbGaio()
		.query<UserType>(
			`
				SELECT 
					*
				FROM
					user 
				WHERE
					email = {email: String}
					AND deleted = 0 
				LIMIT 1
			`,
			{
				params: {
					email
				},
				parse: ['options']
			}
		)
		.then((res) => res[0]);
}

async function getUserById(userId: string): Promise<UserType> {
	return await dbGaio()
		.query<UserType>(
			`
				SELECT
					*
				FROM
					user 
				WHERE
					userId = {userId: String} 
					AND deleted = 0
			`,
			{
				params: {
					userId
				},
				parse: ['options']
			}
		)
		.then((res) => res[0]);
}

async function getUserTags(userId: string) {
	return await dbGaio().query<TagEntity>(
		`
			SELECT
				*
			FROM
				tag
			WHERE
				userId = {userId: String}
		`,
		{
			params: {
				userId
			}
		}
	);
}

async function getUserAppTags(userId: string) {
	return await dbGaio().query<TagEntity>(
		`
			SELECT
				*
			FROM
				tag
			WHERE
				userId = {userId: String}
				AND type = 'app'
		`,
		{
			params: {
				userId
			}
		}
	);
}

async function mergeUserMetadata(userData: UserType) {
	return await dbGaio().exec(
		`ALTER TABLE user
			UPDATE  name = {name: String},
				email = {email: String}, 
				lang = {lang: String}
			WHERE userId = {userId: String}`,
		{
			params: userData
		}
	);
}

async function updateUserData(userData: UserType, userContext: UserType) {
	return await dbGaio().exec(
		`
			ALTER TABLE
				user
			UPDATE
				name = {name: String},
				email = {email: String},
				role = {role: String},
				status = {status: String}, 
				lang = {lang: String},
				modifiedBy = {modifiedBy: String},
				tags = {tags: Array(Nullable(String))},
				updatedAt = now()
			WHERE
				userId = {userId: String}
		`,
		{
			params: {
				name: userData.name,
				email: userData.email,
				role: userData.role,
				status: userData.status,
				lang: userData.lang,
				modifiedBy: userContext.userId,
				tags: userData.tags,
				userId: userData.userId
			}
		}
	);
}

async function updatePassword({
	password,
	userId
}: { password: string; userId: string }) {
	return await dbGaio().exec(
		`ALTER TABLE user
			UPDATE  password = {password: String}
			WHERE userId = {userId: String}`,
		{
			params: {
				password,
				userId
			}
		}
	);
}

async function mergeUserOptions(userId: string, options: UserOptionsType) {
	return await dbGaio().exec(
		`ALTER TABLE user
			UPDATE options = {options: String}
			WHERE userId = {userId: String}`,
		{
			params: {
				options,
				userId
			},
			stringify: ['options']
		}
	);
}

async function emailExist(email: string) {
	return await dbGaio().query(
		'SELECT email, deleted FROM user WHERE email = {email: String} AND deleted = 0',
		{
			params: {
				email
			}
		}
	);
}

async function getAllUsersAndGroups() {
	return await dbGaio()
		.query<UserEntity>(
			`
				SELECT
					*
				FROM
					user
				WHERE
					role != 'portal' 
					AND deleted = 0
				ORDER BY
					name, userId
			`,
			{
				parse: ['options']
			}
		)
		.then((res) =>
			res.map((user) => {
				delete user.password;
				return user;
			})
		);
}

async function deleteUser(userId: string, userContext: UserType) {
	const user = await dbGaio().query<UserEntity>(
		`
			SELECT
				userId,
				role
			FROM
				user
			WHERE
				userId = {userId: String}
		`,
		{ params: { userId } }
	);

	if (user && user[0].userId !== userContext.userId) {
		await dbGaio().exec(
			`
				ALTER TABLE
					user
				UPDATE
					deleted = 1,
					modifiedBy = {modifiedBy: String},
					updatedAt = now()
				WHERE
					userId = {userId: String}`,
			{ params: { modifiedBy: userContext.userId, userId } }
		);
	}

	return { status: 'done' };
}

async function resetTwoFactor(userId: string, userContext: UserType) {
	return await dbGaio().exec(
		`
			ALTER TABLE
				user
			UPDATE
				twoFactorKey = null,
				twoFactorSigned = 'no',
				twoFactorStatus = 'invalid',
				updatedAt = now(),
				modifiedBy = {modifiedBy: String}
			WHERE
				userId = {userId: String}`,
		{
			params: {
				modifiedBy: userContext.userId,
				userId
			}
		}
	);
}

async function getUsersByTagRefAndType(ref: string, type: string) {
	return await dbGaio().query(
		`
			SELECT
				user.userId,
				user.name,
				user.email,
				'user' as type,
				user.role,
				lang
			FROM
				user
			INNER JOIN
				tag
					ON
						tag.userId = user.userId
			WHERE
				tag.refId = {ref: String}
			AND
				tag.type = {type: String}
			AND
				user.deleted = 0`,
		{
			params: {
				ref,
				type
			}
		}
	);
}

async function getUsersToTagControl(userId?: string) {
	const fields = [
		'userId',
		'name',
		'email',
		'role',
		'options',
		'lang',
		`'user' AS type`
	];

	if (userId) {
		return await dbGaio().query(
			`
				SELECT 
					${fields.join(',')}
				FROM
					user 
				WHERE
					role  != 'portal' 
				AND
					deleted = 0
				AND
					userId = {userId: String}
				ORDER BY
					name, userId`,
			{
				params: { userId },
				parse: ['options']
			}
		);
	}
	return await dbGaio().query(
		`
		SELECT
			${fields.join(',')}
		FROM
			user 
		WHERE
			role  != 'portal' 
		AND
			deleted = 0
		ORDER BY
			name, userId`,
		{
			parse: ['options']
		}
	);
}

async function deleteGroup(userId: string) {
	const usersInGroup = (await dbGaio().query(
		`
			SELECT
				userId, tags 
			FROM
				user 
			WHERE
				has(tags, {userId: String}) = 1`,
		{
			params: { userId }
			// parse: ['tags']
		}
	)) as UserType[];

	await Promise.all(
		usersInGroup.map(async (user) => {
			return dbGaio().exec(
				`
					ALTER TABLE
						user 
					UPDATE
						tags = {tags: Array<Nullable(String)>} 
					WHERE
						userId = {userId: String}`,
				{
					params: {
						tags: user.tags.filter((tag) => tag !== userId).join(','),
						userId: user.userId
					}
				}
			);
		})
	);

	await dbGaio('deleteUserGroup').exec(
		`
		ALTER TABLE
			user
		DELETE
		where
			userId = {userId: String}`,
		{ params: { userId } }
	);
}

async function saveGroup(userData: UserType, userContext: UserType) {
	if (userData.userId) {
		return await dbGaio().exec(
			`
				ALTER TABLE
					user
				UPDATE
					name = {name: String},
					options = {options: String},
					modifiedBy = {modifiedBy: String},
					updatedAt = now()
				WHERE userId = {userId: String}`,
			{
				params: {
					modifiedBy: userContext.userId,
					name: userData.name,
					options: userData.options,
					userId: userData.userId
				},
				stringify: ['options']
			}
		);
	}

	const userId = `user:${await SerialRepository.getUserSerial()}`;

	await dbGaio().exec(
		`
				INSERT INTO
					user (userId, name, role, options)
				VALUES (
					{userId: String},
					{name: String}, 
					{role: String}, 
					{options: String}
				)`,
		{
			params: {
				name: userData.name,
				options: userData.options || {},
				role: 'group',
				status: 'active',
				userId
			},
			stringify: ['options']
		}
	);
}

async function getUserRefreshToken(userId: string) {
	return await dbGaio()
		.query<Pick<UserEntity, 'refreshToken'>>(
			`
			SELECT
				refreshToken
			FROM
				user
			WHERE
				userId = {userId: String}
		`,
			{
				params: {
					userId
				}
			}
		)
		.then((res) => {
			return res[0].refreshToken;
		});
}

async function saveUserRefreshToken(userId: string, refreshToken: string) {
	return await dbGaio().exec(
		`
			ALTER TABLE
				user
			UPDATE
				refreshToken = {refreshToken: String}
			WHERE
				userId = {userId: String}
		`,
		{
			params: {
				refreshToken,
				userId
			}
		}
	);
}

async function deleteUserRefreshToken(userId: string) {
	return await dbGaio().exec(
		`
			ALTER TABLE
				user
			UPDATE
				refreshToken = ''
			WHERE
				userId = {userId: String}
		`,
		{
			params: {
				userId
			}
		}
	);
}

async function countUsers() {
	return await dbGaio()
		.query<{ total: number }>(
			`
      SELECT
        COUNT(*) as total
      FROM
        \`user\`
      `
		)
		.then((res) => {
			return res[0].total;
		});
}

async function listPortalUsers(appId: string) {
	return await dbGaio().query(
		`
			SELECT
				name,
				email AS token,
				options
			FROM
				user
			WHERE
				startsWith(email, {email: String})
				ORDER BY
					createdAt DESC
		`,
		{
			params: {
				email: safeBase64Encode(appId)
			},
			parse: ['options']
		}
	);
}

async function savePortalUser(portalData: PortalType) {
	const portal = await getUserByEmail(portalData.token);

	if (portal) {
		await dbGaio().query(
			'ALTER TABLE user UPDATE options = {options: String}, name = {name: String}, updatedAt = {updatedAt: String} WHERE email = {email: String}',
			{
				params: {
					options: JSON.stringify(portalData.options),
					name: portalData.name,
					email: portalData.token,
					updatedAt: clickDate(new Date())
				}
			}
		);

		return portal;
	}

	const userId = `user:${await SerialRepository.getUserSerial()}`;

	const newUser = {
		userId,
		email: portalData.token,
		password: portalData.token,
		options: portalData.options,
		name: portalData.name,
		role: 'portal',
		createdAt: clickDate(new Date()),
		updatedAt: clickDate(new Date())
	};

	await dbGaio().insert('user', [
		{
			...newUser,
			options: JSON.stringify(newUser.options)
		}
	]);

	return newUser;
}

export default {
	createUser,
	deleteGroup,
	deleteUser,
	deleteUserRefreshToken,
	emailExist,
	getAllUsersAndGroups,
	getUserAppTags,
	getUserByEmail,
	getUserById,
	getUserRefreshToken,
	getUserTags,
	getUsersByTagRefAndType,
	getUsersToTagControl,
	mergeUserMetadata,
	mergeUserOptions,
	resetTwoFactor,
	saveGroup,
	saveUserRefreshToken,
	updatePassword,
	updateUserData,
	countUsers,
	deleteAlllPortalUsers,
	deleteSingleUserPortal,
	listPortalUsers,
	savePortalUser
};
