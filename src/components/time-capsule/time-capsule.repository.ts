import { dbGai<PERSON> } from '@/db/db.gaio';
import type {
	TimeCapsuleSaveRequestDTO,
	TimeCapsulesListByFlowIdUseCaseDTO
} from '@/types/http/dtos/time-capsule.dtos';
import { monotonicFactory } from 'ulidx';

const ulid = monotonicFactory();

async function getAllTimeCapsulesByFlowId({
	appId,
	flowId,
	currentPage,
	limit
}: TimeCapsulesListByFlowIdUseCaseDTO) {
	return await dbGaio().findAll({
		options: {
			pagination: { currentPage, limit },
			select: ['timeCapsuleId', 'timeCapsuleLabel', 'createdAt'],
			order: { by: 'createdAt', direction: 'DESC' },
			join: {
				from: 'createdBy',
				on: 'userId',
				table: 'user',
				type: 'LEFT',
				select: ['name', 'email']
			},
			parse: ['timeCapsuleMetadata']
		},
		table: 'time_capsule',
		where: {
			flowId,
			appId
		}
	});
}

async function getTimeCapsuleById(timeCapsuleId: string, appId: string) {
	return await dbGaio().findOne({
		options: {
			select: [
				'createdAt',
				'timeCapsuleMetadata',
				'timeCapsuleId',
				'timeCapsuleLabel'
			]
		},
		table: 'time_capsule',
		where: {
			timeCapsuleId,
			appId
		}
	});
}

async function createTimeCapsule({
	timeCapsuleId,
	appId,
	flowId,
	timeCapsuleLabel,
	timeCapsuleMetadata,
	userId
}: TimeCapsuleSaveRequestDTO) {
	return await dbGaio().insert('time_capsule', [
		{
			timeCapsuleId: timeCapsuleId ?? ulid(),
			appId,
			createdBy: userId,
			flowId,
			timeCapsuleLabel,
			timeCapsuleMetadata: JSON.stringify(timeCapsuleMetadata)
		}
	]);
}

async function deleteTimeCapsuleByAppId(appId: string) {
	return await dbGaio().exec(
		'ALTER TABLE time_capsule DELETE WHERE appId = {appId: String}',
		{
			params: {
				appId
			}
		}
	);
}

export default {
	createTimeCapsule,
	getAllTimeCapsulesByFlowId,
	getTimeCapsuleById,
	deleteTimeCapsuleByAppId
};
