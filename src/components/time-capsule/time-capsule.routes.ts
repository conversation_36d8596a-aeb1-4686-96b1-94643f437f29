import { getTimeCapsuleById } from '@/components/time-capsule/use-cases/time-capsule.get-one';
import { listTimeCapsulesByFlowIdUseCase } from '@/components/time-capsule/use-cases/time-capsule.list';
import { createTimeCapsuleUseCase } from '@/components/time-capsule/use-cases/time-capsule.save';
import jwtGuard from '@/server/middleware/jwt.guard';
import { readBody } from '@/server/middleware/readBody';
import type { TimeCapsuleSaveRequestDTO } from '@/types/http/dtos/time-capsule.dtos';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { timeCapsuleDocs } from './time-capsule.docs';

const app = new Hono()
	.get(
		'/list/:appId/:flowId',
		describeRoute(timeCapsuleDocs.timeCapsuleList),
		jwtGuard().isAuth,
		async (c) => {
			const { appId, flowId } = c.req.param();

			const limit = c.req.query('limit');
			const currentPage = c.req.query('currentPage');

			const { data, meta } = await listTimeCapsulesByFlowIdUseCase({
				currentPage,
				flowId,
				limit,
				appId
			});

			const shouldSendMeta = Number(limit) > 0 && Number(currentPage) > 0;

			return c.json(
				{
					data,
					...(shouldSendMeta ? { meta } : {}),
					timestamp: new Date().toISOString()
				},
				{
					status: 200
				}
			);
		}
	)

	.get(
		'/:appId/:timeCapsuleId',
		describeRoute(timeCapsuleDocs.timeCapsuleGetOne),
		jwtGuard().isAuth,
		async (c) => {
			const { appId, timeCapsuleId } = c.req.param();

			const timeCapsule = await getTimeCapsuleById(timeCapsuleId, appId);

			return c.json(
				{
					data: timeCapsule,
					timestamp: new Date().toISOString()
				},
				{
					status: 200
				}
			);
		}
	)

	.post(
		'/save',
		describeRoute(timeCapsuleDocs.timeCapsuleSave),
		jwtGuard().isAuth,
		async (c) => {
			const { appId, flowId, timeCapsuleMetadata, timeCapsuleLabel, user } =
				await readBody<TimeCapsuleSaveRequestDTO>(c);
			const { userId } = user;

			const timeCapsule = await createTimeCapsuleUseCase({
				appId,
				flowId,
				timeCapsuleLabel,
				timeCapsuleMetadata,
				userId
			});

			return c.json(
				{
					data: timeCapsule,
					timestamp: new Date().toISOString()
				},
				{
					status: 200
				}
			);
		}
	);

export default app;
