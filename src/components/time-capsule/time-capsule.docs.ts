import type { RouteOpenAPIDocs } from '@/types/http/route.docs';

export const timeCapsuleDocs = {
	timeCapsuleList: {
		responses: {
			200: {
				description: 'List Time Capsules successfully'
			}
		}
	},
	timeCapsuleGetOne: {
		responses: {
			200: {
				description: 'Get Time Capsule successfully'
			}
		}
	},
	timeCapsuleSave: {
		responses: {
			200: {
				description:
					'Save a time capsule - Body: `{ "appId": "appid", "flowId": "flowid", "timeCapsuleLabel": {}, "timeCapsuleMetadata": {} }`'
			}
		}
	}
} satisfies RouteOpenAPIDocs;
