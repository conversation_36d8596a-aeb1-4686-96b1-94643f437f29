import TimeCapsuleRepository from '@/components/time-capsule/time-capsule.repository';
import type { TimeCapsuleSaveRequestDTO } from '@/types/http/dtos/time-capsule.dtos';
import { monotonicFactory } from 'ulidx';

const ulid = monotonicFactory();
export async function createTimeCapsuleUseCase(
	data: TimeCapsuleSaveRequestDTO
) {
	const timeCapsuleId = ulid();

	const { success } = await TimeCapsuleRepository.createTimeCapsule({
		...data,
		timeCapsuleId
	});

	if (success) {
		return {
			success,
			...(await TimeCapsuleRepository.getTimeCapsuleById(
				timeCapsuleId,
				data.appId
			))
		};
	}

	return { success };
}
