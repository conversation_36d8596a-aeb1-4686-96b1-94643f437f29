import TimeCapsuleRepository from '@/components/time-capsule/time-capsule.repository';
import type { TimeCapsulesListByFlowIdUseCaseDTO } from '@/types/http/dtos/time-capsule.dtos';
import type { AllToString } from '@/types/utilities.type';
import { HTTPException } from 'hono/http-exception';

export async function listTimeCapsulesByFlowIdUseCase({
	appId,
	flowId,
	limit,
	currentPage
}: AllToString<TimeCapsulesListByFlowIdUseCaseDTO>) {
	try {
		let itemsLimit = 10;
		let currentItemsPage = 1;

		if (typeof Number.parseInt(limit) === 'number') {
			itemsLimit = Number(limit);
		}

		if (typeof Number.parseInt(currentPage) === 'number') {
			currentItemsPage = Number(currentPage);
		}

		return await TimeCapsuleRepository.getAllTimeCapsulesByFlowId({
			currentPage: currentItemsPage,
			flowId,
			limit: itemsLimit,
			appId
		});
	} catch (err) {
		throw new HTTPException(500, {
			cause: err,
			message: 'Internal Server error fetching time capsules'
		});
	}
}
