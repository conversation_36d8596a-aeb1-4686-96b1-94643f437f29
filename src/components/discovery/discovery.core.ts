import MetaRepository from '@/components/meta/meta.repository';
import type { DiscoveryMetaData } from '@/types/http/dtos/discovery.dtos';
import type { UserType } from '@gaio/shared/types';
import { getId } from '@gaio/shared/utils';
import CronRepository from '../cron/cron.repository';
import { removeInsightsByAppIdAndInsightId } from '../insight/use-cases/insights.schedule.runner';

export async function listDiscovery(appId: string) {
	return await MetaRepository.getAllMetaByAppId(appId);
}

export async function deleteMeta(metaData: DiscoveryMetaData) {
	await CronRepository.deleteCronReference({ refId: metaData.metaId });
	await removeInsightsByAppIdAndInsightId(
		metaData.appId,
		metaData.metaId,
		metaData.repoId
	);
	return await MetaRepository.deleteMeta(metaData.metaId);
}

export async function createMeta(metaData: DiscoveryMetaData, user: UserType) {
	if (!metaData.metaId) metaData.metaId = getId();

	return await MetaRepository.insertMeta(metaData, user.userId);
}

export async function saveMeta(metaData: DiscoveryMetaData, user?: UserType) {
	if (!metaData.metaId) {
		metaData.metaId = getId();

		return await MetaRepository.insertMeta(metaData, user.userId);
	}

	return await MetaRepository.saveMeta(metaData, user);
}

export async function updateHits(metaId: string, user: UserType) {
	return await MetaRepository.updateHits(metaId, user);
}
