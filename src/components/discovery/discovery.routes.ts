import {
	createMeta,
	deleteMeta,
	listDiscovery,
	saveMeta,
	updateHits
} from '@/components/discovery/discovery.core';
import { listPowerReferences } from '@/components/discovery/use-cases/discovery.power';
import {
	getMetaByMetaId,
	listAllSmartDash,
	listAllSmartDashByAppId
} from '@/components/discovery/use-cases/discovery.smart-dashboard';
import jwtGuard from '@/server/middleware/jwt.guard';
import { readBody } from '@/server/middleware/readBody';
import type { DiscoveryMetaData } from '@/types/http/dtos/discovery.dtos';
import type { AppType, UserType } from '@gaio/shared/types';
import { type Context, Hono } from 'hono';

const app = new Hono()
	.post('/list', jwtGuard('user').isAuth, async (c) => {
		const { appId } = await c.req.json<Partial<AppType>>();

		return c.json(await listDiscovery(appId));
	})

	.post('/delete-meta', jwtGuard('dev').isAuth, async (c) => {
		const { metaData } = await readBody<{ metaData: DiscoveryMetaData }>(c);

		await deleteMeta(metaData);

		return c.json({ message: 'Meta deleted' });
	})

	.post('/save-meta', jwtGuard('dev').isAuth, async (c) => {
		const { user, metaData } = await readBody<{
			metaData: DiscoveryMetaData;
			user: UserType;
		}>(c);

		return c.json((await saveMeta(metaData, user)) as unknown);
	})

	.post('/create-meta', jwtGuard('dev').isAuth, async (c: Context) => {
		const { user, metaData } = await readBody<{
			metaData: DiscoveryMetaData;
			user: UserType;
		}>(c);

		const result = await createMeta(metaData, user);

		return c.json(result as unknown);
	})

	.post('/update-hits', jwtGuard('user').isAuth, async (c) => {
		const { metaId, user } = await readBody<{ metaId: string }>(c);

		return c.json(await updateHits(metaId, user));
	})

	.get('/list-power', jwtGuard('user').isAuth, async (c) => {
		const { user } = await readBody(c);

		return c.json(await listPowerReferences(user));
	})

	.get('/list-smart-dash', jwtGuard('user').isAuth, async (c: Context) => {
		const user = c.get('user') as UserType;
		const appId = c.req.query('appId');

		if (appId) {
			return c.json(await listAllSmartDashByAppId(appId));
		}
		return c.json(await listAllSmartDash(user.userId));
	})

	.post('/meta-by-id', jwtGuard('user').isAuth, async (c) => {
		const { metaId } = await readBody<{ metaId: string }>(c);
		return c.json(await getMetaByMetaId(metaId));
	});

export default app;
