import MetaRepository from '@/components/meta/meta.repository';

export async function listAllSmartDash(userId: string) {
	return await MetaRepository.getAllMetaSmartDash(userId);
}

export async function listAllSmartDashByAppId(appId: string) {
	return await MetaRepository.getAllMetaSmartDashByAppId(appId);
}

export async function getMetaByMetaId(metaId: string) {
	return await MetaRepository.getMetaByMetaId(metaId);
}
