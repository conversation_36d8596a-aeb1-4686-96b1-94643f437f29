import MetaStoriesRepository from '@/components/meta-stories/meta-stories.repository';
import MetaViewRepository from '@/components/meta-view/meta-view.repository';
import MetaRepository from '@/components/meta/meta.repository';
import type { UserType } from '@gaio/shared/types';

export async function listPowerReferences(user: UserType) {
	const [contexts, views, stories] = await Promise.all([
		MetaRepository.getMetasByUserTag(user.userId),
		MetaViewRepository.getMetaViewsByUser(user.userId),
		MetaStoriesRepository.getMetaStoriesByUser(user.userId)
	]);

	return {
		contexts,
		stories,
		views
	};
}
