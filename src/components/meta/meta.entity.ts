import type { SchemaType } from '@gaio/shared/types';

export type MetaOptionsInsightsList = {
	left: string;
	operator: '/' | '*' | '=';
	right: string;
	label?: string;
};

export type MetaOptionsFilters = {
	value: string;
	columnName: string;
	operator: string;
};

export type MetaEntity = {
	metaId: string;
	appId: string;
	label: string;
	metaType: string;
	description: string;
	options: {
		insights: {
			type: 'field' | 'computed';
			dimension?: string[];
			measure?: string[];
			date?: string[];
			list?: Array<MetaOptionsInsightsList>;
		};
		numberFormat: Partial<{
			formatDecimalSize: number;
			separators: string;
			formatType: string;
			formatPrefix: string;
			formatSuffix: string;
		}>;
		percentFormat: Partial<{
			formatDecimalSize: number;
			separators: string;
			formatType: string;
			formatPrefix: string;
			formatSuffix: string;
		}>;
		schema: SchemaType;
		inverted: boolean;
		growthPercentage: number;
		filters?: MetaOptionsFilters[];
		period?: 'daily' | 'weekly' | 'monthly';
		label?: string;

		// cron?: string
		// cronStatus?: 'active' | 'inactive'
		// cronBase?: string

		// id?: string
		// appId?: string
	};
	databaseName: string;
	tableName: string;
	repoId: string;
	status: string;
	userFilter: string;
	fields: string;
	hits: number;
	type: string;
	modifiedBy: string;
	createdBy: string;
	updatedAt: string;
};
