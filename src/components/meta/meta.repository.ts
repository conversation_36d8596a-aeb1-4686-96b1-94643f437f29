import CronRepository from '@/components/cron/cron.repository';
import { dbGaio } from '@/db/db.gaio';
import type { AppEntity, MetaEntity } from '@/db/entities';
import type { DiscoveryMetaData } from '@/types/http/dtos/discovery.dtos';
import { currentDatetime } from '@/utils/index';
import type { GenericType, MetaType, UserType } from '@gaio/shared/types';
import { getBucketNameFromAppId } from '@gaio/shared/utils';
import type { CronEntity } from 'src/components/cron/cron.entity';
import { removeInsightsByAppIdAndInsightId } from '../insight/use-cases/insights.schedule.runner';

async function updateHits(metaId: string, userContext: UserType) {
	return await dbGaio().exec(
		`
			ALTER TABLE
				meta
			UPDATE
				hits = hits + 1,
				modifiedBy = {modifiedBy: String},
				updatedAt = now()
			WHERE
				metaId = {metaId: String}
		`,
		{
			params: {
				metaId,
				modifiedBy: userContext.userId
			}
		}
	);
}

async function insertMeta(metaData: DiscoveryMetaData, userId: string) {
	if (metaData?.metaType.includes('insights')) {
		await CronRepository.createCron({
			cron: {
				appId: metaData.appId,
				cron: metaData.cron,
				cronBase: JSON.stringify(metaData.cronBase),
				cronStatus: metaData.cronStatus,
				refId: metaData.metaId,
				refType: 'insight'
			},
			userId: metaData.createdBy
		});
	}

	return await dbGaio().updateOrInsert({
		primaryKeys: ['metaId'],
		table: 'meta',
		values: {
			...metaData,
			databaseName: getBucketNameFromAppId(metaData.appId),
			modifiedBy: userId,
			updatedAt: currentDatetime().replace('T', ' ').replace('Z', '')
		} as GenericType
	});
}

async function deleteMetaByAppId(appId: string) {
	return await dbGaio().exec('DELETE FROM meta WHERE appId = {appId: String}', {
		params: {
			appId
		}
	});
}

async function saveMeta(metaData: DiscoveryMetaData, userContext: UserType) {
	if (metaData.metaType.includes('insights')) {
		await CronRepository.updateCronByInsight({
			cron: {
				appId: metaData.appId,
				cron: metaData.cron || null,
				cronBase: metaData.cronBase,
				cronStatus: metaData.cronStatus || 'inactive',
				refId: metaData.metaId,
				refType: 'insight'
			},
			userId: userContext.userId
		});
		if (metaData.cronStatus === 'inactive') {
			await removeInsightsByAppIdAndInsightId(
				metaData.appId,
				metaData.metaId,
				metaData.repoId
			);
		}
	} else {
		await removeInsightsByAppIdAndInsightId(
			metaData.appId,
			metaData.metaId,
			metaData.repoId
		);
		await CronRepository.deleteCron({
			appId: metaData.appId,
			refId: metaData.metaId
		});
	}

	return await dbGaio().exec(
		`
			ALTER TABLE
				meta
			UPDATE
				label = {label: String},
				description = {description: String},
				options = {options : String},
				databaseName = {databaseName: String},
				tableName = {tableName: String},
				repoId = {repoId: String},
				appId = {appId: String},
				status = {status: String},
				userFilter = {userFilter: String},
				fields = {fields: String},
				modifiedBy = {modifiedBy: String},
				createdBy = {createdBy: String},
				metaType = {metaType: Array(String)},
				aiManagerId = {aiManagerId: String}
			WHERE
				metaId = {metaId: String}
		`,
		{
			params: {
				appId: metaData.appId,
				createdBy: userContext.userId,
				databaseName: getBucketNameFromAppId(metaData.appId),
				description: metaData.description,
				fields: metaData.fields || [],
				label: metaData.label,
				metaId: metaData.metaId,
				modifiedBy: userContext.userId,
				options: metaData.options || {},
				repoId: metaData.repoId,
				status: metaData.status,
				tableName: metaData.tableName,
				userFilter: metaData.userFilter,
				metaType: metaData.metaType,
				aiManagerId: metaData.aiManagerId
			},
			stringify: ['options', 'fields']
		}
	);
}

async function getAllMetaByAppId(appId: string): Promise<DiscoveryMetaData[]> {
	return await dbGaio('metasByApp').query(
		`
			SELECT 
				meta.*,
				meta.appId as appId,
				metaType,
				aiManagerId,
				'powerView' as icon,
				cron.cron as cron,
				cron.cronStatus as cronStatus,
				cron.cronBase as cronBase
			FROM
				meta 
			LEFT JOIN
				cron
					ON cron.refType = 'insight'
					AND cron.refId = meta.metaId
			WHERE
				appId IN ({appId: String})
		`,
		{
			params: {
				appId
			},
			parse: ['options', 'cronBase', 'fields']
		}
	);
}

async function getAllMetaSmartDashByAppId(appId: string) {
	return await dbGaio('getAllMetaSmartDash').query(
		`
			SELECT 
				meta.*,
				app.appName,
				app.appId as appId,
				app.options as appOptions,
				metaType,
				aiManagerId,
				'smartDash' as icon
			FROM
				meta 
			LEFT JOIN app ON app.appId = meta.appId
			WHERE has(metaType, 'smartDash') AND appId = '${appId}'
		`,
		{
			parse: ['options', 'fields', 'appOptions'],
			params: {
				appId
			}
		}
	);
}

async function getAllMetaSmartDash(userId: string) {
	return await dbGaio('getAllMetaSmartDash').query(
		`
			SELECT 
				meta.*,
				app.appName,
				app.appId as appId,
				app.options as appOptions,
				metaType,
				aiManagerId,
				'smartDash' as icon
			FROM
				meta 
			INNER JOIN tag ON tag.refId = meta.appId
			INNER JOIN app ON app.appId = meta.appId
			WHERE has(metaType, 'smartDash') AND tag.userId = '${userId}'
		`,
		{
			parse: ['options', 'fields', 'appOptions']
		}
	);
}

async function getMetasInsightsByMetaId(metaId: string) {
	return await dbGaio('metasByApp').query<
		MetaEntity &
			Pick<AppEntity, 'repoId'> &
			Pick<CronEntity, 'cronBase' | 'cron' | 'cronStatus'>
	>(
		`
			SELECT
				meta.*,
				meta.appId as appId,
				meta.options as options,
				meta.metaType as metaType,
				meta.aiManagerId as aiManagerId,
				'insights' as icon,
				app.repoId as repoId,
				cron.cron as cron,
				cron.cronStatus as cronStatus,
				cron.cronBase as cronBase
			FROM
				meta 
			INNER JOIN
				app
					ON app.appId = meta.appId
			LEFT JOIN
				cron
					ON cron.refType = 'insight'
					AND cron.refId = meta.metaId
			WHERE
				metaId = {metaId: String}
		`,
		{
			params: {
				metaId
			},
			parse: ['options', 'cronBase', 'fields']
		}
	);
}

async function getMetaByMetaId(metaId: string) {
	return await dbGaio('getMetaByMeetaId')
		.query('select * from meta where metaId = {metaId: String}', {
			params: {
				metaId: metaId
			},
			parse: ['options', 'fields']
		})
		.then((res) => res[0]);
}

async function getMetaByTableAndAppId(
	tableName: string,
	appId: string
): Promise<MetaType> {
	return await dbGaio('getMetaByMeetaId')
		.query(
			'select * from meta where tableName = {tableName: String} and appId = {appId: String}',
			{
				params: {
					tableName,
					appId
				},
				parse: ['options', 'fields']
			}
		)
		.then((res) => res[0]);
}

async function getMetasByUserTag(userId: string) {
	return await dbGaio('metasByUserTag').query(
		`
					SELECT
						meta.metaId as metaId,
						meta.label as label,
						meta.tableName as tableName,
						meta.databaseName as databaseName,
						meta.fields as fields,
						meta.appId as appId,
						meta.description as description,
						meta.status as status,
						meta.userFilter as userFilter,
						meta.hits as hits,
						meta.updatedAt as updatedAt,
						meta.createdAt as createdAt,
						meta.modifiedBy as modifiedBy,
						meta.createdBy as createdBy,
						meta.type as type,
						meta.repoId as repoId,
						meta.options as options,
						meta.aiManagerId as aiManagerId,
						meta.metaType as metaType,
						'context' as powerType,
						'powerView' as icon,
						cron.cron as cron,
						cron.cronStatus as cronStatus,
						cron.cronBase as cronBase
					FROM
						meta
					INNER JOIN 
						tag ON tag.refId = meta.appId
					LEFT JOIN
						cron
					ON
						cron.refType = 'insight'
					AND
						cron.refId = meta.metaId
					WHERE
						tag.userId = {userId: String}
						AND	meta.status <> 'inactive'
		`,
		{
			params: {
				userId
			},
			parse: ['options', 'cronBase', 'fields']
		}
	);
}

function deleteMeta(metaId: string) {
	return dbGaio('deleteMeta').exec(
		'DELETE FROM meta WHERE metaId = {metaId: String}',
		{
			params: {
				metaId
			}
		}
	);
}

function deleteMetaViewOfUser(userId: string) {
	return dbGaio('deleteMetaViewOfUser').exec(
		'DELETE FROM meta_view WHERE userId = {userId: String}',
		{
			params: {
				userId
			}
		}
	);
}

export default {
	deleteMeta,
	deleteMetaByAppId,
	getAllMetaByAppId,
	getAllMetaSmartDash,
	deleteMetaViewOfUser,
	getMetasByUserTag,
	getMetasInsightsByMetaId,
	getMetaByMetaId,
	insertMeta,
	saveMeta,
	updateHits,
	getMetaByTableAndAppId,
	getAllMetaSmartDashByAppId
};
