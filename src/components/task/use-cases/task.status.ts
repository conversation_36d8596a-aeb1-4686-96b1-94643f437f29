import { gaioRedis } from '@/db/gaioRedis';
import type { Context } from 'hono';
import { stream } from 'hono/streaming';

export const taskStatus = async (userId: string, appId: string, c: Context) => {
	return stream(c, async (stream) => {
		const subscribeId = `${appId}-${userId}`;
		const watchFlow = gaioRedis.sub();
		await watchFlow.subscribe(subscribeId);

		await stream.write('{}');

		watchFlow.on('message', (channel, message) => {
			if (channel && channel === subscribeId) {
				stream.write(message);
			}
		});
	});
};
