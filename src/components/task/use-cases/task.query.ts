import runnerQuery from '@/components/task/use-cases/runners/runner-tasks/runner.query';
import type {
	ParamType,
	QueryTaskType,
	TaskContextType,
	UserType
} from '@gaio/shared/types';

export const taskQuery = (
	taskData: QueryTaskType,
	params: ParamType[],
	flowId: string,
	user: UserType
) => {
	return runnerQuery(
		taskData,
		{
			appId: taskData.appId,
			flowId,
			userId: user.userId,
			params
		} as TaskContextType,
		new AbortController()
	);
};
