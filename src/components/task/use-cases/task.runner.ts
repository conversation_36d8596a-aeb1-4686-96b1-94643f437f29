import FlowRepository from '@/components/flow/flow.repository';
import TaskLogRepository from '@/components/task-log/task-log.repository';
import runnerTriage from '@/components/task/use-cases/runners/runner.triage';
import { runnerCustomParam } from '@/components/task/use-cases/task.runner.custom-params';
import { gaioRedis } from '@/db/gaioRedis';
import type {
	FlowTaskType,
	ParamType,
	DefineParamTaskType,
	TaskBaseType
} from '@gaio/shared/types';
import type { TaskJobType, TaskLogType } from '@gaio/shared/types';
import type { TaskContextType } from '../definitions/types/task-context.type';
import { untilRules } from './task.runner.until-rules';

function hasStopOnError(
	task: TaskBaseType[keyof TaskBaseType]
): task is TaskBaseType[keyof TaskBaseType] & { stopOnError?: boolean } {
	return (
		task !== null &&
		typeof task === 'object' &&
		'stopOnError' in (task as Record<string, unknown>)
	);
}

export async function prepareFlowAndRun(taskContext: TaskContextType) {
	if (taskContext.taskExecutionScope?.taskPayload) {
		return await taskRunner(
			[taskContext.taskExecutionScope.taskPayload],
			taskContext
		);
	}

	//*	 Run taskIds or full run based on the workflow
	const { tasks, flowTimeout } = (await FlowRepository.getFlowTasksAndMetadata(
		taskContext.appId,
		taskContext.flowId
	)) as { tasks: TaskBaseType[keyof TaskBaseType][]; flowTimeout: number };

	const parsedTimeout = Number(flowTimeout);
	const timeoutMs =
		Number.isFinite(parsedTimeout) && parsedTimeout > 0
			? parsedTimeout
			: undefined;

	//*  No tasks to run
	if (!tasks || (tasks && tasks.length === 0)) {
		return {
			message: 'No task to run',
			status: 'done',
			params: taskContext.params
		};
	}

	//* Run all tasks
	if (!taskContext.taskExecutionScope) {
		return await taskRunner(tasks, taskContext, timeoutMs);
	}

	//* Run tasks if taskIds are present in flow  :: (run from here or single run)
	if (taskContext.taskExecutionScope.taskIds) {
		const tasksScope = tasks.filter((task) => {
			task.paused = false;
			return taskContext.taskExecutionScope.taskIds.includes(task.id);
		});

		return await taskRunner(tasksScope, taskContext, timeoutMs);
	}
}

export async function taskRunner(
	tasks: TaskBaseType[keyof TaskBaseType][],
	taskContext: TaskContextType,
	flowTimeoutMs?: number
) {
	const taskLog = await TaskLogRepository.createTaskLog(
		taskContext.appId,
		taskContext.flowId,
		taskContext.userId,
		taskContext.logFrom
	);

	taskLog.userName = taskContext.userName as string;
	const abortController = new AbortController();

	// Flow timeout controller
	let timeoutId: ReturnType<typeof setTimeout> | undefined;
	let timedOut = false;
	if (flowTimeoutMs && flowTimeoutMs > 0) {
		timeoutId = setTimeout(() => {
			timedOut = true;
			abortController.abort();
		}, flowTimeoutMs);
	}

	// Monitor Task For Abort
	const taskObserver = gaioRedis.sub();

	taskObserver.subscribe(taskLog.taskLogId);
	taskObserver.on('message', (_, message) => {
		if (message === 'abort') {
			abortController.abort();
		}
	});

	const onAbort = () => {
		taskLog.aborted = true;
	};
	abortController.signal.addEventListener('abort', onAbort);

	const taskJobItems = {};

	try {
		let index = 0;
		for await (const taskData of tasks) {
			// stop loop if user requested to stop
			if (abortController?.signal?.aborted === true) {
				break;
			}

			// check if task should be run
			untilRules(
				taskData as TaskBaseType[keyof TaskBaseType],
				taskContext.params
			);

			// stop loop if params accomplished some condition
			if (taskData.stopped) {
				break;
			}

			// skip task if paused
			if (taskData.paused) {
				continue;
			}

			const startedAt = new Date().toISOString();

			const taskJob: TaskJobType = {
				endedAt: null,
				startedAt: startedAt,
				flowId: taskContext.flowId,
				status: 'started',
				taskId: taskData.id,
				taskType: taskData.type as string,
				taskLabel: taskData.label,
				taskTarget: ['tableToParam'].includes(taskData.type)
					? null
					: taskData.resultTable,
				taskSource: ['tableToParam'].includes(taskData.type)
					? taskData.tableName
					: null,
				userId: taskContext.userId
			};

			try {
				await updateTaskLogAndPublish(taskLog, taskJob, taskJobItems);

				if (taskData.type === 'defineParam') {
					await taskDefineParam(
						taskData as DefineParamTaskType,
						taskContext.params
					);
				} else if (taskData.type === 'flow') {
					if (taskData.paused) {
						continue;
					}
					if (taskData.runType !== 'single') {
						await conditionalRulesWhileExecuteTaskRunFlow(
							taskData as FlowTaskType,
							taskContext.params
						);
					}

					taskContext.loops = taskContext.loops ?? {};

					// prepare and check if loop should continue, if exist a loop
					let runFlowCanBeExecuted = true;
					if (taskData.loopSize) {
						if (
							taskContext.loops[taskData.id] === undefined ||
							taskContext.loops[taskData.id] === null
						) {
							taskContext.loops[taskData.id] = taskData.loopSize;
						}
						if (taskContext.loops[taskData.id] >= 0) {
							if (taskContext.loops[taskData.id] === 0) {
								runFlowCanBeExecuted = false;
							}
							taskContext.loops[taskData.id] =
								taskContext.loops[taskData.id] - 1;
						}
					}

					if (runFlowCanBeExecuted) {
						await prepareFlowAndRun({
							...taskContext,
							flowId: taskData.flowId as string
						});
					}
				} else {
					await runnerTriage(taskData, taskContext, abortController);
				}

				taskJob.status = abortController.signal?.aborted ? 'aborted' : 'ended';
				taskJob.endedAt = new Date().toISOString();
				taskLog.endedAt = new Date().toISOString();
				taskLog.status = index >= tasks.length - 1 ? 'ended' : 'started';

				if (abortController.signal?.aborted && timedOut) {
					taskJob.message = 'Flow timeout exceeded';
				}

				await updateTaskLogAndPublish(taskLog, taskJob, taskJobItems);

				index++;
			} catch (e) {
				taskJob.status = abortController.signal?.aborted ? 'aborted' : 'error';
				taskJob.endedAt = new Date().toISOString();
				taskLog.endedAt = new Date().toISOString();
				taskJob.message = e.message;

				taskLog.status = index >= tasks.length - 1 ? 'ended' : 'started';

				await updateTaskLogAndPublish(taskLog, taskJob, taskJobItems);

				index++;

				if (hasStopOnError(taskData) && taskData.stopOnError) {
					break;
				}
			}
		}

		return {
			aborted: abortController.signal?.aborted,
			error: false,
			message: 'finished',
			params: taskContext.params,
			status: 'finished',
			taskLog
		};
	} catch (e) {
		return {
			aborted: abortController.signal?.aborted,
			code: e.code,
			error: true,
			message: e.message,
			params: taskContext.params,
			status: 'finished',
			taskLog
		};
	} finally {
		if (timeoutId) {
			clearTimeout(timeoutId);
		}
		await TaskLogRepository.finalizeTaskLog(
			taskLog.taskLogId,
			'ended',
			abortController.signal?.aborted
		);

		// finishs the task log at the frontend
		await gaioRedis.pub.publish(
			`type:job-${taskLog.appId}-${taskLog.userId}`,
			JSON.stringify({
				...taskLog,
				aborted: abortController.signal?.aborted,
				endedAt: new Date().toISOString(),
				status: 'ended',
				reason: timedOut ? 'flowTimeout' : undefined
			})
		);

		await taskObserver.unsubscribe(taskLog.taskLogId);
		abortController.signal.removeEventListener('abort', onAbort);
	}
}

async function conditionalRulesWhileExecuteTaskRunFlow(
	taskData: FlowTaskType,
	contextualParams: ParamType[]
) {
	let conditionFulfilled = false;

	for (const taskParam of taskData.params) {
		for (const param of contextualParams) {
			if (taskParam.paramName === param.paramName) {
				if (`${param.paramValue}`.trim().startsWith('{{')) {
					const customParam = await runnerCustomParam(
						taskData,
						param.paramValue as string,
						contextualParams
					);
					param.paramValue = `${customParam}`;
				}

				if (`${taskParam.paramValue}`.trim().startsWith('{{')) {
					const taskCustomParam = await runnerCustomParam(
						taskData,
						taskParam.paramValue as string,
						contextualParams
					);
					taskParam.paramValue = `${taskCustomParam}`;
				}

				// todo: should we make paramValue a string, always?
				if (taskParam.paramValue === param.paramValue) {
					conditionFulfilled = true;
					taskData.flowId = taskParam.flowId;
					break;
				}
			}
		}
	}

	if (!conditionFulfilled && taskData.elseFlowId) {
		taskData.flowId = taskData.elseFlowId;
	}
}

async function taskDefineParam(
	taskData: DefineParamTaskType,
	params: ParamType[]
) {
	if (taskData.params) {
		for (const param of taskData.params) {
			for (const paramOnFlow of params) {
				if (param.paramName === paramOnFlow.paramName) {
					if (`${param.paramValue}`.trim().startsWith('{{')) {
						const customParamValue = await runnerCustomParam(
							taskData,
							param.paramValue as string,
							params
						);
						paramOnFlow.paramValue = `${customParamValue || ''}`;
					} else {
						paramOnFlow.paramValue = param.paramValue;
					}
				}
			}
		}
	}

	return { status: 'done' };
}

async function updateTaskLogAndPublish(
	taskLog: TaskLogType,
	taskJob: TaskJobType,
	taskJobItems: Record<string, TaskJobType>
) {
	taskJobItems[taskJob.taskId] = taskJob;
	await TaskLogRepository.updateSingleTaskLog(taskLog, taskJobItems);

	gaioRedis.pub.publish(
		`type:job-${taskLog.appId}-${taskLog.userId}`,
		JSON.stringify({
			...taskLog,
			taskData: taskJob
		})
	);
}
