import { HTTPException } from 'hono/http-exception';
import { ofetch } from 'ofetch';

export type HttpRequestData = {
	url: string;
	method: string;
	headers: HeadersInit;
	data?: unknown;
	auth?: {
		username: string;
		password: string;
	};
};

export const taskRest = async ({
	auth,
	url,
	method,
	headers,
	data
}: HttpRequestData) => {
	const start = new Date();

	try {
		const requestHeaders = new Headers(headers);

		if (auth) {
			const basicAuth = Buffer.from(
				`${auth.username}:${auth.password}`
			).toString('base64');
			requestHeaders.set('Authorization', `Basic ${basicAuth}`);
		}
		

		const response = await ofetch.raw(url, {
			body: method === 'GET' ? undefined : data ? data : undefined,
			headers: Object.fromEntries(requestHeaders.entries()),
			method: method
		});

		const end = new Date();

		return {
			duration: end.getTime() - start.getTime(),
			data: response._data,
			headers: response.headers,
			status: response.status
		};

		// Optionally, handle different statuses here or assume all responses are valid
	} catch (error) {
		const end = new Date();
		const message = {
			duration: end.getTime() - start.getTime(),
			path: error.errno,
			status: error.status,
			code: error.code,
			data: error.data,
			message: error.message
		};

		throw new HTTPException(error.status || 400, {
			message: JSON.stringify(message)
		} as unknown);
	}
};
