import { join, resolve } from 'node:path';
import { repositoryInstance } from '@/db/connections/db.clickhouse';
import type { DatabaseConnectionQuery } from '@/db/db.hub';
import { DbService } from '@/db/db.service';
import { arrowDataType } from '@/utils/index';
import type {
	FieldType,
	GenericType,
	TaskContextType,
	TaskType
} from '@gaio/shared/types';
import { getBucketNameFromAppId } from '@gaio/shared/utils';
import { writeToPath } from 'fast-csv';
import { ensureDirSync, removeSync } from 'fs-extra';
import { camelCase, deburr } from 'lodash-es';

/**
 * Transforms column-based data arrays into row-based objects
 * @param columnNames Array of column names
 * @param dataArrays Array of data arrays, where each array represents a column
 * @param rowCount Number of rows to process
 * @returns Array of row objects
 */
export const transformColumnsToRows = (
	columnNames: string[],
	dataArrays: any[][],
	rowCount: number
): GenericType[] => {
	const result: GenericType[] = [];

	for (let i = 0; i < rowCount; i++) {
		const rowObj: GenericType = {};
		for (let j = 0; j < columnNames.length; j++) {
			const value = dataArrays[j][i];
			rowObj[columnNames[j]] = value;
		}
		result.push(rowObj);
	}

	return result;
};

type ColumnGenericType = {
	fields: string;
	fieldTypes: string;
	createOrderBy: string;
	primaryKey: string;
};

export type TaskWithSchema = Partial<{
	schema?: {
		select: FieldType[];
		selectAll?: boolean;
	} | null;
	columns?: FieldType[];
	schemaInference?: boolean;
	client?: string;
}>;

export const bucketConnection = async <T extends TaskType>(
	taskData: T,
	taskContext: TaskContextType
) => {
	return await new DbService().connect(
		{
			...taskData,
			client: 'clickhouse',
			sourceType: 'bucket'
		},
		taskContext
	);
};

export const dropTableIfExist = async (
	taskData: TaskType,
	taskContext: TaskContextType
) => {
	const bucketName = getBucketNameFromAppId(taskData.appId);

	const task = {
		...taskData,
		client: 'clickhouse',
		sourceType: 'bucket'
	};

	await executeQuery({
		at: task,
		sql: `
			DROP TABLE IF EXISTS
				${bucketName}.${taskData.resultTable};
		`,
		taskContext
	});

	return await executeQuery({
		at: task,
		sql: `
			DROP TEMPORARY TABLE IF EXISTS
			${bucketName}.${taskData.resultTable};
		`,
		taskContext
	});
};

export const pythonEnvFactory = (task: string) => `env_task_${task}`;

export const strongMinifySql = (sql: string) => sql.replace(/\s+/g, ' ').trim();

export const createTableAtBucket = async (
	taskData: TaskType,
	columnsData: Partial<ColumnGenericType>,
	taskContext: TaskContextType
) => {
	const bucketName = getBucketNameFromAppId(taskData.appId as string);
	const db = await bucketConnection(taskData, taskContext);
	const { fieldTypes, createOrderBy, primaryKey } = columnsData;

	let order = '';

	if (primaryKey) {
		order += ` PRIMARY KEY (${primaryKey}) `;
	}

	if (createOrderBy) {
		order += ` ORDER BY (${createOrderBy}) `;
	} else {
		order += 'ORDER BY tuple()';
	}

	try {
		await db.query(
			`CREATE TABLE IF NOT EXISTS ${bucketName}.${taskData.resultTable}
				(${fieldTypes})
			ENGINE = MergeTree
			${order}
			SETTINGS allow_nullable_key = 1
		`
		);
	} catch (e) {}

	return { status: 'done' };
};

export const contentFolder = resolve('content');

export const connectionsFolder = (appId: string) => {
	return join(contentFolder, 'apps', appId.replace('app:', ''), 'connections');
};

export const outputsFolder = (appId: string) => {
	return join(
		contentFolder,
		'apps',
		appId.replace('app:', ''),
		'assets',
		'outputs'
	);
};

export const resourcesFolder = (appId: string) => {
	return join(contentFolder, 'apps', appId.replace('app:', ''), 'resources');
};

export const modelsBaseFolder = (appId: string) => {
	return join(contentFolder, 'apps', appId.replace('app:', ''), 'models');
};

export const modelsFolder = (appId: string, taskDataId: string) => {
	return join(modelsBaseFolder(appId), taskDataId);
};

export const getFilenameBySessionId = (
	filename: string,
	sessionId?: string,
	ext?: string
) => {
	return `${sessionId ? `${sessionId}_` : ''}${filename}${ext ? `.${ext}` : ''}`;
};

export const createCsvFileFromAnArray = (
	data: Array<GenericType>,
	outputFile: string
) => {
	return new Promise((resolve, reject) => {
		writeToPath(outputFile, data, { headers: true })
			.on('finish', () => {
				resolve({ status: 'CSV has been written' });
			})
			.on('error', (err) => reject(err));
	});
};

export const makeSureFolderExist = (path: string) => {
	return ensureDirSync(path);
};

export const removeLocal = (files: string[]) => {
	const status = { qtd: 0, status: 'done' };

	for (const file of files) {
		try {
			removeSync(file);
			status.qtd++;
		} catch {
			status.qtd--;
		}
	}

	return status;
};

export const tableCanBeTemporary = (from: string) => {
	return (
		from !== 'studio' && from !== 'cron' && from !== 'rest' && from !== 'api'
	);
};

export const cleanErrorAtCatch = (error: GenericType) => {
	if (error?.data?.[0]) {
		const { message, code } = error.data[0];
		throw { code, error: true, message };
	}
	if (error?.data?.message) {
		const { message, code } = error.data;
		throw { code, error: true, message };
	}
	const { message, code, errno, query } = error;
	throw { code, errno, error: true, message, query };
};

// export const createTableViewAtBucket = async (taskData: TaskType, columns: GenericType, query: string) => {
//     const bucketName = getBucketNameFromAppId(taskData.appId as string)
//     const db = await bucketConnection(taskData)
//     const { fieldTypes } = columns
//
//     try {
//         db.query(
//             ` CREATE VIEW IF NOT EXISTS ${bucketName}.${taskData.resultTable}
//                         (${fieldTypes})
//                         AS ${query}`
//         )
//     } catch (e) {
//         throw e
//     }
//
//     return { status: 'done' }
// }

export const createTableAsSelectAtBucket = async (
	taskData: TaskType,
	columns: GenericType,
	query: string,
	taskContext: TaskContextType
) => {
	const bucketName = getBucketNameFromAppId(taskData.appId as string);
	const db = await bucketConnection(taskData, taskContext);
	const { fieldTypes, createOrderBy, primaryKey } = columns;

	let order = '';

	if (primaryKey) {
		order += ` PRIMARY KEY (${primaryKey}) `;
	}

	if (createOrderBy) {
		order += ` ORDER BY (${createOrderBy}) `;
	} else {
		order += 'ORDER BY tuple()';
	}
	// @ts-ignore
	if (taskData?.schema?.selectAll) {
		await db.query(`
			CREATE TABLE IF NOT EXISTS ${bucketName}.${taskData.resultTable}
				ENGINE = MergeTree
				${order} SETTINGS allow_nullable_key = 1
			AS
			(
				${query}
			)
		`);
	} else {
		await db.query(
			`
				CREATE TABLE IF NOT EXISTS ${bucketName}.${taskData.resultTable}
				(
					${fieldTypes}
				)
					ENGINE = MergeTree
					${order} SETTINGS allow_nullable_key = 1 AS
				(
					${query}
				)`
		);
	}

	return { status: 'done' };
};

export const insertAsSelect = async (
	taskData: TaskType,
	fieldStringList: string,
	query: string,
	taskContext: TaskContextType
) => {
	const bucketName = getBucketNameFromAppId(taskData.appId as string);
	const db = await bucketConnection(taskData, taskContext);

	changeTableComment(db, taskData);

	return await db.query(
		`INSERT INTO ${bucketName}.${taskData.resultTable}
			 (${fieldStringList})
			 ${query}`
	);
};

export const changeTableComment = (
	db: DatabaseConnectionQuery,
	taskData: TaskType
) => {
	try {
		if (taskData.sourceType !== 'bucket') return;

		let tableName = '';

		if (
			['update', 'delete', 'insertRow', 'update', 'insert'].includes(
				taskData.type || ''
			)
		) {
			tableName = taskData.tableName;
		} else if (taskData.resultTable) {
			tableName = taskData.resultTable;
		}

		if (!tableName) return;
		//
		// await db.query(
		// 	`ALTER TABLE ${getBucketNameFromAppId(taskData.appId as string)}.${tableName} MODIFY COMMENT 'alterId:${getId()}'`
		// );
	} catch {}
};

export const getColumnSchema = async (taskData: TaskWithSchema) => {
	const fields = [];
	const types = [];
	const createOrderBy = [];
	const primaryKey = [];

	const columns = taskData.schema ? taskData.schema.select : taskData.columns;

	if (!columns) {
		return {
			createOrderBy: '',
			fieldTypes: '',
			fields: '',
			primaryKey: ''
		};
	}

	for (const field of columns) {
		arrowDataType(field);

		let columnName = field.alias || field.columnName;

		if (!taskData.schemaInference) {
			if (taskData.client === 'oracle') {
				columnName = columnName.toLowerCase();
			}
		}

		let defaults = field.default
			? field.isFunction
				? `default ${field.default}`
				: `default '${field.default}'`
			: '';

		let comment = field.columnLength
			? ` COMMENT 'columnLength=${field.columnLength}'`
			: '';

		if (field.dataType.includes('Array')) {
			if (field.arrayDataType === 'Numeric') {
				field.dataType = 'Array(Float64)';
			} else {
				field.dataType = 'Array(String)';
			}
			defaults = '';
			comment = '';
		}

		fields.push(`${columnName}`);
		types.push(`${columnName} ${field.dataType} ${defaults} ${comment}`);

		if (field.createOrderBy) {
			createOrderBy.push(columnName);
		}

		if (field.primaryKey) {
			primaryKey.push(columnName);
		}
	}

	return {
		createOrderBy: createOrderBy.toString(),
		fieldTypes: types.toString(),
		fields: fields.toString(),
		primaryKey: primaryKey.toString()
	};
};

export const prepareSchemaInference = (
	fileFormatType: string,
	schemaInference: boolean
) => {
	let inference: string;
	let inferType: string;

	switch (fileFormatType) {
		case 'TabSeparatedWithNames':
			inferType = 'input_format_tsv_use_best_effort_in_schema_inference';
			break;
		case 'CSVWithNames':
			inferType = 'input_format_csv_use_best_effort_in_schema_inference';
			break;
		default:
			inferType = 'input_format_csv_use_best_effort_in_schema_inference';
			break;
	}

	if (schemaInference) {
		inference = `${inferType}=1`;
	} else {
		inference = `${inferType}=0`;
	}

	return inference;
};

export const dropOrTruncateUsedSource = async (
	taskData: TaskType,
	taskContext: TaskContextType
) => {
	if (taskData.truncateTables) {
		const bucketName = getBucketNameFromAppId(taskData.appId as string);
		const db = await bucketConnection(taskData, taskContext);
		for (const table of taskData.truncateTables) {
			await db.query(`TRUNCATE TABLE IF EXISTS ${bucketName}.${table}`);
		}
	}

	if (taskData.dropTables) {
		for (const resultTable of taskData.dropTables) {
			await dropTableIfExist(
				{
					...taskData,
					resultTable
				},
				taskContext
			);
		}
	}
};

export const sanitizeColumnNamesAtSystemTable = async (
	taskData: TaskType,
	taskContext: TaskContextType,
	abortController: AbortController
) => {
	const { data: tableColumns } = await executeQuery<{ name: string }>({
		abortSignal: abortController.signal,
		at: taskData,
		sql: `
				SELECT name
				FROM system.columns
				WHERE database = '${getBucketNameFromAppId(taskData.appId)}' AND table = '${taskData.resultTable}'
			`,
		taskContext
	});

	for (const column of tableColumns as GenericType[]) {
		const columnName = sanitizeColumnName(column.name);

		if (column.name !== columnName) {
			await executeQuery({
				abortSignal: abortController.signal,
				at: taskData,
				sql: `
					ALTER TABLE
						${getBucketNameFromAppId(taskData.appId)}.${taskData.resultTable}
						RENAME COLUMN IF EXISTS
							"${column.name}" TO "${columnName}"
				`,
				taskContext
			});
		}
	}
};

export const sanitizeColumnName = (columnName: string) => {
	return (
		deburr(columnName || ''.trim())
			.replaceAll(' ', '_')
			.replaceAll('-', '_')
			.replaceAll('.', '_')
			.replace(/_+/g, '_')
			// only allow alphanumeric characters and underscores, lowercase and/or uppercase
			.replace(/[^a-zA-Z0-9_]/g, '')
			// if start with a number, add an underscore
			.replace(/^[0-9]/, '_$&')
			// remove multiple space to just one space
			.replace(/\s+/g, ' ')
			// remove leading and trailing spaces
			.trim()
			// remove multiple spaces to just one space
			.replace(/\s+/g, ' ')
			// remove leading and trailing spaces
			.trim()
	);
};

export const killMutation = async ({
	taskData,
	taskContext
}: {
	taskData: TaskType;
	taskContext: TaskContextType;
}) => {
	try {
		const { databaseName, tableName } = taskData;
		const db = await bucketConnection(taskData, taskContext);
		return await db.query(
			`
				KILL MUTATION
				WHERE database = '${databaseName}'
				AND table = '${tableName}'
			`
		);
	} catch {
		return { status: 'Cant clear mutation' };
	}
};

export const executeQuery = async <TOutput>({
	at,
	sql,
	taskContext,
	abortSignal,
	logComment
}: {
	at: TaskType;
	sql: string;
	taskContext: TaskContextType;
	abortSignal?: AbortSignal;
	logComment?: string;
}) => {
	const db = await new DbService().connect(at, taskContext);

	const execution = await db.query<TOutput>(sql, abortSignal, logComment);

	const minifySql = strongMinifySql(sql || '');

	if (
		!minifySql.toLowerCase().startsWith('select') ||
		!minifySql.toLowerCase().startsWith('drop') ||
		!minifySql.toLowerCase().startsWith('create')
	) {
		changeTableComment(db, at);
	}

	return execution;
};

export const streamToBucket = async ({
	at,
	tableName,
	data
}: {
	at: TaskType;
	tableName: string;
	data: GenericType[];
}) => {
	return await repositoryInstance(at.repoId as string).insert(
		`${at.databaseName}.${tableName}`,
		data
	);
};

export const logComment = (
	taskData: TaskType,
	taskContext: TaskContextType = {
		flowId: 'flowId',
		userId: 'user:1'
	}
) => {
	return `--log_comment='${logCommentText(taskData, taskContext)}'`;
};

export const logCommentText = (
	taskData: TaskType,
	taskContext: TaskContextType
) => {
	const { appId, id, type } = taskData;

	const gaioQt = {
		appId: appId,
		flowId: taskContext.flowId,
		ref: 'gaio_qt',
		taskId: id,
		type: type,
		userId: taskContext.userId
	};

	return JSON.stringify(gaioQt);
};

/**
 * Generic utility for creating AutoML metadata tables
 * Extracts the common pattern used across multiple metadata table creation functions
 */
export const createAutoMLMetadataTable = async (
	taskData: TaskType,
	taskContext: TaskContextType,
	config: {
		tablePrefix: string;
		dataSource: {
			columns: Array<{ name?: string; type: string }>;
			rowcount: number;
			data: any[][]
		} | null;
		fallbackColumnName?: string;
	}
) => {
	// Early return if no data source
	if (!config.dataSource) return;

	const resultTable = `${config.tablePrefix}_${taskData.resultTable}`;
	const fallbackColumnName = config.fallbackColumnName || "reference";

	// Map columns to FieldType format
	const columns: FieldType[] = config.dataSource.columns.map((col) => ({
		tableName: resultTable,
		columnName: col.name ? col.name : fallbackColumnName,
		dataType: ["float", "double"].includes(col.type)
			? "Nullable(Float)"
			: "Nullable(String)",
	}));

	// Drop existing table
	await dropTableIfExist(
		{
			...taskData,
			resultTable,
		},
		taskContext,
	);

	// Create new table
	await createTableAtBucket(
		{
			...taskData,
			resultTable,
		},
		await getColumnSchema({
			...taskData,
			columns,
		}),
		taskContext,
	);

	// Transform and stream data
	const columnNames = columns.map((col) => col.columnName);
	const result = transformColumnsToRows(
		columnNames,
		config.dataSource.data,
		config.dataSource.rowcount
	);

	try {
		await streamToBucket({
			at: taskData,
			tableName: resultTable,
			data: result,
		});
	} catch (error) {
		console.error(
			"Error streaming data to bucket:",
			error instanceof Error ? error.message : String(error)
		);
	}
};
