import { spawn } from 'child_process';
import type { GenericType } from '@gaio/shared/types';

function resultsHandler(
	code: number,
	name: string,
	error: string,
	result: GenericType,
) {
	if (error) {
		result.message = `${error}`;
	} else if (code === 0) {
		result.message = 'Success';
	} else {
		result.message = `Exit code: ${code}`;
	}
	return result;
}

export async function shell(
	name: string,
	task: string,
	args: string[] = [],
	abortControllerSignal?: AbortSignal,
) {
	return new Promise<GenericType>((resolve, reject) => {
		const terminal = spawn(task, args, {
			detached: false,
			shell: true,
			signal: abortControllerSignal,
		});

		const result: GenericType = { code: name, message: '' };
		let error = '';

		terminal.stderr.on('data', (data: Buffer) => {
			const output = data.toString();

			if (!output?.toLowerCase().includes('cmdstanpy')) {
				error += data.toString();
			}
		});

		terminal.stdout.on('data', (data: Buffer) => {
			if (data.toString().includes('MemoryError')) {
				error += ' Memory high usage. ';
			}
		});

		terminal.on('exit', (code: number) => {
			resultsHandler(code, name, error, result);
			if (result.message === 'Success') {
				resolve(result);
			} else {
				reject(result);
			}
		});

		terminal.on('error', (err) => {
			reject({ code: name, message: `Process error: ${err.message}` });
		});

		terminal.on('close', () => {
			terminal.unref();
			terminal.stdout.removeAllListeners();
			terminal.stderr.removeAllListeners();
			terminal.removeAllListeners();
		});

		if (abortControllerSignal) {
			abortControllerSignal.addEventListener('abort', () => {
				terminal.kill();
			});
		}
	});
}
