import type { TaskBaseType, TaskContextType } from '@gaio/shared/types';
import runnerCloudFile from './runner-tasks/runner-cloud-file';
import runnerAutoML from './runner-tasks/runner.automl';
import runnerBuilder from './runner-tasks/runner.builder';
import runnerCreateTable from './runner-tasks/runner.create-table';
import runnerCsvUrl from './runner-tasks/runner.csv-url';
import runnerDeleteRow from './runner-tasks/runner.delete-row';
import runnerExportToFile from './runner-tasks/runner.export-to-file';
import runnerFileImport from './runner-tasks/runner.file-import';
import runnerGoogleSpreadsheet from './runner-tasks/runner.google-spreadsheet';
import runnerInsertRow from './runner-tasks/runner.insert-row';
import runnerInsertTable from './runner-tasks/runner.insert-table';
import runnerLocalFile from './runner-tasks/runner.local-file';
import runnerMail from './runner-tasks/runner.mail';
import runnerParamToTable from './runner-tasks/runner.param-to-table';
import runnerPivotTable from './runner-tasks/runner.pivot-table';
import runnerPython from './runner-tasks/runner.python';
import runnerPythonHub from './runner-tasks/runner.python-hub';
import runnerQuery from './runner-tasks/runner.query';
import runnerQuickTable from './runner-tasks/runner.quick-table';
import runnerRest from './runner-tasks/runner.rest';
import runnerSample from './runner-tasks/runner.sample';
import runnerScoring from './runner-tasks/runner.scoring';
import runnerSource from './runner-tasks/runner.source';
import runnerSurvivalAnalysis from './runner-tasks/runner.survival-analysis';
import runnerTableToParam from './runner-tasks/runner.table-to-param';
import runnerUnpivot from './runner-tasks/runner.unpivot';
import runnerUpdateRow from './runner-tasks/runner.update-row';
import runnerUser from './runner-tasks/runner.user';
import runnerWhatsapp from './runner-tasks/runner.whatsapp';

export default async <T extends keyof TaskBaseType>(
	taskData: TaskBaseType[T],
	taskContext: TaskContextType,
	abortController?: AbortController
) => {
	switch (taskData.type) {
		case 'builder':
			if (taskData.sourceType === 'bucket') {
				return await runnerBuilder(taskData, taskContext, abortController);
			}
			if (taskData.sourceType === 'source') {
				return await runnerSource(taskData, taskContext, abortController);
			}
			break;
		case 'sourceRaw':
			return await runnerSource(taskData, taskContext, abortController);
		case 'pivot':
			return await runnerPivotTable(taskData, taskContext, abortController);
		case 'query':
			return await runnerQuery(taskData, taskContext, abortController);
		case 'unpivot':
			return await runnerUnpivot(taskData, taskContext, abortController);
		case 'delete':
			return await runnerDeleteRow(taskData, taskContext, abortController);
		case 'tableToParam':
			return await runnerTableToParam(taskData, taskContext, abortController);
		case 'paramToTable':
			return await runnerParamToTable(taskData, taskContext, abortController);
		case 'insertRow':
			return await runnerInsertRow(taskData, taskContext, abortController);
		case 'update':
			return await runnerUpdateRow(taskData, taskContext, abortController);
		case 'sample':
			return await runnerSample(taskData, taskContext, abortController);
		case 'quickTable':
			return await runnerQuickTable(taskData, taskContext, abortController);
		case 'insert':
			return await runnerInsertTable(taskData, taskContext, abortController);
		case 'cloudStorage':
			return await runnerCloudFile(taskData, taskContext, abortController);
		case 'exportToFile':
			return await runnerExportToFile(taskData, taskContext, abortController);
		case 'userMirror':
			// todo implement abortController
			return await runnerUser(taskData, taskContext, abortController);
		case 'create':
			// todo implement abortController
			return await runnerCreateTable(taskData, taskContext, abortController);
		case 'csvUrl':
			return await runnerCsvUrl(taskData, taskContext, abortController);
		case 'fileImport':
			return await runnerFileImport(taskData, taskContext, abortController);
		case 'forecast':
		case 'cluster':
		case 'pca':
		case 'basket':
			return await runnerPythonHub(taskData, taskContext, abortController);
		case 'googleSpreadsheet':
			return await runnerGoogleSpreadsheet(
				taskData,
				taskContext,
				abortController
			);
		case 'coxph':
			return await runnerSurvivalAnalysis(
				taskData,
				taskContext,
				abortController
			);
		case 'mail':
			return await runnerMail(taskData, taskContext, abortController);
		case 'whatsapp':
			return await runnerWhatsapp(taskData, taskContext, abortController);
		case 'rest':
			return await runnerRest(taskData, taskContext, abortController);
		case 'automl':
			// return await runnerPythonHub(
			// 	taskData,
			// 	params,
			// 	taskContext,
			// 	abortController
			// );
			return await runnerAutoML(taskData, taskContext, abortController);
		case 'scoring':
			return await runnerScoring(taskData, taskContext, abortController);
		case 'localCsv':
			return await runnerLocalFile(taskData, taskContext, abortController);
		case 'python':
			return await runnerPython(taskData, taskContext, abortController);
		default:
			return { status: 'no task' };
	}
};
