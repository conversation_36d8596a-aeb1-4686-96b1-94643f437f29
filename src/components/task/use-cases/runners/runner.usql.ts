import type { GenericType } from '@gaio/shared/types';

export const usqlQueryCommand = async (type: string, args: GenericType) => {
	const {
		host,
		oracleAlternativeDriver,
		user,
		password,
		encryptSource,
		port,
		database,
		sid,
		serviceName,
		sql
	} = args;

	const miniQuery = JSON.stringify(
		`${sql}`
			.replace(/--.*\n/g, '')
			.replace(/\/\*[\s\S]*?\*\//g, '')
			.replace(' =', '=')
			.replace('= ', '=')
			.replace('( ', '(')
			.replace(' )', ')')
			.replace('\r', ' ')
			.replace('\\n', ' ')
			.replace('\\r', ' ')
			.replace('\n', ' ')
			.replace(/`/g, '')
			.replace(/ +/g, ' ')
			.replace(/^ /g, '')
			.replace(/\s+/g, ' ')
			.trim()
	);

	const sed = `| sed "s/'/’/g" | sed "s/    / /g" `;
	const scrubcsv = `${sed} | scrubcsv --replace-newlines --null '(?i)NULL' -q -d "|" `;

	const dbPort = type === 'clickhouse' ? '9000' : port;
	const conn = `${user}:${encodeURIComponent(password)}@${host}:${Number(dbPort)}`;

	switch (type) {
		case 'clickhouse':
			return `usql --csv -q -F "|" 'ch://${conn}/${database}' -w  --command ${miniQuery} --set SHOW_HOST_INFORMATION=false ${scrubcsv} `;

		case 'mysql':
		case 'memsql':
		case 'aurora':
		case 'mariadb':
			return `usql --csv -q -F "|" 'my://${conn}/${database}' -w  --command ${miniQuery} --set SHOW_HOST_INFORMATION=false ${scrubcsv} `;

		case 'mssql':
			return `usql --csv -q -F "|" 'mssql://${conn}/${database}${
				!encryptSource ? '' : '?encrypt=true'
			}' -w  --command ${miniQuery} --set SHOW_HOST_INFORMATION=false ${scrubcsv} `;

		case 'pg':
			return `usql --csv -q -F "|" 'pg://${conn}/${database}?sslmode=disable' -w  --command ${miniQuery} --set SHOW_HOST_INFORMATION=false ${scrubcsv} `;

		case 'redshift':
			return `usql --csv -q -F "|" 'postgres://${conn}/${database}?sslmode=disable' -w  --command ${miniQuery} --set SHOW_HOST_INFORMATION=false ${scrubcsv} `;

		case 'oracle':
			if (oracleAlternativeDriver) {
				return `usql --csv -q -F "|" 'gr://${conn}/${
					sid || serviceName
				}' -w  --command ${miniQuery} --set SHOW_HOST_INFORMATION=false ${scrubcsv} `;
			}
			return `usql --csv -q -F "|" 'or://${conn}/${
				sid || serviceName
			}' -w  --command ${miniQuery} --set SHOW_HOST_INFORMATION=false ${scrubcsv} `;

		default:
			return '';
	}
};
