import { exec } from 'node:child_process';
import { writeFileSync } from 'node:fs';
import { join } from 'node:path';
import RepoRepository from '@/components/repo/repo.repository';
import type {
	SurvivalAnalysisTaskType,
	TaskContextType
} from '@gaio/shared/types';
import { getBucketNameFromAppId } from '@gaio/shared/utils';
import { ensureDir } from 'fs-extra';
import { shell } from '../runner.shell';
import {
	createTableAtBucket,
	dropTableIfExist,
	getColumnSchema,
	logComment,
	removeLocal
} from '../runner.tools';
import { a } from 'ofetch/dist/shared/ofetch.d0b3d489';

export default async function (
	taskData: SurvivalAnalysisTaskType,
	taskContext: TaskContextType,
	abortController: AbortController
) {
	const autoPath = join(__dirname, '..', '..', '..', '..', 'coxph');
	await ensureDir(autoPath);

	await removeLocal([`${autoPath}/${taskData.id}*`]);

	await dropTableIfExist(taskData, taskContext);
	await generateTableResult(taskData, taskContext);

	await createLocalData(taskData, autoPath);
	await createExecutable(taskData, autoPath);
	await runCoxph(taskData, autoPath, abortController);
	await csvUnion(taskData, autoPath);
	await importToBucket(taskData, autoPath, taskContext);

	await removeLocal([`${autoPath}/${taskData.id}*`]);

	return { status: 'done' };
}

async function csvUnion(taskData: SurvivalAnalysisTaskType, autoPath: string) {
	await shell('union of results', 'gocsv', [
		'zip',
		`${autoPath}/${taskData.id}_in.csv`,
		`${autoPath}/${taskData.id}_out.csv`,
		'>',
		`${autoPath}/${taskData.id}_pre_result.csv`
	]);

	// await removeLocal([`${autoPath}/${taskData.id}_in.csv`, `${autoPath}/${taskData.id}_out.csv`]);

	await shell('remove header', 'gocsv', [
		'behead',
		`${autoPath}/${taskData.id}_pre_result.csv`,
		'>',
		`${autoPath}/${taskData.id}_result.csv`
	]);

	return await removeLocal([`${autoPath}/${taskData.id}_pre_result.csv`]);
}

async function runCoxph(
	taskData: SurvivalAnalysisTaskType,
	autoPath: string,
	abortController: AbortController
) {
	try {
		return await shell('run SA executable', 'python3', [
			`${autoPath}/${taskData.id}.py`,
			abortController.signal.toString()
		]);
	} catch (e) {
		throw { message: e.message };
	}
}

async function generateTableResult(
	taskData: SurvivalAnalysisTaskType,
	taskContext: TaskContextType
) {
	taskData.columns.unshift({
		...taskData.columns[0],
		columnLength: 4,
		columnName: 'saPredict',
		dataType: 'Nullable(Float64)'
	});

	await createTableAtBucket(
		taskData,
		await getColumnSchema({
			...taskData
		}),
		taskContext
	);
}

async function importToBucket(
	taskData: SurvivalAnalysisTaskType,
	autoPath: string,
	taskContext: TaskContextType
) {
	const countCsvBeforeImport = await new Promise((resolve, reject) => {
		exec(`xsv count ${autoPath}/${taskData.id}_result.csv`, (error, stdout) => {
			if (error) {
				reject({
					code: 'import to bucket',
					error: true,
					message: "can't count csv rows before upload to repository"
				});
			}
			resolve({ count: `${stdout}`.replace(/\n/g, '').replace(/ /g, '') });
		});
	});

	if (countCsvBeforeImport && countCsvBeforeImport.count === '0') {
		return { status: 'done' };
	}
	const repoData = await RepoRepository.getRepoCredentials(taskData);
	const query = `insert into ${getBucketNameFromAppId(taskData.appId)}.${taskData.resultTable} FORMAT CSV`;
	const clientArgs = [
		`--host=${repoData.host}`,
		`--user=${repoData.user}`,
		`--password=${repoData.password} `,
		'--input_format_skip_unknown_fields=1',
		'--date_time_input_format="best_effort"',
		'--input_format_null_as_default=1',
		'--input_format_defaults_for_omitted_fields=1',
		'--input_format_tsv_empty_as_default=1',
		logComment(taskData),
		// '--input_format_csv_unquoted_null_literal_as_null=1',
		`--query='${query}'`,
		'<',
		`${autoPath}/${taskData.id}_result.csv`
	];

	await shell('import file with data to repository', 'clickhouse-client', [
		...clientArgs
	]);

	await removeLocal([
		`${autoPath}/${taskData.id}_result.csv`
		// `${autoPath}/${taskData.id}.py`,
	]);

	return { status: 'done' };
}

async function createLocalData(
	taskData: SurvivalAnalysisTaskType,
	autoPath: string
) {
	const before = `${autoPath}/${taskData.id}_out.csv`;

	const query = `select * from ${taskData.databaseName}.${taskData.tableName}`;
	const repo = await RepoRepository.getRepoCredentials(taskData);
	const click = `clickhouse-client ${logComment(taskData)} --host=${repo.host} --user=${repo.user} --password=${
		repo.password
	} --query="${query} FORMAT CSVWithNames" > ${before}`;
	await shell('export csv file', click, []);

	return { status: 'done' };
}

async function createExecutable(
	taskData: SurvivalAnalysisTaskType,
	autoPath: string
) {
	const pyFile = `${autoPath}/${taskData.id}.py`;
	const inFile = `${autoPath}/${taskData.id}_in.csv`;
	const outFile = `${autoPath}/${taskData.id}_out.csv`;

	const startColumn = taskData.startColumn
		? `start_column="${taskData.startColumn}",`
		: '';
	const ignore =
		taskData.excludeColumns.length <= 0
			? ''
			: `ignored_columns=['${taskData.excludeColumns.join("','")}'],`;

	return writeFileSync(
		pyFile,
		[
			'import pandas as pd',
			'import h2o',
			'from h2o.estimators.coxph import H2OCoxProportionalHazardsEstimator',
			'h2o.init()',
			`data = pd.read_csv('${outFile}', sep=',')`,
			'data_h2o = h2o.H2OFrame(data)',
			'train, valid = data_h2o.split_frame(ratios=[.7], seed=1234)',
			`model = H2OCoxProportionalHazardsEstimator(
                ${startColumn}
                ${ignore}
                stop_column="${taskData.stopColumn}",
                ties="${taskData.ties}")`,
			`model.train(y="${taskData.eventColumn}",training_frame=train)`,
			'scoring = model.predict(data_h2o)',
			`h2o.export_file(scoring, '${inFile}', force=True)`
		].join('\n')
	);
}
