import type {
	FieldType,
	QuickTableTaskType,
	TaskContextType
} from '@gaio/shared/types';
import {
	createTableAtBucket,
	dropTableIfExist,
	executeQuery,
	getColumnSchema,
	logCommentText,
	streamToBucket
} from '../runner.tools';
import { arrowDataType } from '@/utils/helpers';

export default async function (
	taskData: QuickTableTaskType,
	taskContext: TaskContextType,
	abortController: AbortController
) {
	if (taskData.dropTable) {
		await dropTableIfExist(taskData, taskContext);
	}

	taskData.columns = taskData.columns.map((column) => {
		column.columnName = column.columnName.replace(/\s/g, '');
		return column;
	});

	const columns = await getColumnSchema({
		...taskData,
		columns: taskData.columns.map((column) => {
			return {
				...column,
				dataType: 'Nullable(String)'
			};
		})
	});

	await createTableAtBucket(taskData, columns, taskContext);
	await streamToBucket({
		at: taskData,
		data: taskData.data,
		tableName: taskData.resultTable
	});

	if (taskData.transformData) {
		if (
			taskData.columns.filter(
				(column) => column.dataType !== 'Nullable(String)'
			).length > 0
		) {
			if (taskData.lastItems) {
				await treatDataByUpdateColumns(taskData, taskContext, abortController);
				await alterTableToNonStringColumns(
					taskData,
					taskContext,
					abortController
				);
			}
		}
	}
}

async function treatDataByUpdateColumns(
	taskData: QuickTableTaskType,
	taskContext: TaskContextType,
	abortController: AbortController
) {
	const sqlQuery = `ALTER TABLE ${taskData.resultTable} UPDATE ${taskData.columns
		.filter((column) => column.dataType !== 'Nullable(String)')
		.map((column) => {
			switch (column.dataType) {
				case 'Nullable(Int64)':
					return `${column.columnName} = toInt64OrNull(${column.columnName})`;
				case 'Nullable(Float64)':
					return `${column.columnName} = toFloat64OrNull(${column.columnName})`;
				case 'Nullable(Decimal64)':
					return `${column.columnName} = toDecimal64OrNull(${column.columnName}, ${column.columnLength || 0})`;
				case 'Nullable(Date)':
					if (column.dateTimeFormat) {
						return `${column.columnName} = toString(toDate(parseDateTimeInJodaSyntaxOrNull(${column.columnName}, '${column.dateTimeFormat}')))`;
					}
					return `${column.columnName} = toDate(parseDateTimeBestEffortOrNull(${column.columnName}))`;
				case 'Nullable(DateTime)':
					if (column.dateTimeFormat) {
						return `${column.columnName} = parseDateTimeInJodaSyntaxOrNull(${column.columnName}, '${column.dateTimeFormat}')`;
					}
					return `${column.columnName} = parseDateTimeBestEffortOrNull(${column.columnName})`;
			}
		})
		.join(', ')} WHERE 1 = 1`;

	return await executeQuery({
		abortSignal: abortController.signal,
		at: taskData,
		logComment: logCommentText(taskData, taskContext),
		sql: sqlQuery,
		taskContext
	});
}

async function alterTableToNonStringColumns(
	taskData: QuickTableTaskType,
	taskContext: TaskContextType,
	abortController: AbortController
) {
	for (let column of taskData.columns) {
		column = arrowDataType(column as FieldType);
		if (column.dataType !== 'Nullable(String)') {
			executeQuery({
				abortSignal: abortController.signal,
				at: taskData,
				logComment: logCommentText(taskData, taskContext),
				sql: `ALTER TABLE ${taskData.resultTable} MODIFY COLUMN ${column.columnName} ${column.dataType}`,
				taskContext
			}).then();
		}
	}

	return {
		success: true
	};
}
