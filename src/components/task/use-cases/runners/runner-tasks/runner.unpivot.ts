import type { TaskContextType, UnpivotTaskType } from '@gaio/shared/types'
import { getBucketNameFromAppId } from '@gaio/shared/utils'
import { dropTableIfExist, executeQuery } from '../runner.tools'

export default async function(
	taskData: UnpivotTaskType,
	taskContext: TaskContextType,
	abortController: AbortController
) {
	await dropTableIfExist(taskData, taskContext)

	const sql = constructUnpivotSQL(taskData)

	return await executeQuery({
		abortSignal: abortController.signal,
		at: taskData,
		sql: `
			CREATE TABLE
				${getBucketNameFromAppId(taskData.appId)}.${taskData.resultTable}
				ENGINE = MergeTree
				ORDER BY tuple
			(
			)
			AS ${sql}
		`, taskContext
	})
}

function constructUnpivotSQL(taskData: UnpivotTaskType): string {
	const {
		appId,
		tableName,
		extraColumns = [],
		extraColumnPosition,
		orderBy,
		orderByType,
		unpivotColumns
	} = taskData

	const baseFields = ['category', 'quantity']
	const fields =
		extraColumnPosition === 'start'
			? [...extraColumns, ...baseFields]
			: [...baseFields, ...extraColumns]
	const order = orderBy ? `ORDER BY ${orderBy} ${orderByType}` : ''

	return `
		SELECT ${fields.join(', ')}
		FROM ${getBucketNameFromAppId(appId)}.${tableName} ARRAY
					 JOIN [${unpivotColumns.join(', ')}] AS quantity, [${unpivotColumns.map((o) => `'${o}'`).join(', ')}] AS category
			${order}
	`
}
