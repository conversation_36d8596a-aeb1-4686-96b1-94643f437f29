import templateString from '@/utils/template-string';
import type { TaskContextType } from '@gaio/shared/types';
import type { CsvUrlTaskType } from '@gaio/shared/types';
import {
	dropTableIfExist,
	executeQuery,
	prepareSchemaInference,
	sanitizeColumnNamesAtSystemTable
} from '../runner.tools';

export default async function (
	taskData: CsvUrlTaskType,
	taskContext: TaskContextType,
	abortController: AbortController
) {
	const { params } = taskContext;
	const formattedCsvUrl = templateString(taskData.url, params);

	let fileFormatType = taskData.fileFormatType;
	let delimiter = ', format_csv_allow_single_quotes=1';

	if (
		!['TabSeparatedWithNames', 'CSVWithNames'].includes(taskData.fileFormatType)
	) {
		fileFormatType = 'CSVWithNames';
		delimiter = `, format_csv_delimiter='${taskData.fileFormatType}', format_csv_allow_single_quotes=1`;
	}

	delimiter += ', input_format_null_as_default=1';

	if (taskData.insertMode) {
		await executeQuery({
			abortSignal: abortController.signal,
			at: taskData,
			sql: `
				INSERT INTO ${taskData.resultDatabase}.${taskData.resultTable}
				SELECT *
				FROM url('${formattedCsvUrl}', ${fileFormatType}) SETTINGS input_format_skip_unknown_fields = 1,
									input_format_csv_trim_whitespaces = 1,
									input_format_csv_try_infer_numbers_from_strings = 1 ${delimiter}
			`,
			taskContext
		});
	} else {
		let schemaInference = '';

		await dropTableIfExist(taskData, taskContext);
		schemaInference = prepareSchemaInference(
			taskData.fileFormatType,
			taskData.schemaInference
		);

		await executeQuery({
			abortSignal: abortController.signal,
			at: taskData,
			sql: `
				CREATE TABLE
					${taskData.resultDatabase}.${taskData.resultTable}
					ENGINE = MergeTree ORDER BY tuple
				(
				)
				AS
				SELECT *
				FROM url('${formattedCsvUrl}', ${fileFormatType}) SETTINGS input_format_skip_unknown_fields = 1, 
										input_format_csv_trim_whitespaces = 1,
									input_format_csv_try_infer_numbers_from_strings = 1, ${schemaInference}
					${delimiter}
			`,
			taskContext
		});

		await sanitizeColumnNamesAtSystemTable(
			taskData,
			taskContext,
			abortController
		);
	}

	return { status: 'done' };
}
