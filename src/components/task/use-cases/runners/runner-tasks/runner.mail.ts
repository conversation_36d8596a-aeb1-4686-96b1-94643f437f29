import templateString from '@/utils/template-string';
import type {
	GenericType,
	MailTaskType,
	ParamType,
	TaskContextType
} from '@gaio/shared/types';
import { getBucketNameFromAppId } from '@gaio/shared/utils';
import { resolve, sleep } from 'bun';
import { isNumber } from 'lodash-es';
import { dropTableIfExist, executeQuery } from '../runner.tools';
import { createEmailService } from 'unemail';
// @ts-ignore
import resendProvider from 'unemail/providers/resend';
// @ts-ignore
import awsSesProvider from 'unemail/providers/aws-ses';
// @ts-ignore
import smtpProvider from 'unemail/providers/smtp';
import { pool } from 'mssql';

interface EmailService {
	sendEmail(message: EmailMessage): Promise<EmailResult>;
}

interface EmailMessage {
	from: string;
	to: string;
	subject: string;
	html: string;
}

interface EmailResult {
	success: boolean;
	message: string | null;
}

// Simplified email provider using unemail's defineProvider
const createEmailProvider = async (
	taskData: MailTaskType,
	abortSignal?: AbortSignal
) => {
	// Use unemail's built-in provider detection or create a custom provider
	// For now, let's fall back to a simple fetch-based approach that works universally

	switch (taskData.service) {
		case 'awsSes':
			// For AWS SES, we'll use a custom implementation since it's complex
			return {
				name: 'awsSes',
				send: async (_message: EmailMessage) => {
					const emailService = createEmailService({
						provider: awsSesProvider({
							accessKeyId: taskData.options.accessKeyId,
							secretAccessKey: taskData.options.secretAccessKey,
							region: taskData.options.region
						})
					});

					const response = await emailService.sendEmail({
						from: {
							name: taskData.fromName || null,
							email: _message.from
						},
						to: [
							{
								email: _message.to
							}
						],
						subject: _message.subject,
						html: _message.html
					});

					if (!response.success) {
						throw new Error(response.error.message);
					}
				}
			};

		case 'resend':
			return {
				name: 'resend',
				send: async (message: EmailMessage) => {
					if (!taskData.options.apiKey) {
						throw new Error('Resend API key is required');
					}

					const emailService = createEmailService({
						provider: resendProvider({
							apiKey: taskData.options.apiKey
						})
					});

					const response = await emailService.sendEmail({
						from: {
							name: taskData.fromName || null,
							email: message.from
						},
						to: [
							{
								email: message.to
							}
						],
						subject: message.subject,
						html: message.html
					});

					if (!response.success) {
						throw new Error(response.error.message);
					}
				}
			};

		case 'sendGrid':
			return {
				name: 'sendGrid',
				send: async (message: EmailMessage) => {
					const response = await fetch(
						'https://api.sendgrid.com/v3/mail/send',
						{
							method: 'POST',
							headers: {
								'Content-Type': 'application/json',
								Authorization: `Bearer ${taskData.options.apiKey}`
							},
							// Pass abort signal to allow cancellation
							signal: abortSignal,
							body: JSON.stringify({
								personalizations: [
									{
										to: [
											{
												name: taskData.fromName || null,
												email: message.to
											}
										]
									}
								],
								subject: message.subject,
								content: [
									{
										type: 'text/plain',
										value: message.html
									}
								],
								from: {
									email: message.from
								}
							})
						}
					);

					if (!response.ok) {
						const error = response.statusText;
						throw new Error(error);
					}
				}
			};

		default:
			// SMTP provider
			return {
				name: 'SMTP',
				send: async (message: EmailMessage) => {
					const emailService = createEmailService({
						provider: smtpProvider({
							host: taskData.host,
							port: Number(taskData.port),
							rejectUnauthorized: taskData.office365,
							secure: taskData.office365 ? false : taskData.secure,
							user: taskData.user,
							password: taskData.password,
							timeout: taskData.options?.timeout || 15000
						})
					});

					const timeoutMs = taskData.options?.timeout || 15000;
					let timeoutId: NodeJS.Timeout;
					try {
						const response = await Promise.race<{
							success: boolean;
							message: string | null;
						}>([
							new Promise((resolve) => {
								emailService
									.sendEmail({
										from: {
											name: taskData.fromName || null,
											email: message.from
										},
										to: [
											{
												email: message.to
											}
										],
										subject: message.subject,
										html: message.html
									})
									.then(() => {
										resolve({ success: true, message: null });
									})
									.catch((err) => {
										resolve({ success: false, message: err.message });
									});
							}),
							new Promise((resolve) => {
								timeoutId = setTimeout(
									() =>
										resolve({
											success: false,
											message: 'Email send timed out'
										}),
									timeoutMs
								);
							})
						]);

						if (!response?.success) {
							throw new Error(response?.message || 'Unknown error');
						}

						return response;
					} finally {
						if (timeoutId) clearTimeout(timeoutId);
					}
				}
			};
	}
};

export default async function (
	taskData: MailTaskType,
	taskContext: TaskContextType,
	abortController: AbortController
) {
	await prepareResult(taskData, taskContext, abortController.signal);
	const extraData = {} as GenericType;
	if (taskData.extraData) {
		for (const tableName of taskData.extraData) {
			extraData[tableName] = await executeQuery({
				abortSignal: abortController.signal,
				at: taskData,
				sql: `select *
							from ${getBucketNameFromAppId(taskData.appId)}.${tableName} limit 50`,
				taskContext
			}).then((res) => res.data);
		}
	}
	const testMail = isTestingMail(taskData, taskContext);

	const { data: list } = await executeQuery<GenericType[]>({
		abortSignal: abortController.signal,
		at: taskData,
		sql: `SELECT *
					FROM ${getBucketNameFromAppId(taskData.appId)}.${taskData.tableName} ${testMail ? 'LIMIT 1' : ''}`,
		taskContext
	});

	if (Array.isArray(list) && list.length > 0) {
		const emailProvider = await setupEmailProvider(
			taskData,
			abortController.signal
		);

		for (const data of list) {
			if (abortController.signal.aborted) break;
			if (
				taskData.sleep &&
				isNumber(taskData.sleep) &&
				Number(taskData.sleep) > 0
			) {
				await sleep(Number(taskData.sleep));
				if (abortController.signal.aborted) break;
			}
			if (!taskData.toColumnName || !data[taskData.toColumnName]) {
				await saveLog(
					'error',
					taskData,
					`No email address found in ${taskData.toColumnName} column`,
					data[taskData.resultTableReferenceId] || data[taskData.toColumnName],
					taskContext,
					abortController.signal
				);
			} else {
				if (testMail) {
					data[taskData.toColumnName] = taskData.testMail;
				}
				await sendEmail(
					taskData,
					data,
					extraData,
					emailProvider,
					taskContext,
					abortController.signal
				);
			}
		}
	}
	await keepLogVerify(taskData, taskContext, abortController.signal);
	return { message: 'success' };
}

const isTestingMail = (
	taskData: MailTaskType,
	taskContext: TaskContextType
) => {
	let testMail = false;
	const { logFrom, userRole } = taskContext;

	if (
		taskData.testMail &&
		logFrom === 'studio' &&
		['admin', 'dev'].includes(userRole)
	) {
		testMail = true;
	}

	return testMail;
};

const setupEmailProvider = async (
	taskData: MailTaskType,
	abortSignal?: AbortSignal
): Promise<EmailService> => {
	return {
		sendEmail: async (message: EmailMessage): Promise<EmailResult> => {
			try {
				const provider = await createEmailProvider(taskData, abortSignal);
				await provider.send(message);
				return {
					success: true,
					message: null
				};
			} catch (error) {
				return {
					success: false,
					message: error instanceof Error ? error.message : 'Unknown error'
				};
			}
		}
	};
};

const sendEmail = async (
	taskData: MailTaskType,
	data: GenericType,
	extraData: GenericType,
	emailService: EmailService,
	taskContext: TaskContextType,
	abortSignal?: AbortSignal
) => {
	const emailMessage: EmailMessage = {
		from: taskData.from,
		to: data[taskData.toColumnName],
		subject: prepareTextByParams(
			taskData.subject,
			taskContext.params,
			data,
			extraData
		),
		html: prepareTextByParams(
			taskData.message,
			taskContext.params,
			data,
			extraData
		)
	};

	const result = await emailService.sendEmail(emailMessage);

	const referenceId = taskData.resultTableReferenceId
		? data[taskData.resultTableReferenceId]
		: emailMessage.to;

	if (result.success) {
		await saveLog(
			'success',
			taskData,
			result.message || 'Success',
			referenceId,
			taskContext,
			abortSignal
		);
	} else {
		await saveLog(
			'error',
			taskData,
			result.message || 'Unknown error',
			referenceId,
			taskContext,
			abortSignal
		);
	}
};

const saveLog = async (
	status: string,
	taskData: MailTaskType,
	result: string,
	referenceId: string,
	taskContext: TaskContextType,
	abortSignal?: AbortSignal
) => {
	if (!taskData.resultTable) {
		return;
	}

	const safeMessage = result.replace(/'/g, "''").replaceAll('[unemail]', ''); // Escape single quotes
	const safeStatus = status.replace(/'/g, "''"); // Escape single quotes

	await executeQuery({
		at: taskData,
		sql: `INSERT INTO ${getBucketNameFromAppId(taskData.appId)}.${taskData.resultTable} (reference_id, message, status, created_at)
                    VALUES ('${referenceId}', '${safeMessage}', '${safeStatus}', now())`,
		taskContext,
		abortSignal
	});
};

const prepareResult = async (
	taskData: MailTaskType,
	taskContext: TaskContextType,
	abortSignal?: AbortSignal
) => {
	if (taskData.resultTable && taskData.resultTable !== '') {
		if (taskData.resultTableTruncate) {
			// DROP BEFORE
			await dropTableIfExist(taskData, taskContext);
		}
		await executeQuery({
			at: taskData,
			sql: `
				CREATE TABLE IF NOT EXISTS ${getBucketNameFromAppId(taskData.appId)}.${taskData.resultTable}
				(
					created_at
					DateTime
					DEFAULT
					now
				(
				),
					reference_id Nullable
				(
					String
				),
					message Nullable
				(
					String
				),
					status Nullable
				(
					String
				)
					) ENGINE = MergeTree ORDER BY tuple
				(
				)`,
			taskContext,
			abortSignal
		});
	}
	return { message: 'Table result created' };
};

const keepLogVerify = async (
	taskData: MailTaskType,
	taskContext: TaskContextType,
	abortSignal?: AbortSignal
) => {
	if (taskData.resultTable && Number(taskData.keepLogDays || 0) > 0) {
		await executeQuery({
			at: taskData,
			sql: `ALTER TABLE ${getBucketNameFromAppId(taskData.appId)}.${taskData.resultTable}
			DELETE
			WHERE created_at < subtractDays(now(), ${Number(taskData.keepLogDays) || 2})`,
			taskContext,
			abortSignal
		});
	}
	return { message: 'Deleted' };
};

const prepareTextByParams = (
	text: string,
	params: ParamType[],
	fieldDataValues: GenericType,
	extraData: GenericType
) => {
	const result = templateString(text, params, {
		field: fieldDataValues,
		table: extraData
	});

	return result;
};
