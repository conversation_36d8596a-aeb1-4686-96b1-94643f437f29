import type { CreateTableTaskType, TaskContextType } from '@gaio/shared/types';
import {
	createTableAtBucket,
	dropTableIfExist,
	getColumnSchema
} from '../runner.tools';

export default async function (
	taskData: CreateTableTaskType,
	taskContext: TaskContextType,
	_: AbortController
) {
	if (taskData.dropTable) {
		await dropTableIfExist(taskData, taskContext);
	}

	await createTableAtBucket(
		taskData,
		await getColumnSchema(taskData),
		taskContext
	);

	return { status: 'done' };
}
