import { dbGaio } from '@/db/db.gaio';
import type {
	FieldType,
	GenericType,
	TaskContextType,
	UserTaskType,
	UserType
} from '@gaio/shared/types';
import {
	createTableAtBucket,
	dropTableIfExist,
	getColumnSchema,
	streamToBucket
} from '../runner.tools';

const openFields = [
	'userId',
	'name',
	'email',
	'createdAt',
	'updatedAt',
	'role',
	'group'
];

const types = {
	createdAt: {
		dataType: 'Nullable(DateTime)'
	},
	email: {
		dataType: 'Nullable(String)'
	},
	group: {
		dataType: 'Nullable(String)'
	},
	name: {
		dataType: 'Nullable(String)'
	},
	role: {
		alias: 'type',
		dataType: 'Nullable(String)'
	},
	updatedAt: {
		dataType: 'Nullable(DateTime)'
	},
	userId: {
		dataType: 'Nullable(String)'
	}
};

export default async function (
	taskData: UserTaskType,
	taskContext: TaskContextType,
	_: AbortController
) {
	await dropTableIfExist(taskData, taskContext);

	const fields = taskData.userMirrorFields.filter((f) =>
		openFields.includes(f)
	);

	await createUserTable(taskData, fields, taskContext);

	try {
		const groups = (await listAllGroups()) as GenericType[];

		const users = (await listUsers(fields)) as UserType[];
		return await populateUserTable(taskData, users, fields, groups);
	} catch (e) {
		throw { code: 'error', message: e.message };
	}
}

async function createUserTable(
	taskData: UserTaskType,
	fields: string[],
	taskContext: TaskContextType
) {
	const baseColumns: FieldType[] = [
		{
			columnName: 'userId',
			...types.userId
		}
	];

	for (const columnName of fields) {
		if (types[columnName as keyof typeof types]) {
			baseColumns.push({
				columnName,
				...types[columnName as keyof typeof types]
			});
		}
	}

	const columns = await getColumnSchema({
		...taskData,
		columns: baseColumns
	});

	return await createTableAtBucket(taskData, columns, taskContext);
}

async function listUsers(fields: string[]) {
	const prepareFields = fields.map((f) => {
		if (f === 'role' || f === 'type') {
			return 'user.role as type';
		}
		if (f === 'group') {
			return 'user.tags as tags';
		}
		return `user.${f}`;
	});

	try {
		return await dbGaio().query(
			`
				SELECT user.userId,
							 ${prepareFields.toString()}
				FROM user
				WHERE user.role != 'portal' 
				AND user.role != 'group' 
				AND isNotNull(user.email)
				AND user.email != ''
			`
		);
	} catch (e) {
		throw {
			code: 'error',
			message: `Can\'t run query on Gaio Admin - ${e.message}`
		};
	}
}

async function listAllGroups() {
	return await dbGaio().query(
		`
			SELECT userId, name
			FROM user
			WHERE role = 'group'`
	);
}

async function populateUserTable(
	taskData: UserTaskType,
	users: UserType[],
	fields: string[],
	groups: GenericType[]
) {
	const willGetGroups = fields
		.map((o) => {
			if (o === 'role') {
				return 'type';
			}
			return o;
		})
		.filter((o) => o === 'group').length;

	const data: GenericType[] = [];

	for (const item of users) {
		let baseTags = '';
		if (willGetGroups) {
			baseTags = (item.tags || [])
				.map((o) => groups.find((g) => g.userId === o)?.name || '')
				.filter((o) => o)
				.toString();

			baseTags = baseTags || '';
		}

		data.push({
			...item,
			tags: baseTags
		});
	}

	await streamToBucket({
		at: taskData,
		tableName: taskData.resultTable,
		data
	});

	return { status: 'done' };
}
