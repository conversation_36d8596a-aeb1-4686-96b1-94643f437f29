import { join } from 'node:path';
import RepoRepository from '@/components/repo/repo.repository';
import { createBashScriptEnvRunner, generateFile } from '@/utils/index';
import {
	associationRulesScript,
	clusterScript,
	// autoMLScript,
	forecastScript,
	pcaScript
} from '@/utils/python-content';
// import type { AutoMLTaskType } from '@gaio/shared/types';
import type {
	AssociationRulesTaskType,
	ClusterTaskType,
	ForecastTaskType,
	PcaTaskType,
	PythonHubTaskType,
	TaskContextType
} from '@gaio/shared/types';
import { ensureDirSync } from 'fs-extra';
import { shell } from '../runner.shell';
import { contentFolder, pythonEnvFactory } from '../runner.tools';

export default async function (
	taskData: PythonHubTaskType | ClusterTaskType | PcaTaskType,
	taskContext: TaskContextType,
	abortController: AbortController
) {
	const paths = pathNames(taskData);
	switch (taskData.type) {
		case 'basket':
			await associationRules(
				taskData,
				paths,
				taskContext,
				abortController.signal
			);
			break;
		case 'cluster':
			await cluster(
				taskData as ClusterTaskType,
				paths,
				taskContext,
				abortController.signal
			);
			break;
		case 'pca':
			await pca(
				taskData as PcaTaskType,
				paths,
				taskContext,
				abortController.signal
			);
			break;
		// case 'automl':
		// 	await automl(
		// 		taskData as AutoMLTaskType,
		// 		paths,
		// 		taskContext,
		// 		abortController.signal
		// 	);
		// 	break;
		case 'forecast':
			await forecast(taskData as ForecastTaskType, paths, taskContext);
			break;
	}

	return { status: 'done' };
}

type PathNames = {
	envPath: string;
	mainPath: string;
	runnerPath: string;
};

async function associationRules(
	taskData: PythonHubTaskType,
	paths: PathNames,
	taskContext: TaskContextType,
	abortController: AbortSignal
) {
	const repoData = await RepoRepository.getRepoCredentials(taskData);
	const logComment = pyClientLogComment(taskData, taskContext);
	const scriptContent = associationRulesScript(
		repoData,
		taskData as AssociationRulesTaskType,
		logComment
	);
	const envName = pythonEnvFactory('basket');

	await prepareScripts(envName, scriptContent, paths);
	await shell(
		'Running association rules',
		'bash',
		[paths.envPath],
		abortController
	);
}

// async function automl(
// 	taskData: AutoMLTaskType,
// 	paths: PathNames,
// 	taskContext: TaskContextType,
// 	abortController: AbortSignal
// ) {
// 	const repoData = await RepoRepository.getRepoCredentials(taskData);
// 	const logComment = pyClientLogComment(taskData, taskContext);
// 	const scriptContent = autoMLScript(repoData, taskData, logComment);
// 	const envName = pythonEnvFactory('cluster');

// 	await prepareScripts(envName, scriptContent, paths);
// 	await shell('Running automl', 'bash', [paths.envPath], abortController);
// }

async function cluster(
	taskData: ClusterTaskType,
	paths: PathNames,
	taskContext: TaskContextType,
	abortController: AbortSignal
) {
	const repoData = await RepoRepository.getRepoCredentials(taskData);
	const logComment = pyClientLogComment(taskData, taskContext);
	const scriptContent = clusterScript(repoData, taskData, logComment);
	const envName = pythonEnvFactory('cluster');

	await prepareScripts(envName, scriptContent, paths);
	await shell('Running cluster', 'bash', [paths.envPath], abortController);
}

async function pca(
	taskData: PcaTaskType,
	paths: PathNames,
	taskContext: TaskContextType,
	abortController: AbortSignal
) {
	const repoData = await RepoRepository.getRepoCredentials(taskData);
	const logComment = pyClientLogComment(taskData, taskContext);
	const scriptContent = pcaScript(repoData, taskData, logComment);
	const envName = pythonEnvFactory('pca');

	await prepareScripts(envName, scriptContent, paths);
	await shell('Running pca', 'bash', [paths.envPath], abortController);
}

async function forecast(
	taskData: ForecastTaskType,
	paths: PathNames,
	taskContext: TaskContextType
) {
	const repoData = await RepoRepository.getRepoCredentials(taskData);
	const logComment = pyClientLogComment(taskData, taskContext);
	const scriptContent = await forecastScript(repoData, taskData, logComment);
	const envName = pythonEnvFactory('forecast');

	await prepareScripts(envName, scriptContent, paths);

	await shell('Running forecast', 'bash', [paths.envPath]);
}

async function prepareScripts(
	envKeyName: string,
	scriptContent: string,
	paths: PathNames
) {
	ensureDirSync(paths.mainPath);
	await generateFile(
		paths.envPath,
		createBashScriptEnvRunner(envKeyName, paths.runnerPath)
	);
	await generateFile(paths.runnerPath, scriptContent);
}

function pathNames(taskData: PythonHubTaskType): PathNames {
	const mainPath = join(
		contentFolder,
		'apps',
		taskData.appId.replace('app:', ''),
		taskData.type
	);
	const envPath = join(mainPath, `env_${taskData.id}.sh`);
	const runnerPath = join(mainPath, `run_${taskData.id}.py`);

	return {
		envPath,
		mainPath,
		runnerPath
	};
}

function pyClientLogComment(
	taskData: PythonHubTaskType,
	taskContext: TaskContextType
) {
	const { appId, id, runningFlowId, type } = taskData;
	const { userId } = taskContext;

	const gaioQt = {
		appId: appId || '',
		flowId: runningFlowId || '',
		ref: 'gaio_qt',
		taskId: id || '',
		type: type || '',
		userId: userId || ''
	};

	return `settings = {'log_comment':'${JSON.stringify(gaioQt)}'}`;
}
