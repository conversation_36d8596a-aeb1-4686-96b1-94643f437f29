import { spawn } from 'node:child_process';
import { join } from 'node:path';
import RepoRepository from '@/components/repo/repo.repository';
import { dbGaio } from '@/db/db.gaio';
import type { CodeSnippetEntity } from '@/db/entities';
import { gaioRedis } from '@/db/gaioRedis';
import templateString from '@/utils/template-string';
import type { ParamType } from '@gaio/shared/types';
import type { PythonTaskType, TaskContextType } from '@gaio/shared/types';
import { getRepositoryUserNameFromAppId } from '@gaio/shared/utils';
import { getAppNumberReference } from '@gaio/shared/utils/libs/helpers';
import { ensureDirSync } from 'fs-extra';
import { contentFolder } from '../runner.tools';

async function generateFile(
	path: string,
	data: string | ArrayBufferLike,
	opts = {}
) {
	await new Promise((resolve, reject) => {
		Bun.write(Bun.file(path), data, opts)
			.then(() => {
				resolve({ status: 'saved' });
			})
			.catch((err) => {
				reject(err);
			});
	});
}

export default async function (
	taskData: PythonTaskType,
	taskContext: TaskContextType,
	abortController: AbortController
) {
	const { params } = taskContext;
	taskData.body = await loadContentBody(taskData);

	await makeSureDirExist(taskData, `env/${taskData.id}`);
	const bashScript = await preparePathFile(taskData, params);

	if (!taskData.body) {
		return sendMessage(taskData, 'error', 'Nothing to run', taskContext);
	}

	return await runPython(taskData, bashScript, taskContext, abortController);
}

async function loadContentBody(taskData: PythonTaskType): Promise<string> {
	const data = await dbGaio()
		.query<CodeSnippetEntity & { role: string }>(
			"select codeSnippet from code_snippet where codeSnippetId = { codeSnippetId: String } and type = 'python'",
			{
				params: { codeSnippetId: taskData.codeSnippetId }
			}
		)
		.then((res) => {
			if (res[0]?.codeSnippet) {
				return res[0];
			}
			return {
				codeSnippet: '',
				role: ''
			};
		});

	if (data.role === 'session') {
		await dbGaio().query(
			`delete
			 from code_snippet
			 where codeSnippetId = { codeSnippetId: String }`,
			{
				params: { codeSnippetId: taskData.codeSnippetId }
			}
		);
	}

	return data.codeSnippet;
}

async function runPython(
	taskData: PythonTaskType,
	bashScript: string,
	taskContext: Partial<{
		from: string;
		userId: string;
		userStatus: 'inactive' | 'active';
		userRole: 'user' | 'admin' | 'dev';
		sessionid: string;
		appId: string;
		flowId: string;
		params: ParamType[];
	}>,
	abortController: AbortController
) {
	sendMessage(taskData, 'info', '🚀 Started! \n', taskContext);

	const finalPath = join(
		contentFolder,
		'apps',
		getAppNumberReference(taskData.appId),
		'assets'
	);

	const shell = spawn('bash', [bashScript], {
		cwd: finalPath,
		shell: true,
		signal: abortController.signal
	});

	let error = '';

	shell.stdout.on('data', (data) => {
		sendMessage(taskData, 'info', data.toString(), taskContext);
	});

	shell.stderr.on('data', (data) => {
		const base = Buffer.from(data);
		if (error.length > 4414) {
			error = '';
		}
		error += ` ${base.toString()} `;
	});

	return await new Promise((resolve, reject) => {
		shell.on('exit', (code) => {
			if (code <= 0) {
				// removeLocal([finalPathFolder])
				// removeFileTrash(taskData)
				sendMessage(taskData, 'info', '🐍 Ended', taskContext);
				resolve({ status: true });
			} else {
				// removeLocal([finalPathFolder])
				// removeFileTrash(taskData)
				sendMessage(taskData, 'error', error, taskContext);
				sendMessage(taskData, 'info', '🐍 Ended', taskContext);
				setTimeout(() => {
					reject({
						code: 'error',
						message: `${error}`.replace('password', '')
					});
				});
			}
		});
	});
}

function sendMessage(
	taskData: PythonTaskType,
	type: string,
	data: string,
	taskContext: TaskContextType
) {
	if (taskContext.logFrom === 'studio') {
		const finalPath = join(
			contentFolder,
			'apps',
			`${taskData.appId.replace('app:', '')}`
		);

		// send socket
		gaioRedis.pub.publish(
			`type:python-${taskData.codeSnippetId}`,
			`${data}`.replace(finalPath, '').replace(contentFolder, '')
		);
	}
}

async function renderParams(query: string, params: ParamType[]) {
	return templateString(query, params);
}

async function preparePathFile(taskData: PythonTaskType, params: ParamType[]) {
	const assetsFolder = join(
		contentFolder,
		'apps',
		taskData.appId.replace('app:', ''),
		'/'
	);

	const bashActivateEnv = join(
		contentFolder,
		'apps',
		`${taskData.appId.replace('app:', '')}`,
		'env',
		taskData.id,
		'start.sh'
	);
	const scriptFile = join(
		contentFolder,
		'apps',
		`${taskData.appId.replace('app:', '')}`,
		'env',
		taskData.id,
		'main.py'
	);
	let scriptBody = await renderParams(taskData.body, params);
	const repoConnectionBody = await prepareRepositoryConnection(taskData);

	const baseIdent = `@memory_limit()\ndef gaio_main():\n  ${scriptBody.replace(/\n/g, '\n  ')}`;

	scriptBody = `
import psutil
import functools

app_inputs = '${assetsFolder}/inputs'
app_outputs = '${assetsFolder}/outputs'
app_assets = '${assetsFolder}/assets'

def memory_limit(limit=0.8):
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            process = psutil.Process()
            start_mem = process.memory_info().rss
            func(*args, **kwargs)
            end_mem = process.memory_info().rss
            mem_usage = end_mem - start_mem
            if mem_usage > limit * psutil.virtual_memory().total:
                raise MemoryError(f"MemoryError: Memory usage exceeded {limit*100:.2f}%")
        return wrapper
    return decorator

${repoConnectionBody}

${baseIdent}

if __name__ == '__main__':
    gaio_main()`;

	await generateFile(
		bashActivateEnv,
		createBashScriptFromContent(
			`env_${getAppNumberReference(taskData.appId)}`,
			scriptFile
		)
	);
	await generateFile(scriptFile, scriptBody);

	return bashActivateEnv;
}

async function makeSureDirExist(taskData: PythonTaskType, path: string) {
	ensureDirSync(
		join(contentFolder, 'apps', `${taskData.appId.replace('app:', '')}`, path)
	);
	ensureDirSync(
		join(
			contentFolder,
			'apps',
			`${taskData.appId.replace('app:', '')}`,
			'assets'
		)
	);
}

async function prepareRepositoryConnection(taskData: PythonTaskType) {
	const credentials = await RepoRepository.getRepoCredentials(taskData).then(
		(result) => result
	);

	if (credentials) {
		const strRandoVariable = `db_${Math.random().toString(36).substring(7)}`;

		let body = `import re \n
import clickhouse_connect\n
import unicodedata \n`;
		body += `${strRandoVariable} = clickhouse_connect.get_client(
                                                host='${credentials.host}',
                                                username='${getRepositoryUserNameFromAppId(taskData.appId)}',
                                                password='${credentials.password}',
                                                port=${credentials.port},
                                                database='bucket_${taskData.appId.replace('app:', '')}')\n`;

		body += `
class bucket:
    def query(str, parameters = None):
        result = ${strRandoVariable}.query(str, parameters)
        return result

    def query_df(str, parameters = None):
        result = ${strRandoVariable}.query_df(str, parameters)
        return result

    def command(str, parameters = None):
        result = ${strRandoVariable}.command(str, parameters)
        return result

    def insert_df(str, df):
        result = ${strRandoVariable}.insert(str, df)
        return result

    def select_df(table_name):
        result = ${strRandoVariable}.query_df(f"select * from {table_name}")
        return result

    def create_df(table_name, df):
        type_mapping = {
            "int64": "Nullable(Int64)",
            "float64": "Nullable(Float64)",
            "int8": "Nullable(Int64)",
            "int16": "Nullable(Int64)",
            "int32": "Nullable(Int64)",
            "float16": "Nullable(Float64)",
            "float32": "Nullable(Float64)"
        }

        drop_table_query = "DROP TABLE IF EXISTS " + table_name
        ${strRandoVariable}.command(drop_table_query)

        def remove_special_chars(text):
            text = re.sub(r'\\W+', '_', text)  # Replace special characters with underscore
            text = ''.join(c for c in unicodedata.normalize('NFKD', text) if not unicodedata.combining(c))  # Remove accents
            return text

        column_names = [remove_special_chars(name) for name in df.columns]

        column_definitions = [
            remove_special_chars(name) + " " + type_mapping.get(str(ctype), 'Nullable(String)')
            for name, ctype in zip(column_names, df.dtypes)]

        create_table_query = "CREATE TABLE " + table_name + " (" + ", ".join(column_definitions) + ") ENGINE = MergeTree() ORDER BY tuple();"
        ${strRandoVariable}.command(create_table_query)




        `;
		return body;
	}

	return '';
}

function createBashScriptFromContent(envKey: string, scriptFile: string) {
	return [
		'#!/bin/bash',
		`eval "$(pyenv init -)"`,
		`eval "$(pyenv virtualenv-init -)"`,
		`pyenv activate ${envKey}`,
		`python3 ${scriptFile}`
	].join('\n');
}
