import { aml, automlRest } from '@/components/automl/automl.core';
import type { ScoringTaskType, TaskContextType } from '@gaio/shared/types';
import type { GenericType } from '@gaio/shared/types';
import type { FieldType } from '@gaio/shared/types';
import { getBucketNameFromAppId } from '@gaio/shared/utils';
import { chunk, get } from 'lodash-es';
import {
	createTableAtBucket,
	dropTableIfExist,
	executeQuery,
	modelsFolder,
	sanitizeColumnName
} from '../runner.tools';
import {
	importFile,
	parseFrame,
	parseSetup,
	createLocalData,
	fetchH2OJobProgress,
	deleteFrames, 
	deleteModelFrame
} from './runner.automl'

type H2oModelType = {
	context: string | FrameDataType;
	type: 'automl' | unknown;
	modelRef?: string;
};

type FrameDataType = {
	columns: never[][];
	rows: number;
	fields: FieldType[];
	offset: number;
	limit?: number;
};

export default async function (
	taskData: ScoringTaskType,
	taskContext: TaskContextType,
	_: AbortController
) {
	await dropTableIfExist(taskData, taskContext);

	const model = {
		context: taskData.modelId,
		type: 'automl',
		modelRef: taskData.modelId
	};

	return await processFromAutoml(
		taskData,
		model.context as string,
		taskContext
	);
}

// 0. FROM AUTOML
async function processFromAutoml(
	taskData: ScoringTaskType,
	modelId: string,
	taskContext: TaskContextType
) {
	await deleteFrames(taskData.id);
	await prepareData(taskData);
	const model = await importModel(taskData, modelId);
	
	await predictions(taskData, model);
	await rapids(taskData);
	const frameData = await listAlData(taskData, {
		columns: [],
		fields: [],
		offset: 0,
		rows: 1
	});
	await createTableToAutoml(taskData, frameData, taskContext);
	await deleteModelFrame(model, taskData.id);
	await deleteFrames(taskData.id);
	return insertData(taskData, frameData, taskContext);
}

// # 0.9
async function prepareData(taskData: ScoringTaskType) {
	await createLocalData(taskData, false);

	const sourceFrames = await importFile(taskData.appId, taskData.id);
	const parseSetupData = await parseSetup(sourceFrames);
	const parse = await parseFrame(parseSetupData);

	let shouldFetchProgress = true;

	while (shouldFetchProgress) {
		const progress = await fetchH2OJobProgress(parse?.job?.key?.name);

		if (!progress) {
			shouldFetchProgress = false;
			throw new Error(`Failed to fetch H2O Job Progress - ${progress}`);
		}

		if (progress instanceof Error) {
			shouldFetchProgress = false;
			throw new Error(progress.message);
		}

		if (progress.status === 'RUNNING') {
			await new Promise((resolve) => setTimeout(resolve, 100));
			continue;
		}

		shouldFetchProgress = false;

		return;
	}
}

// # 1 import saved model
async function importModel(taskData: ScoringTaskType, modelId: string) {
	const autoPath = modelsFolder(taskData.appId, modelId);

	try {
		const response = await automlRest.post(
			aml('99/Models.bin/not_in_use'),
			{
				dir: `${autoPath}/winner`,
				force: true
			},
			{
				'Content-Type': 'application/x-www-form-urlencoded'
			}
		);
		return response.models[0].model_id.name;
	} catch (error) {
		throw new Error(`Failed to parse setup - ${error}`);
	}
}

// # 2 LOAD PREDICT
async function predictions(
	taskData: ScoringTaskType,
	model: H2oModelType | string
) {
	const frameName = `Key_Frame__${taskData.id}.hex`;
	const response = await automlRest.post(
		aml(`3/Predictions/models/${model}/frames/${frameName}`),
		{
			predictions_frame: taskData.id
		},
		{
			'Content-Type': 'application/x-www-form-urlencoded'
		}
	);
	
	return response;
}

// # 3 PREDICT PREPARE COMBINED
async function rapids(taskData: ScoringTaskType) {
	const frameName = `Key_Frame__${taskData.id}.hex`;
	const response = await automlRest.post(
		aml('99/Rapids'),
		{
			ast: `(assign combined-${taskData.id} (cbind ${taskData.id} ${frameName}))`
		},
		{
			'Content-Type': 'application/x-www-form-urlencoded'
		}
	);
	return response;
}

// # 4 LIST RECURSIVELY
async function listAlData(taskData: ScoringTaskType, frameData: FrameDataType) {
	frameData = await listCombined(taskData, frameData);

	if (frameData.columns.length < frameData.rows) {
		return await listAlData(taskData, frameData);
	}

	return frameData;
}

// # 4.1 LIST COMBINED DATA PREDICT + CSV
async function listCombined(
	taskData: ScoringTaskType,
	frameData: FrameDataType
) {
	const response = await automlRest.post(
		aml(
			`3/Frames/combined-${taskData.id}?row_offset=${frameData.offset}&row_count=1000`
		),
		{},
		{
			'Content-Type': 'application/x-www-form-urlencoded'
		}
	);
	
	
	const baseColumns = get(response, 'frames[0].columns');

	return {
		offset: frameData.offset + 1000,
		rows: get(response, 'frames[0].rows'),
		fields: get(response, 'frames[0].columns'),
		columns: frameData.columns.concat(
			prepareColumnData(get(response, 'frames[0].columns'))
		)
	};
}

// # 4.2 ORGANIZE DATA FROM COLUMNS
function prepareColumnData(columns: GenericType[]) {
	const result: GenericType[] = [];
	for (const [colIndex, col] of columns.entries()) {
		if (col.domain) {
			for (const [dtIndex, dt] of col.data.entries()) {
				result[dtIndex] = result[dtIndex] || [];
				result[dtIndex][colIndex] = col.domain[dt] || '';
			}
		} else if (col.data) {
			for (const [dtIndex, dt] of col.data.entries()) {
				result[dtIndex] = result[dtIndex] || [];
				result[dtIndex][colIndex] = dt || '';
			}
		} else if (col.string_data) {
			for (const [sdIndex, sd] of col.string_data.entries()) {
				result[sdIndex] = result[sdIndex] || [];
				result[sdIndex][colIndex] = sd || '';
			}
		}
	}
	return result;
}

// $ 5 CREATE TABLE FROM automl
async function createTableToAutoml(
	taskData: ScoringTaskType,
	frameData: FrameDataType,
	taskContext: TaskContextType
) {
	const { fields } = frameData;
	const generateColumnType = (col: FieldType) => {
		if (['int', 'integer'].includes(col.type)) {
			return 'Nullable(Int64)';
		}
		if (
			[
				'double',
				'real',
				'Real',
				'Decimal',
				'decimal',
				'Numeric',
				'numeric'
			].includes(col.type)
		) {
			return 'Nullable(Float64)';
		}
		if (['time'].includes(col.type)) {
			return "Nullable(DateTime64(3, 'UTC'))";
		}
		return 'Nullable(String)';
	};

	const fieldNames = fields.map((col) => {
		const columnName = sanitizeColumnName(`${col.label}`);
		const columnType = generateColumnType(col);
		return `${columnName} ${columnType}`;
	});

	return createTableAtBucket(
		taskData,
		{
			fieldTypes: fieldNames.toString()
		},
		taskContext
	);
}

// # 6 INSERT
async function insertData(
	taskData: ScoringTaskType,
	frameData: FrameDataType,
	taskContext: TaskContextType
) {
	const { columns, fields } = frameData;
	const fieldNames = fields.map((col) => sanitizeColumnName(`${col.label}`));
	const newCols = chunk<Array<Array<GenericType>>>(columns, 500);

	for (const colList of newCols) {
		const toInsert = [];
		for (const res of colList) {
			toInsert.push(
				`(${(res || []).map((o) => (o ? `'${o}'` : "''")).toString()})`
			);
		}
		if (toInsert.length > 0) {
			await executeQuery({
				at: taskData,
				sql: `INSERT INTO ${getBucketNameFromAppId(taskData.appId)}.${taskData.resultTable}
									(${fieldNames.toString()})
								VALUES
									${toInsert.join(',')}`,
				taskContext
			});
		}
	}

	return { status: 'Success' };
}
