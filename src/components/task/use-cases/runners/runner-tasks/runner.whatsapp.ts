import templateString from '@/utils/template-string';
import type {
	GenericType,
	TaskContextType,
	WhatsappTaskType
} from '@gaio/shared/types';
import { getBucketNameFromAppId } from '@gaio/shared/utils';
import { isObject } from 'lodash-es';
import twilio from 'twilio';
import { dropTableIfExist, executeQuery } from '../runner.tools';

export default async function (
	taskData: WhatsappTaskType,
	taskContext: TaskContextType,
	abortController: AbortController
) {
	await prepareResult(taskData, taskContext);
	const { data: list } = await listData(taskData, taskContext, abortController);

	for (const data of list) {
		await sendData(taskData, data, taskContext);
	}

	return { status: 'Done' };
}

async function listData(
	taskData: WhatsappTaskType,
	taskContext: TaskContextType,
	abortController: AbortController
): Promise<GenericType> {
	return await executeQuery({
		at: taskData,
		sql: `select * from ${taskData.tableName}`,
		taskContext,
		abortSignal: abortController.signal
	});
}

async function sendData(
	taskData: WhatsappTaskType,
	data: GenericType,
	taskContext: TaskContextType
) {
	switch (taskData.sender) {
		case 'twilio':
			return await sendTwilio(taskData, data, taskContext);
		case 'chat-api':
			return await sendChatApi(taskData, data, taskContext);
		default:
			return {};
	}
}

async function sendChatApi(
	taskData: WhatsappTaskType,
	data: GenericType,
	taskContext: TaskContextType
) {
	const { url, phoneTo, message } = taskData.options;

	try {
		const response = await fetch(url as string, {
			body: JSON.stringify({
				body: data[message as string],
				phone: data[phoneTo as string]
			}),
			headers: {
				'Content-Type': 'application/json'
			},
			method: 'POST'
		});

		const responseData = await response.json();
		return saveResult(taskData, responseData, taskContext);
	} catch (error) {
		return saveResult(
			taskData,
			{
				code: error.code || error.name,
				message: error.message
			},
			taskContext
		);
	}
}

async function sendTwilio(
	taskData: WhatsappTaskType,
	data: GenericType,
	taskContext: TaskContextType
) {
	const accountSid = taskData.options.twilioAccountSid as string;
	const authToken = taskData.options.twilioAuthToken as string;

	const template = taskData.templates.find(
		(o: GenericType) => o.template_id === data.template_id
	);

	if (template && data.phone_to) {
		const client = twilio(accountSid, authToken);
		return await client.messages
			.create({
				body: prepareDataByParameters(template.text, data),
				from: `whatsapp:${taskData.options.phoneFrom}`,
				to: `whatsapp:${data.phone_to}`
			})
			.then((message) => saveResult(taskData, message, taskContext))
			.catch((err) => saveResult(taskData, err, taskContext));
	}
	return { status: 'Can`t send message' };
}

async function saveResult(
	taskData: WhatsappTaskType,
	result: unknown,
	taskContext: TaskContextType
) {
	const saveText = isObject(result) ? JSON.stringify(result) : result;
	return await executeQuery({
		at: taskData,
		sql: `insert into ${getBucketNameFromAppId(taskData.appId)}.${taskData.resultTable} 
                (message) values ('${saveText}')`,
		taskContext
	});
}

const prepareResult = async (
	taskData: WhatsappTaskType,
	taskContext: TaskContextType
) => {
	if (taskData.resultTable && taskData.resultTable !== '') {
		if (taskData.resultTableTruncate) {
			// DROP BEFORE
			await dropTableIfExist(taskData, taskContext);
		}
		await prepareResultTableAtBucket(taskData, taskContext);
	}
	return { status: 'success' };
};

async function prepareResultTableAtBucket(
	taskData: WhatsappTaskType,
	taskContext: TaskContextType
) {
	// TABLE TO WHERE DATA WILL BE INSERTED
	await executeQuery({
		at: taskData,
		sql: `
        CREATE TABLE IF NOT EXISTS ${getBucketNameFromAppId(taskData.appId)}.${taskData.resultTable} (
            created_at DateTime DEFAULT now(),
            message Nullable(String)
        )  ENGINE = MergeTree ORDER BY tuple()`,
		taskContext
	});
}

function prepareDataByParameters(txt: string, item: GenericType) {
	const params = [];
	for (const key in item) {
		if (Object.hasOwn(item, key)) {
			params.push({
				paramName: key,
				paramValue: item[key]
			});
		}
	}
	return templateString(txt, params);
}
