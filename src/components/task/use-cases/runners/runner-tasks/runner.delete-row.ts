import { QueryBuilder } from '@/components/builder/use-cases/builder';
import type { DeleteTaskType } from '@gaio/shared/types';
import type { TaskContextType } from '@gaio/shared/types';
import { executeQuery, killMutation } from '../runner.tools';

export default async function (
	taskData: DeleteTaskType,
	taskContext: TaskContextType,
	abortController: AbortController
) {
	await killMutation({
		databaseName: taskData.databaseName,
		tableName: taskData.tableName
	});

	await executeQuery({
		abortSignal: abortController.signal,
		at: taskData,
		sql: await new QueryBuilder().generate(taskData, []),
		taskContext
	});

	return { status: 'done' };
}
