import { readFileSync, readdirSync, writeFileSync } from "node:fs";
import {
  aml,
  automlRest,
  checkAutoMLStatus,
} from "@/components/automl/automl.core";
import RepoRepository from "@/components/repo/repo.repository";
import { gaioRedis } from "@/db/gaioRedis";
import type {
  FieldType,
  ScoringTaskType,
  TaskContextType,
} from "@gaio/shared/types";
import type {
  AutoMLTaskType,
  AutoMLTaskTypeSettings,
  AutoMLOutput,
} from "@gaio/shared/types";
import { ensureDirSync } from "fs-extra";
import { shell } from "../runner.shell";
import {
  createTableAtBucket,
  dropTableIfExist,
  getColumnSchema,
  logComment,
  modelsFolder,
  removeLocal,
  resourcesFolder,
  streamToBucket,
  transformColumnsToRows,
} from "../runner.tools";
import { listModelByNameUseCase } from "@/components/automl/automl.list-model-by-name";
import { getId } from "@gaio/shared/utils";
import { $ } from "bun";
import { deburr } from "lodash-es";

export type H2OFrameKey = {
  name: string;
  type: string;
  URL: string;
};

export default async function (
  taskData: AutoMLTaskType,
  taskContext: TaskContextType,
  abortController: AbortController,
) {
  await checkAutoMLStatus();

  const projectName = `project_${taskData.id}_${getId()}`;

  const path = modelsFolder(taskData.appId, taskData.id);

  ensureDirSync(path);

  await deletePreviousFrames(taskData, path);
  await deleteFrames(taskData.id);

  removeLocal([path]);

  ensureDirSync(path);

  await createLocalData(taskData, true);

  await startAutoml(taskData, taskContext, projectName, path, abortController);

  await deleteFrames(taskData.id);

  return { status: "success" };
}

async function startAutoml(
  taskData: AutoMLTaskType,
  taskContext: TaskContextType,
  projectName: string,
  path: string,
  _abortController: AbortController,
) {
  try {
    const trainingData = await importFile(taskData.appId, taskData.id);

    const parseSetupData = await parseSetup(trainingData);

    const parse = await parseFrame(parseSetupData);

    await followProgress(parse.job, taskData, "AutomlSetup", taskContext);

    const builder = await automlBuilder({
      id: taskData.id,
      ...taskData.settings,
      destinationFrame: `${taskData.id}.hex`,
      projectName,
    });

    await followProgress(builder.job, taskData, "AutomlTrain", taskContext);

    await saveModel(
      taskData,
      taskData.settings.responseColumn,
      projectName,
      path,
      taskContext,
    );
  } catch (e: any) {
    throw new Error(e.message || e.error || String(e));
  }
}

async function saveModel(
  taskData: AutoMLTaskType,
  responseColumn: string,
  projectName: string,
  path: string,
  taskContext: TaskContextType,
) {
  try {
    const result = await automlRest.get(
      aml(
        `99/Leaderboards/${encodeURIComponent(`${projectName}@@${responseColumn}`)}`,
      ),
    );

    const { models, table, leaderboard_frame } = result;

    await automlRest.delete(aml(`3/Frames/${projectName}@@${responseColumn}`));
    await automlRest.delete(aml(`3/Models/${projectName}@@${responseColumn}`));
    await automlRest.delete(aml(`3/AutoML/${projectName}@@${responseColumn}`));

    if (leaderboard_frame?.name) {
      await automlRest.delete(aml(`3/Frames/${leaderboard_frame.name}`));
      await automlRest.delete(aml(`3/Models/${leaderboard_frame.name}`));
    }

    if (models.length === 0) {
      throw new Error(
        "No model found. The problem could be related to constant value data for some columns. Verify it your response variable values are not constant.",
      );
    }

    await automlRest.get(
      aml(`99/Models.bin/${models[0].name}?dir=${path}/winner&force=true`),
    );

    writeFileSync(`${path}/models_metadata.json`, JSON.stringify(table));

    if (taskData.settings.metadataTableResults && models[0]?.URL) {
      const modelName = models[0].URL;
      const savedData: AutoMLOutput = await listModelByNameUseCase({
        appId: taskData.appId,
        taskDataId: taskData.id,
        modelName,
      });

      if (savedData?.output?.variable_importances?.data) {
        await createMetadataTableVariableImportance(
          taskData,
          taskContext,
          savedData,
        );

        await createMetadataTableModel(taskData, taskContext, savedData);

        await createMetadataTableCrossValidation(
          taskData,
          taskContext,
          savedData,
        );

        await createMetadataTableConfusionMatrix(
          taskData,
          taskContext,
          savedData,
        );

        await createMetadataTableVariableImportanceFrequency(
          taskData,
          taskContext,
          savedData,
        );

        await createMetadataTableVariableImportanceCoverage(
          taskData,
          taskContext,
          savedData,
        );
      }
    }

    return { message: "Success to save model" };
  } catch (error) {
    throw new Error(error instanceof Error ? error.message : String(error));
  }
}

async function createMetadataTableVariableImportance(
  taskData: AutoMLTaskType,
  taskContext: TaskContextType,
  savedData: AutoMLOutput,
) {
  if (!savedData.output.variable_importances) return;

  const resultTable = `variable_importances_${taskData.resultTable}`;

  const columns: FieldType[] =
    savedData.output.variable_importances.columns.map((col) => ({
      tableName: resultTable,
      columnName: col.name ? col.name : "reference",
      dataType: ["float", "double"].includes(col.type)
        ? "Nullable(Float)"
        : "Nullable(String)",
    }));

  await dropTableIfExist(
    {
      ...taskData,
      resultTable,
    },
    taskContext,
  );

  await createTableAtBucket(
    {
      ...taskData,
      resultTable,
    },
    await getColumnSchema({
      ...taskData,
      columns,
    }),
    taskContext,
  );

  const columnNames = columns.map((col) => col.columnName);

  const rowCount = savedData.output.variable_importances.rowcount;
  const dataArrays = savedData.output.variable_importances.data;
  const result = transformColumnsToRows(columnNames, dataArrays, rowCount);

  try {
    await streamToBucket({
      at: taskData,
      tableName: resultTable,
      data: result,
    });
  } catch (error) {
    console.error("Error streaming data to bucket:", error instanceof Error ? error.message : String(error));
  }
}

async function createMetadataTableConfusionMatrix(
  taskData: AutoMLTaskType,
  taskContext: TaskContextType,
  savedData: AutoMLOutput,
) {
  if (!savedData.output.cross_validation_metrics) return;

  const resultTable = `confusion_matrix_${taskData.resultTable}`;

  const columns: FieldType[] =
    savedData.output.cross_validation_metrics.cm.table.columns.map((col) => ({
      tableName: resultTable,
      columnName: col.name ? col.name : "reference",
      dataType: ["float", "double"].includes(col.type)
        ? "Nullable(Float)"
        : "Nullable(String)",
    }));

  await dropTableIfExist(
    {
      ...taskData,
      resultTable,
    },
    taskContext,
  );

  await createTableAtBucket(
    {
      ...taskData,
      resultTable,
    },
    await getColumnSchema({
      ...taskData,
      columns,
    }),
    taskContext,
  );

  const columnNames = columns.map((col) => col.columnName);

  const rowCount = savedData.output.cross_validation_metrics.cm.table.rowcount;
  const dataArrays = savedData.output.cross_validation_metrics.cm.table.data;
  const result = transformColumnsToRows(columnNames, dataArrays, rowCount);

  function transformConfusionMatrix(data: any[]) {
    // Identify dynamic category names (excluding 'Error' and 'Rate')
    const categoryKeys = Object.keys(data[0])
      .filter((key) => key !== "Error" && key !== "Rate")
      .map((key) => deburr(key));

    // Parse each row and convert necessary fields to numbers
    const parsed = data.map((row: any) => {
      let obj: any = {};
      categoryKeys.forEach((key) => {
        obj[key] = Number(row[key]);
      });
      obj.Error = Number(row.Error);
      if ("Rate" in row) obj.Rate = Number(row.Rate);
      return obj;
    });

    // Calculate precision for each predicted category
    function precision(tp: number, ...fps: number[]) {
      const denom = tp + fps.reduce((sum, v) => sum + v, 0);
      return denom === 0 ? 0 : tp / denom;
    }

    // Calculate recall for each real category (1 - Error)
    const recall = parsed.map((row) => +(1 - row.Error));

    // Build rows
    const result: any[] = [];

    // Predicted category rows
    categoryKeys.forEach((predKey, predIdx) => {
      let rowObj: any = { Category: `Pred_${predKey}` };
      categoryKeys.forEach((realKey, realIdx) => {
        rowObj[`Real_${realKey}`] = parsed[realIdx][predKey];
      });
      // Prepare precision values
      const tp = parsed[predIdx][predKey];
      const fps = categoryKeys
        .map((key, idx) => (idx !== predIdx ? parsed[idx][predKey] : 0))
        .filter((_, idx) => idx !== predIdx); // remove tp from fps
      rowObj.Precision = +precision(
        tp,
        ...categoryKeys.map((_, idx) =>
          idx !== predIdx ? parsed[idx][predKey] : 0,
        ),
      );
      result.push(rowObj);
    });

    // Recall row
    let recallRow: any = { Category: "Recall" };
    categoryKeys.forEach((realKey, idx) => {
      recallRow[`Real_${realKey}`] = recall[idx];
    });
    // For compatibility, put the recall for the "total" row if present
    recallRow.Precision = +(1 - parsed[parsed.length - 1].Error);
    result.push(recallRow);

    // Total row (usually last in the data)
    let totalRow: any = { Category: "Total" };
    categoryKeys.forEach((key) => {
      totalRow[`Real_${key}`] = parsed[parsed.length - 1][key];
    });
    totalRow.Precision = null;
    result.push(totalRow);

    return { result, categoryKeys };
  }

  const { result: resultMetrics, categoryKeys } =
    transformConfusionMatrix(result);

  const secondTableConfusionMatrix = [
    {
      columnName: "Category",
      dataType: "Nullable(String)",
    },
    ...categoryKeys.map((key) => ({
      columnName: `Real_${key}`,
      dataType: "Nullable(Float)",
    })),
    {
      columnName: "Precision",
      dataType: "Nullable(Float)",
    },
  ];
	
	const resultTableMatrix = `confusion_matrix_metrics_${taskData.resultTable}`

  await dropTableIfExist(
    {
      ...taskData,
      resultTable: resultTableMatrix,
    },
    taskContext,
  );

  await createTableAtBucket(
    {
      ...taskData,
      resultTable: resultTableMatrix,
    },
    await getColumnSchema({
      ...taskData,
      columns: secondTableConfusionMatrix,
    }),
    taskContext,
  );

  try {
    await streamToBucket({
      at: taskData,
      tableName: resultTableMatrix,
      data: resultMetrics,
    });
  } catch (error) {
    console.error("Error streaming data to bucket:", error instanceof Error ? error.message : String(error));
  }

  try {
    await streamToBucket({
      at: taskData,
      tableName: resultTable,
      data: result,
    });
  } catch (error) {
    console.error("Error streaming data to bucket:", error instanceof Error ? error.message : String(error));
  }
}

async function createMetadataTableVariableImportanceCoverage(
  taskData: AutoMLTaskType,
  taskContext: TaskContextType,
  savedData: AutoMLOutput,
) {
  if (!savedData.output.variable_importances_cover) return;

  const resultTable = `variable_importance_cover_${taskData.resultTable}`;

  const columns: FieldType[] =
    savedData.output.variable_importances_cover.columns.map((col) => ({
      tableName: resultTable,
      columnName: col.name ? col.name : "reference",
      dataType: ["float", "double"].includes(col.type)
        ? "Nullable(Float)"
        : "Nullable(String)",
    }));

  await dropTableIfExist(
    {
      ...taskData,
      resultTable,
    },
    taskContext,
  );

  await createTableAtBucket(
    {
      ...taskData,
      resultTable,
    },
    await getColumnSchema({
      ...taskData,
      columns,
    }),
    taskContext,
  );

  const columnNames = columns.map((col) => col.columnName);

  const rowCount = savedData.output.variable_importances_cover.rowcount;
  const dataArrays = savedData.output.variable_importances_cover.data;
  const result = transformColumnsToRows(columnNames, dataArrays, rowCount);

  try {
    await streamToBucket({
      at: taskData,
      tableName: resultTable,
      data: result,
    });
  } catch (error) {
    console.error("Error streaming data to bucket:", error instanceof Error ? error.message : String(error));
  }
}

async function createMetadataTableVariableImportanceFrequency(
  taskData: AutoMLTaskType,
  taskContext: TaskContextType,
  savedData: AutoMLOutput,
) {
  if (!savedData.output.variable_importances_frequency) return;

  const resultTable = `variable_importance_frequency_${taskData.resultTable}`;
  const columns: FieldType[] =
    savedData.output.variable_importances_frequency.columns.map((col) => ({
      tableName: resultTable,
      columnName: col.name ? col.name : "reference",
      dataType: ["float", "double"].includes(col.type)
        ? "Nullable(Float)"
        : "Nullable(String)",
    }));

  await dropTableIfExist(
    {
      ...taskData,
      resultTable,
    },
    taskContext,
  );

  await createTableAtBucket(
    {
      ...taskData,
      resultTable,
    },
    await getColumnSchema({
      ...taskData,
      columns,
    }),
    taskContext,
  );

  const columnNames = columns.map((col) => col.columnName);

  const rowCount = savedData.output.variable_importances_frequency.rowcount;
  const dataArrays = savedData.output.variable_importances_frequency.data;
  const result = transformColumnsToRows(columnNames, dataArrays, rowCount);

  try {
    await streamToBucket({
      at: taskData,
      tableName: resultTable,
      data: result,
    });
  } catch (error) {
    console.error("Error streaming data to bucket:", error instanceof Error ? error.message : String(error));
  }
}

async function createMetadataTableCrossValidation(
  taskData: AutoMLTaskType,
  taskContext: TaskContextType,
  savedData: AutoMLOutput,
) {
  if (!savedData.output.cross_validation_metrics_summary) return;

  const resultTable = `cross_validation_${taskData.resultTable}`;

  const columns: FieldType[] =
    savedData.output.cross_validation_metrics_summary.columns.map((col) => ({
      tableName: resultTable,
      columnName: col.name ? col.name : "reference",
      dataType: ["float", "double"].includes(col.type)
        ? "Nullable(Float)"
        : "Nullable(String)",
    }));

  await dropTableIfExist(
    {
      ...taskData,
      resultTable,
    },
    taskContext,
  );

  await createTableAtBucket(
    {
      ...taskData,
      resultTable,
    },
    await getColumnSchema({
      ...taskData,
      columns,
    }),
    taskContext,
  );

  const columnNames = columns.map((col) => col.columnName);

  const rowCount = savedData.output.cross_validation_metrics_summary.rowcount;
  const dataArrays = savedData.output.cross_validation_metrics_summary.data;
  const result = transformColumnsToRows(columnNames, dataArrays, rowCount);

  try {
    await streamToBucket({
      at: taskData,
      tableName: resultTable,
      data: result,
    });
  } catch (error) {
    console.error("Error streaming data to bucket:", error instanceof Error ? error.message : String(error));
  }
}

async function createMetadataTableModel(
  taskData: AutoMLTaskType,
  taskContext: TaskContextType,
  savedData: AutoMLOutput,
) {
  const resultTable = `models_${taskData.resultTable}`;
  const columns: FieldType[] = savedData.summary.columns.map((col) => ({
    tableName: resultTable,
    columnName: col.name ? col.name : "rank",
    dataType: ["float", "double"].includes(col.type)
      ? "Nullable(Float)"
      : "Nullable(String)",
  }));

  await dropTableIfExist(
    {
      ...taskData,
      resultTable,
    },
    taskContext,
  );

  await createTableAtBucket(
    {
      ...taskData,
      resultTable,
    },
    await getColumnSchema({
      ...taskData,
      columns,
    }),
    taskContext,
  );

  // Extract column names
  const columnNames = columns.map((col) => col.columnName);

  // Prepare rows as array of objects
  const rowCount = savedData.summary.rowcount;
  const dataArrays = savedData.summary.data;
  const result = transformColumnsToRows(columnNames, dataArrays, rowCount);

  try {
    await streamToBucket({
      at: taskData,
      tableName: resultTable,
      data: result,
    });
  } catch (error) {
    console.error("Error streaming data to bucket:", error instanceof Error ? error.message : String(error));
  }
}

// ## 1
export async function importFile(appId: string, taskDataId: string) {
  try {
    const filePath = `${resourcesFolder(appId)}/${taskDataId}.csv`;

    const result =
      await $`curl -X POST "http://localhost:54321/3/PostFile?destination_frame=${taskDataId}" -H "Content-Type: multipart/form-data" -F "file=@${filePath}"`;

    const response = await result.json();

    if (!response.destination_frame) {
      throw new Error("Failed to import file");
    }

    return [response.destination_frame];
  } catch (error) {
    throw new Error(`Failed to import file - ${error instanceof Error ? error.message : String(error)}`);
  }
}

// ## 2
export async function parseSetup(sourceFrames: string[]) {
  try {
    const response = await automlRest.post(
      aml("3/ParseSetup"),
      {
        source_frames: sourceFrames,
      },
      {
        "Content-Type": "application/x-www-form-urlencoded",
      },
    );

    return response;
  } catch (error) {
    throw new Error(`Failed to parse setup - ${error}`);
  }
}

export async function deleteModelFrame(modelName: string, taskDataId: string) {
  try {
    await automlRest.delete(aml(`3/Frames/${modelName}`));
  } catch {
    // Ignore deletion errors
  }

  try {
    await automlRest.delete(
      aml(`3/Frames/${modelName}Key_Frame__${taskDataId}.hex`),
    );
  } catch {
    // Ignore deletion errors
  }
}

export async function deleteFrames(id: string) {
  try {
    await automlRest.delete(aml(`3/Frames/${id}`));
  } catch {
    // Ignore deletion errors
  }

  try {
    await automlRest.delete(aml(`3/Frames/${id}.hex`));
  } catch {
    // Ignore deletion errors
  }

  try {
    await automlRest.delete(aml(`3/Frames/Key_Frame__${id}.hex`));
  } catch {
    // Ignore deletion errors
  }

  try {
    await automlRest.delete(aml(`3/Frames/combined-${id}`));
  } catch {
    // Ignore deletion errors
  }
}

// ## 3
export async function parseFrame(parseData: {
  check_header: number;
  chunk_size: number;
  column_names: string[];
  column_types: string[];
  destination_frame: string;
  number_columns: number;
  parse_type: string;
  separator: number;
  single_quotes: boolean;
  source_frames: H2OFrameKey[];
}) {
  try {
    const response = await automlRest.post(
      aml("3/Parse"),
      {
        check_header: parseData.check_header,
        chunk_size: parseData.chunk_size,
        column_names: parseData.column_names,
        column_types: parseData.column_types,
        delete_on_done: true,
        destination_frame: parseData.destination_frame,
        number_columns: parseData.number_columns,
        parse_type: parseData.parse_type,
        separator: parseData.separator,
        single_quotes: parseData.single_quotes,
        source_frames: parseData.source_frames.map((o) => o.name),
      },
      {
        "Content-Type": "application/x-www-form-urlencoded",
      },
    );

    return response;
  } catch (error) {
    throw new Error(`Failed to parse frame - ${error}`);
  }
}

// ## 4
async function automlBuilder(
  automlBuilder: {
    id: string;
    destinationFrame: string;
    projectName: string;
  } & AutoMLTaskTypeSettings,
) {
  try {
    const builderRequest = await automlRest.post(aml("99/AutoMLBuilder"), {
      build_control: {
        project_name: automlBuilder.projectName,
        nfolds: 5,
        distribution: "AUTO",
        balance_classes: false,
        stopping_criteria: {
          seed: -1,
          max_models: 0,
          max_runtime_secs: automlBuilder.maxRuntimeSecs,
          max_runtime_secs_per_model: 0,
          stopping_rounds: 3,
          stopping_metric: "AUTO",
          stopping_tolerance: -1,
        },
      },
      build_models: {
        exclude_algos: ["StackedEnsemble"],
        exploitation_ratio: -1,
        monotone_constraints: [],
      },
      input_spec: {
        ignored_columns: automlBuilder.removeColumns || [],
        response_column: automlBuilder.responseColumn,
        sort_metric: "AUTO",
        training_frame: `Key_Frame__${automlBuilder.destinationFrame}`,
      },
    });

    if (!builderRequest?.job) {
      throw new Error(
        `Unable to build a model in AutoML. Possible cause: Some columns contain only a single, constant value across all rows. Error message ${builderRequest.msg || builderRequest.message}`,
      );
    }

    return builderRequest;
  } catch (error) {
    throw new Error(`Failed to build automl - ${error instanceof Error ? error.message : String(error)}`);
  }
}

// PREPARATION
async function deletePreviousFrames(taskData: AutoMLTaskType, path: string) {
  try {
    const modelsBaseFolder = readdirSync(path);

    const jsonFile = modelsBaseFolder.find((file) => file.endsWith(".json"));

    if (jsonFile) {
      try {
        const modelsMetadataFile = readFileSync(
          `${path}/${jsonFile}`,
        ).toString();
        const modelsMetadata = JSON.parse(modelsMetadataFile);

        if (modelsMetadata.data?.[1]) {
          for (const model of modelsMetadata.data[1]) {
            if (model) {
              await automlRest.delete(aml(`3/Models/${model}`));
              await automlRest.delete(aml(`3/Models/${model}.hex`));

              const s = model;
              const index = s.indexOf("AutoML_");
              const frameName = index !== -1 ? `${s.substring(index)}` : "";

              if (frameName) {
                const leaderboardFrameName = `${frameName}_validation_${taskData.id}.hex`;
                const blendingFrameName = `${frameName}_blending_${taskData.id}.hex`;
                const trainingFrameName = `${frameName}_training_${taskData.id}.hex`;

                await automlRest.delete(
                  aml(`3/Frames/${leaderboardFrameName}`),
                );
                await automlRest.delete(aml(`3/Frames/${blendingFrameName}`));
                await automlRest.delete(aml(`3/Frames/${trainingFrameName}`));
                await automlRest.delete(aml(`3/Frames/${taskData.id}.hex`));
              }
            }
          }
        }
      } catch (error) {
        // Ignore metadata processing errors
      }
    }
  } catch (error) {
    // Ignore folder reading errors
  }

  await deleteFrames(taskData.id);

  try {
    await automlRest.delete(aml(`3/Models/${taskData.id}.hex`));
  } catch {
    // Ignore deletion errors
  }

  try {
    await automlRest.delete(aml(`3/Models/${taskData.id}.hex`));
  } catch {
    // Ignore deletion errors
  }

  try {
    await automlRest.delete(
      aml(`3/ModelMetrics/frames/${taskData.id}.hex/models/${taskData.id}.hex`),
    );
  } catch {
    // Ignore deletion errors
  }
}

// PREPARATION - EXPORT HELPER
export async function createLocalData(
  taskData: AutoMLTaskType | ScoringTaskType,
  useLimitRows = true,
) {
  try {
    const resourcesDir = resourcesFolder(taskData.appId);
    const trainingFile = `${resourcesDir}/${taskData.id}.csv`;

    removeLocal([trainingFile]);

    ensureDirSync(resourcesDir);

    let query = `
			SELECT *
			FROM ${taskData.databaseName}.${taskData.tableName}
			ORDER BY rand(50)
		`;

    if (useLimitRows) {
      query = `${query} LIMIT ${taskData.limitRows || 100000}`;
    }

    const repo = await RepoRepository.getRepoCredentials(taskData);

    await shell("automl export csv file", "clickhouse-client", [
      logComment(taskData),
      `--host=${repo.host}`,
      `--user=${repo.user}`,
      `--password=${repo.password}`,
      `--query="${query} FORMAT CSVWithNames"`,
      `> ${trainingFile}`,
    ]);

    return { message: "Success to export" };
  } catch (err) {
    throw new Error(`Failed to create training file - ${err instanceof Error ? err.message : String(err)}`);
  }
}

export async function followProgress(
  job: { status: string; key: H2OFrameKey },
  taskData: AutoMLTaskType,
  type: string,
  taskContext: TaskContextType,
) {
  let shouldFetchProgress = true;

  while (shouldFetchProgress) {
    try {
      const progress = await fetchH2OJobProgress(job.key.name);

      if (!progress) {
        shouldFetchProgress = false;
        throw new Error(`Failed to fetch H2O Job Progress - ${progress}`);
      }

      if (progress instanceof Error) {
        shouldFetchProgress = false;
        throw new Error(progress.message);
      }

      if (progress.status === "RUNNING") {
        await sendMessageSocket(
          {
            id: taskData.id,
            job: job.key.name,
            progress: progress.progress,
            status: progress.status,
            type,
          },
          taskContext,
        );

        await new Promise((resolve) => setTimeout(resolve, 100));

        continue;
      }

      await sendMessageSocket(
        {
          id: taskData.id,
          job: job.key.name,
          progress: progress.progress,
          status: progress.status,
          type,
        },
        taskContext,
      );

      shouldFetchProgress = false;

      return;
    } catch (err) {
      shouldFetchProgress = false;
      throw new Error(err instanceof Error ? err.message : String(err));
    }
  }
}

export async function fetchH2OJobProgress(
  jobName: string,
): Promise<{ progress: number; status: string }> {
  try {
    const progressRequest = await automlRest.get(
      aml(`3/Jobs/${encodeURIComponent(jobName)}`),
    );

    if (progressRequest.jobs?.[0]) {
      return {
        progress: Math.trunc(progressRequest.jobs[0].progress * 100),
        status: progressRequest.jobs[0].status,
      };
    }

    throw new Error(JSON.stringify(progressRequest, null, 2));
  } catch (err) {
    throw new Error(`Failed to fetch H2O Job Progress - ${err instanceof Error ? err.message : String(err)}`);
  }
}

async function sendMessageSocket(
  progress: unknown,
  taskContext: TaskContextType,
) {
  const channel = `type:automl-${taskContext.appId}`;
  const message = JSON.stringify(progress);

  await gaioRedis.pub.publish(channel, message);
}
