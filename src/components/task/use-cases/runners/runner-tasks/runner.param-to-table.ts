import type { ParamType, TaskContextType } from '@gaio/shared/types';
import type { ParamToTableTaskType } from '@gaio/shared/types';
import { dropTableIfExist, executeQuery } from '../runner.tools';

export default async function (
	taskData: ParamToTableTaskType,
	taskContext: TaskContextType,
	abortController: AbortController
) {
	const { params } = taskContext;
	await dropTableIfExist(taskData, taskContext);

	const query = paramToTableQuery(taskData, params);

	return await executeQuery({
		abortSignal: abortController.signal,
		at: taskData,
		sql: `
			CREATE TABLE
				${taskData.resultDatabase}.${taskData.resultTable}
				engine = MergeTree order by tuple
			(
			) as
				${query}`,
		taskContext
	});
}

function customParam(paramValue: string) {
	if (paramValue && `${paramValue}`.trim().startsWith('{{')) {
		return `(${paramValue.replace(/\{\{|}}/gi, '')})`;
	}
	if (isNumber(paramValue)) {
		return `'${paramValue}'`;
	}
	if (paramValue === 'undefined' || paramValue === 'null' || !paramValue) {
		return `''`;
	}
	return `'${paramValue || ''}'`;
}

function paramToTableQuery(
	taskData: ParamToTableTaskType,
	params: ParamType[]
) {
	let sql = 'select ';
	params.forEach((par) => {
		if (taskData.params.includes(par.paramName)) {
			// need future change
			if (typeof par.paramValue === 'string') {
				par.paramValue = par.paramValue.replace(/'/gi, "\\'");
			}
			sql += `${customParam(par.paramValue)} as ${par.paramName},`;
		}
	});
	sql += ';';
	sql = sql.replace(',;', '');
	return sql;
}

const isNumber = (n: string) => {
	return /^-?[\d.]+(?:e-?\d+)?$/.test(n);
};
