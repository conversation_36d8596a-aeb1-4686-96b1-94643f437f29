import { QueryBuilder } from '@/components/builder/use-cases/builder';
import CodeSnippetRepository from '@/components/code-snippet/code-snippet.repository';
import RepoRepository from '@/components/repo/repo.repository';
import SourceRepository from '@/components/source/source.repository';
import templateString from '@/utils/template-string';
import type {
	BuilderTaskType,
	ParamType,
	SourceRawTaskType,
	TaskContextType
} from '@gaio/shared/types';
import { getBucketNameFromAppId } from '@gaio/shared/utils';
import { shell } from '../runner.shell';
import {
	type TaskWithSchema,
	createTableAtBucket,
	dropTableIfExist,
	getColumnSchema,
	getFilenameBySessionId,
	logComment,
	makeSureFolderExist,
	removeLocal,
	resourcesFolder,
	tableCanBeTemporary
} from '../runner.tools';
import { usqlQueryCommand } from '../runner.usql';

type SourceOrBucketBuilder = SourceRawTaskType | BuilderTaskType;

export default async (
	taskData: SourceOrBucketBuilder,
	taskContext: TaskContextType,
	abortController: AbortController
) => {
	try {
		const { params } = taskContext;
		await clearFiles(taskData, taskContext);
		const { logFrom, userId, sessionid } = taskContext;
		if (tableCanBeTemporary(logFrom)) {
			taskData.resultTable = `${taskData.resultTable}`.replace(
				/tmp_/g,
				`tmp_gaio${sessionid || userId}_`
			);
		}
		if (
			taskData.type === 'sourceRaw' &&
			taskData.bringData &&
			!taskData.insertMode
		) {
			await dropTableIfExist(taskData, taskContext);
		} else if (taskData.type === 'builder' && !taskData.insertMode) {
			await dropTableIfExist(taskData, taskContext);
		}

		return await sourceExecutionHub(taskData, params, taskContext);
	} catch (e) {
		await clearFiles(taskData, taskContext);

		try {
			if (!taskData.schemaInference && taskData.onErrorCreateTable) {
				await createTableAtBucket(
					taskData,
					await getColumnSchema(taskData as TaskWithSchema),
					taskContext
				);
			}
		} catch {
			// TODO: ?
		}

		if (
			e?.message &&
			typeof e.message === 'string' &&
			e.message.includes('EOF')
		) {
			e.message = 'Error: EOF. Possible empty result';
		}
		throw e;
	}
};

const sourceExecutionHub = async (
	taskData: SourceOrBucketBuilder,
	parameters: ParamType[],
	taskContext?: TaskContextType
) => {
	const sourceCredentials = await SourceRepository.getSourceById(
		taskData.sourceId
	);

	if (taskData.type === 'sourceRaw' && !sourceCredentials.canExecuteRaw) {
		throw { message: 'This source is not enabled to execute raw query' };
	}

	const folder = resourcesFolder(taskData.appId);

	makeSureFolderExist(folder);

	const output = `${folder}/${getFilenameBySessionId(taskData.id, taskContext?.sessionid, 'tsv')}`;
	const csvInput = `${folder}/${getFilenameBySessionId(taskData.id, taskContext?.sessionid, 'csv')}`;
	const txtInput = `${folder}/${getFilenameBySessionId(taskData.id, taskContext?.sessionid, 'txt')}`;

	let sql: string;
	if (taskData.type === 'builder') {
		sql = await new QueryBuilder().generate(taskData, parameters);
	} else if (taskData.client === 'salesforce') {
		sql = templateString(taskData.query, parameters);
		try {
			return bringSalesForce(taskData, sql, output, csvInput, txtInput);
		} catch (e) {
			throw { message: e.message };
		}
	} else if (taskData.type === 'sourceRaw') {
		const codeSnippet = await CodeSnippetRepository.getCodeSnippetByIdDecoded(
			taskData.codeSnippetId
		);
		sql = templateString(codeSnippet.codeSnippet, parameters);
	}

	const query = await usqlQueryCommand(taskData.client, {
		csvInput,
		output,
		sql,
		...sourceCredentials
	});
	await bringData(taskData, taskContext, query);
	return { status: 'done' };
};

const bringSalesForce = async (
	taskData: SourceOrBucketBuilder,
	queryToExecuteOnSource: unknown,
	output: unknown,
	csvInput: unknown,
	txtInput: unknown
) => {
	// todo
};

const bringData = async (
	taskData: SourceOrBucketBuilder,
	taskContext: TaskContextType,
	queryToExecuteOnSource: string
) => {
	if (taskData.type === 'sourceRaw' && !taskData.bringData) {
		await shell(
			'Query execution on source raw, dont bring data, just execute query',
			queryToExecuteOnSource,
			[]
		);
	} else {
		return await treatData(taskData, taskContext, queryToExecuteOnSource);
	}
};

const treatData = async (
	taskData: SourceOrBucketBuilder,
	taskContext: TaskContextType,
	queryToExecuteOnSource = ''
) => {
	const repoData = await RepoRepository.getRepoCredentials(taskData);
	await createTableAtBucket(
		taskData,
		await getColumnSchema(taskData as TaskWithSchema),
		taskContext
	);

	const fields = taskData.schema.select.map((col, index) => {
		const fieldName = `col${index + 1}`; // + ' ' +  col.alias || col.columnName;

		if (col.dataType.includes('DateTime')) {
			return `(substring(if(empty(${fieldName}), null, toString(${fieldName})), 1, 19)) as ${fieldName}`;
		}

		if (col.dataType.includes('Date')) {
			return `((substring(if(empty(${fieldName}), null, toString(${fieldName})), 1, 10)))  as ${fieldName}`;
		}

		return fieldName; // col.alias || col.columnName;
	});

	const cols = taskData.schema.select.map((col, index) => {
		if (col.dataType.includes('Date')) {
			return `col${index + 1} Nullable(String)`;
		}
		return `col${index + 1} ${col.dataType}`; //.replace('Nullable(', '').replace(')', '');
	});

	const bucketName = getBucketNameFromAppId(taskData.appId);
	const query = `insert into ${bucketName}.${taskData.resultTable}
								 select ${fields.join(',')}
								 from input('${cols.join(', ')}') FORMAT CSVWithNames`;

	// minify, make one line query
	const onLineQuery = query.replace(/\s+/g, ' ').trim();

	const localArgs = [
		'|',
		'clickhouse-client',
		`--host=${repoData.host}`,
		`--user=${repoData.user}`,
		`--password=${repoData.password} `,
		`--database=${getBucketNameFromAppId(taskData.appId)}`,
		logComment(taskData),
		'--input_format_with_names_use_header=0',
		'--async_insert=1',
		'--wait_for_async_insert=1',
		'--input_format_try_infer_dates=1',
		'--input_format_try_infer_datetimes=1',
		'--format_csv_allow_single_quotes=1',
		'--date_time_input_format="best_effort"',
		'--input_format_defaults_for_omitted_fields=1',
		'--input_format_null_as_default=1',
		'--input_format_csv_empty_as_default=1',
		'--schema_inference_make_columns_nullable=1',
		'--input_format_tsv_empty_as_default=1',
		`--query="${onLineQuery}"`
	];

	await shell('local treat data', queryToExecuteOnSource, localArgs);
	await clearFiles(taskData, taskContext);
	return { status: 'done' };
};

async function clearFiles(
	taskData: SourceOrBucketBuilder,
	taskContext?: TaskContextType
) {
	const folder = resourcesFolder(taskData.appId);

	return removeLocal([
		`${folder}/${getFilenameBySessionId(taskData.id, taskContext?.sessionid, 'tsv')}`,
		`${folder}/${getFilenameBySessionId(taskData.id, taskContext?.sessionid, 'csv')}`,
		`${folder}/${getFilenameBySessionId(taskData.id, taskContext?.sessionid, 'txt')}`,
		`${folder}/tmp_${getFilenameBySessionId(taskData.id, taskContext?.sessionid, 'tsv')}`
	]);
}
