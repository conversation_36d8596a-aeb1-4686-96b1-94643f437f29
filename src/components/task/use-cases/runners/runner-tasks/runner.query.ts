import type { CodeSnippetEntity } from '@/components/code-snippet/code-snippet.entity';
import { dbGaio } from '@/db/db.gaio';
import templateString from '@/utils/template-string';
import type { QueryTaskType, TaskContextType } from '@gaio/shared/types';
import { executeQuery, logCommentText } from '../runner.tools';

export default async function (
	taskData: QueryTaskType,
	taskContext: TaskContextType,
	abortController: AbortController
) {
	let queryString = '';

	const { params } = taskContext;

	if (taskData.query) {
		queryString = taskData.query;
	} else {
		try {
			const snippet = await dbGaio(
				'getSnippetToTaskQuery'
			).query<CodeSnippetEntity>(`
				SELECT codeSnippet
				FROM code_snippet
				WHERE codeSnippetId = '${taskData.codeSnippetId}' LIMIT 1
			`);

			if (snippet.length > 0) {
				queryString = snippet[0].codeSnippet;
			}
		} catch (err) {
			throw {
				code: 400,
				error: true,
				message: err.message || 'Query not found'
			};
		}
	}

	if (!queryString)
		throw { code: 400, error: true, message: 'Query not found' };

	const finalSpace = templateString(queryString, params);
	const finalQuery = finalSpace
		.replace(/';'/g, '_____')
		.replace(/&lt;/g, '<')
		.replace(/&gt;/g, '>')
		.replace(/&#39;/g, "'")
		.replace(/&quot;/g, '"');

	const queryList = `${finalQuery}`.split(';').filter((o) => `${o}`.trim());

	const results = [];

	for await (const [, sql] of queryList.entries()) {
		if (abortController.signal.aborted) {
			break;
		}

		const res = await runQuery(
			taskData,
			`${minify(sql.replace('_____', "';'"), taskData.limit)}`.replace(
				/^--.*/g,
				''
			),
			taskContext,
			abortController
		);

		results.push(res);
	}

	return results;
}

async function runQuery(
	taskData: QueryTaskType,
	sqlQuery: string,
	taskContext: TaskContextType,
	abortController: AbortController
) {
	try {
		return await executeQuery({
			abortSignal: abortController.signal,
			at: taskData,
			logComment: logCommentText(taskData, taskContext),
			sql: sqlQuery,
			taskContext
		});
	} catch (error) {
		if (taskData.prepare) {
			return await error;
		}
		if (error?.data?.[0]) {
			return {
				data: [],
				meta: [],
				code: error.data[0].code,
				success: false,
				message: error.data[0].message
			};
		}
		if (error?.data?.message) {
			return {
				data: [],
				meta: [],
				code: error.data.code,
				success: false,
				message: error.data.message
			};
		}
		return {
			data: [],
			meta: [],
			code: error.code,
			success: false,
			message: error.message,
			query: error.query
		};
	}
}

function minify(text: string, limit: number) {
	const sql = `${text}`
		.replace(/--.*\n/g, ' ')
		.replace(/\/\*[\s\S]*?\*\//g, ' ')
		.replace(' =', '=')
		.replace('= ', '=')
		.replace('( ', '(')
		.replace(' )', ')')
		.replace('\r', ' ')
		.replace('\n', ' ')
		.replace(/ +/g, ' ')
		.replace(/^ /g, ' ')
		.replace(/\r\n|\r|\n/g, ' ');

	const sqlLower = `${sql}`.toLowerCase().trim();

	const startsWithUpdate = `${sqlLower}`
		.trim()
		.toLowerCase()
		.startsWith('update ');
	const startsWithAlter = `${sqlLower}`
		.trim()
		.toLowerCase()
		.startsWith('alter ');
	const startsWithCreate = `${sqlLower}`
		.trim()
		.toLowerCase()
		.startsWith('create ');
	const startsWithInsert = `${sqlLower}`
		.trim()
		.toLowerCase()
		.startsWith('insert ');

	const doesMachSelect = /select(.*?)from/i.exec(sqlLower);
	const doesMachAttachSelect = /atach(.*?)select(.*?)from/i.exec(sqlLower);
	const doesMachCreateSelect =
		/create(.*?)select(.*?)from/i.exec(sqlLower) ||
		/create(.*?)table/i.exec(sqlLower);
	const doesMachInsertSelect =
		/insert(.*?)select(.*?)from/i.exec(sqlLower) ||
		/insert(.*?)into/i.exec(sqlLower);

	if (
		(doesMachCreateSelect && doesMachCreateSelect.length > 0) ||
		(doesMachAttachSelect && doesMachAttachSelect.length > 0)
	) {
		return sql;
	}
	if (doesMachInsertSelect && doesMachInsertSelect.length > 0) {
		return sql;
	}
	if (
		!startsWithUpdate &&
		!startsWithAlter &&
		!startsWithCreate &&
		!startsWithInsert &&
		doesMachSelect &&
		doesMachSelect.length > 0
	) {
		if (!sql.toLowerCase().includes(' limit ')) {
			if (limit <= 0) {
				return sql;
			}
			return ` ${sql} limit ${limit || 1000} `;
		}
		return sql;
	}
	return sql;
}
