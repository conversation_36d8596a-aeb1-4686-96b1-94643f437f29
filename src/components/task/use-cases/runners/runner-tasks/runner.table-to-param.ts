import { dbGaio } from '@/db/db.gaio';
import type {
	GenericType,
	ParamType,
	TaskContextType
} from '@gaio/shared/types';
import type { TableToParamTaskType } from '@gaio/shared/types';
import { unionBy } from 'lodash-es';
import { executeQuery } from '../runner.tools';

export default async function (
	taskData: TableToParamTaskType,
	taskContext: TaskContextType,
	abortController: AbortController
) {
	const { params } = taskContext;
	const { data: rowValues } = await executeQuery<Record<string, string>[]>({
		abortSignal: abortController.signal,
		at: taskData,
		sql: `
			SELECT * FROM 
				${taskData.databaseName}.${taskData.tableName}
			LIMIT 1`,
		taskContext
	});

	const paramsFromTable = await tableToParam(taskData, params, rowValues[0]);

	return { params: paramsFromTable, status: 'done' };
}

async function tableToParam(
	taskData: TableToParamTaskType,
	params: ParamType[],
	rowValues: Record<string, string>[]
) {
	const saveOnlyThisParams = [];

	for (const key in rowValues) {
		if (taskData.byReference) {
			if (Object.hasOwn(rowValues, key)) {
				const findColumn = (taskData.fieldToParamList || []).find(
					(fieldToParam) => fieldToParam.columnName === key
				);

				if (findColumn?.paramName) {
					params.forEach((param) => {
						if (findColumn.paramName === param.paramName) {
							param.paramValue = String(rowValues[key]);
							saveOnlyThisParams.push(param);
						}
					});
				}
			}
		} else {
			if (Object.hasOwn(rowValues, key)) {
				const newVal = String(rowValues[key]);

				for (const param of params) {
					if (param.paramName === key) {
						param.paramValue = newVal;
						saveOnlyThisParams.push(param);
					}
				}
			}
		}
	}

	if (taskData.saveAsDefault) {
		await updateDefaultParam(taskData, saveOnlyThisParams);
	}

	return params;
}

async function updateDefaultParam(
	taskData: TableToParamTaskType,
	params: ParamType[]
) {
	if (
		Array.isArray(params) &&
		params.filter((o) => o.paramName !== 'userId').length > 0
	) {
		const listAppAndParams = await dbGaio()
			.query(
				`
					SELECT params FROM app
					WHERE appId = {appId: String}
				`,
				{
					params: { appId: taskData.appId },
					parse: ['params']
				}
			)
			.then((res) => {
				const results = res[0];

				if (results?.params && Array.isArray(results.params)) {
					return results.params.filter(
						(param: ParamType) => param.paramName !== 'userId'
					);
				}
				return [];
			})
			.catch(() => [] as GenericType[]);

		const newParams = unionBy(params, listAppAndParams, 'paramName').filter(
			(param: ParamType) => param.paramName !== 'userId'
		);

		return await dbGaio().exec(
			`
				ALTER TABLE app
				UPDATE params = {params: String} 
				WHERE appId = {appId: String}
			`,
			{
				params: { appId: taskData.appId, params: newParams },
				stringify: ['params']
			}
		);
	}

	return { status: 'done' };
}
