import { join } from 'node:path';
import RepoRepository from '@/components/repo/repo.repository';
import type { FileImportTaskType, TaskContextType } from '@gaio/shared/types';
import { cloneDeep } from 'lodash-es';
import { shell } from '../runner.shell';

import { getAppNumberReference } from '@gaio/shared/utils/libs/helpers';
import {
	// changeEncoding,
	contentFolder,
	createTableAtBucket,
	dropTableIfExist,
	getColumnSchema,
	logComment,
	removeLocal,
	tableCanBeTemporary
} from '../runner.tools';
import { transform } from '../runner.transform';

const symbols = {
	comma: ',',
	crNewLine: '\r',
	crlfNewLine: '\r\n',
	// eslint-disable-next-line no-useless-escape
	doubleQuotes: `\"`,
	lfNewLine: '\n',
	newLine: '\n',
	none: '',
	pipe: '|',
	semicolon: ';',
	// eslint-disable-next-line no-useless-escape
	singleQuotes: `\'`,
	tab: '\t'
};

type Symbols = keyof typeof symbols;

export default async function (
	taskData: FileImportTaskType,
	taskContext: TaskContextType,
	abortController: AbortController
) {
	try {
		const { logFrom, userId, sessionid } = taskContext;

		if (tableCanBeTemporary(logFrom)) {
			taskData.resultTable = `${taskData.resultTable}`.replace(
				/tmp_/g,
				`tmp_gaio${sessionid || userId}_`
			);
		}

		if (!taskData.append) {
			await dropTableIfExist(taskData, taskContext);
		}

		await createTableAtBucket(
			taskData,
			await getColumnSchema(taskData),
			taskContext
		);

		return await fullFileImportProcess(taskData, taskContext, abortController);
	} catch (e) {
		await removeAll(fileTransformLocation(taskData));
		throw e;
	}
}

async function fullFileImportProcess(
	taskData: FileImportTaskType,
	taskContext: TaskContextType,
	abortController: AbortController
) {
	const inputTerminatedBy = symbols[taskData.fieldsTerminatedBy as Symbols];

	const file = fileLocation(taskData);
	const repoData = await RepoRepository.getRepoCredentials(taskData);
	const columnReference = await prepareColumnsToClickhouseLocal(taskData);

	await shell(
		'Transform file - scrub the file',
		`GOCSV_DELIMITER="${inputTerminatedBy}" gocsv`,
		[
			// 1. GOCSV
			`clean ${file} | `,
			// FIX ENCODING
			taskData.fixEncoding
				? `iconv -f ${taskData.fixEncoding} -t UTF-8 | `
				: '',

			// REMOVE SINGLE QUOTES
			` sed "s/'/′/g" |`,

			// REMOVE TABS
			// `sed "s/\\t/ /g" |`,

			// REPLACE "\N" WITH empty
			// `sed "s/\\N//g" |`,

			// 2. CLICKHOUSE-LOCAL
			'clickhouse-local',
			taskData.fieldsTerminatedBy === 'tab'
				? '--input-format=TabSeparatedWithNames'
				: '--input-format=CSVWithNames',
			taskData.fieldsTerminatedBy === 'tab'
				? ''
				: `--format_csv_delimiter="${defineFieldTerminatedBy(taskData)}"`,
			'--input_format_with_names_use_header=0',
			'--format_csv_allow_single_quotes=1',
			'--date_time_input_format="best_effort"',
			'--input_format_null_as_default=1',
			'--input_format_defaults_for_omitted_fields=1',
			'--input_format_tsv_empty_as_default=1',
			`-S "${columnReference.previousFields}"`,
			'--output-format=TabSeparatedWithNames',
			`--query="select ${columnReference.nextFields} from table" |`,
			// 3. CLICKHOUSE-CLIENT
			'clickhouse-client',
			`--host=${repoData.host}`,
			`--user=${repoData.user}`,
			`--password=${repoData.password} `,
			logComment(taskData),
			// `--format_csv_delimiter="${defineFieldTerminatedBy(taskData)}"`,
			'--date_time_input_format="best_effort"',
			'--input_format_null_as_default=1',
			'--format_csv_allow_single_quotes=1',
			'--input_format_defaults_for_omitted_fields=1',
			'--input_format_tsv_empty_as_default=1',
			`--query="INSERT INTO ${taskData.databaseName}.${taskData.resultTable} FORMAT TabSeparatedWithNames"`
		],
		abortController.signal
	);
}

function defineFieldTerminatedBy(taskData: FileImportTaskType) {
	if (taskData.fieldsTerminatedBy === 'custom') {
		return taskData.fieldsTerminatedByCustom;
	}
	return symbols[taskData.fieldsTerminatedBy as Symbols];
}

async function prepareColumnsToClickhouseLocal(taskData: FileImportTaskType) {
	taskData.columns.forEach((col) => {
		if (col.dataType.includes('DateTime')) {
			col.transform = 'bestEffortOrNull';
			col.toDataType = 'DateTime';
		} else if (col.dataType.includes('Date')) {
			col.transform = 'bestEffortOrNull';
			col.toDataType = 'Date';
		}

		if (col.transform !== 'nome') {
			col.toDataType = cloneDeep(col.dataType);
			col.dataType = 'Nullable(String)';
			col.extraOne = col.commaDecimalSeparator ? 'comma' : 'dot';
			col.extraTwo = col.columnLength;
		}
	});

	const columns = await transform(taskData);

	return {
		nextFields: columns
			.map((col) => {
				return `${col.mutation} as ${col.columnName}`;
			})
			.join(','),
		previousFields: columns.map((o) => `${o.columnName} String`).join(',')
	};
}

function fileLocation(taskData: FileImportTaskType) {
	return join(
		contentFolder,
		'apps',
		getAppNumberReference(taskData.appId),
		'assets',
		taskData.fullPath
	);
}

function fileTransformLocation(taskData: FileImportTaskType) {
	return join(
		contentFolder,
		'apps',
		getAppNumberReference(taskData.appId),
		'assets',
		taskData.fullPath.replace(taskData.fileName, `local_${taskData.fileName}`)
	);
}

async function removeAll(outputFile: string) {
	await removeLocal([
		outputFile.replace('.csv', ''),
		outputFile,
		`${outputFile}t`
	]);
}
