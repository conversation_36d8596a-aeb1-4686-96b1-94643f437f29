import { QueryBuilder } from '@/components/builder/use-cases/builder';
import type { TaskContextType } from '@gaio/shared/types';
import type { InsertRowTaskType } from '@gaio/shared/types';
import { executeQuery } from '../runner.tools';

export default async function (
	taskData: InsertRowTaskType,
	taskContext: TaskContextType,
	abortController: AbortController
) {
	const { params } = taskContext;

	await executeQuery({
		abortSignal: abortController.signal,
		at: taskData,
		sql: await new QueryBuilder().generate(taskData, params),
		taskContext
	});

	return { status: 'done' };
}
