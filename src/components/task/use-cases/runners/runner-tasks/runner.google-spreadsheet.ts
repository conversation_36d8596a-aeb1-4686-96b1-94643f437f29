import type {
	GoogleSpreadsheetTaskType,
	TaskContextType
} from '@gaio/shared/types';
import { getBucketNameFromAppId } from '@gaio/shared/utils';
import {
	dropTableIfExist,
	executeQuery,
	logCommentText
} from '../runner.tools';
import { deburr } from 'lodash-es';

export default async function (
	taskData: GoogleSpreadsheetTaskType,
	taskContext: TaskContextType,
	abortController: AbortController
) {
	let spreadsheetUrl = `https://docs.google.com/spreadsheets/d/e/${taskData.url}/pub?output=csv`;

	if (taskData.url?.includes('https://docs.google.com/')) {
		spreadsheetUrl = taskData.url.replace('output=csv', 'output=csv');
	}

	if (!taskData.insertMode) {
		await dropTableIfExist(taskData, taskContext);
	}

	await createOrInsert(spreadsheetUrl, taskData, taskContext, abortController);

	return { status: 'success' };
}

async function createOrInsert(
	url: string,
	taskData: GoogleSpreadsheetTaskType,
	taskContext: TaskContextType,
	abortController: AbortController
) {
	const describeTable = await executeQuery({
		at: taskData,
		sql: `select name from system.tables where name = '${taskData.resultTable}' and database = '${getBucketNameFromAppId(taskData.appId)}' limit 1`,
		taskContext,
		logComment: logCommentText(taskData, taskContext),
		abortSignal: abortController.signal
	});

	if (!describeTable?.data?.length) {
		await executeQuery({
			at: taskData,
			sql: `
				CREATE TABLE ${getBucketNameFromAppId(taskData.appId)}.${taskData.resultTable} 
				ENGINE MergeTree ORDER BY tuple() AS
					SELECT * FROM url('${url}','CSVWithNames')
				settings max_http_get_redirects = 100`,
			taskContext,
			logComment: logCommentText(taskData, taskContext),
			abortSignal: abortController.signal
		});

		try {
			const tableDescribe = await executeQuery({
				abortSignal: abortController.signal,
				at: taskData,
				logComment: logCommentText(taskData, taskContext),
				sql: `SELECT name FROM system.columns WHERE database = '${taskData.resultDatabase}' AND table = '${taskData.resultTable}'`,
				taskContext
			}).then((res) => {
				return res;
			});

			const fixName = (name: string) => {
				return deburr(name as string)
					.replace(/ /g, '_')
					.trim()
					.replace(/[^\w\s]/gi, '');
			};

			const columns = (tableDescribe.data as { name: string }[])
				.map((column) => ({
					name: column.name,
					fixedName: fixName(column.name)
				}))
				.filter((column) => column.name !== column.fixedName);

			columns.forEach((column) => {
				executeQuery({
					at: taskData,
					logComment: logCommentText(taskData, taskContext),
					abortSignal: abortController.signal,
					sql: `ALTER TABLE ${taskData.resultDatabase}.${taskData.resultTable} RENAME COLUMN \`${column.name}\` TO \`${column.fixedName}\``,
					taskContext
				});
			});
		} catch (error) {}
	} else {
		await executeQuery({
			at: taskData,
			sql: `
				INSERT INTO ${getBucketNameFromAppId(taskData.appId)}.${taskData.resultTable} 
					SELECT *
					FROM url('${url}','CSVWithNames')
				SETTINGS max_http_get_redirects = 100`,
			taskContext,
			logComment: logCommentText(taskData, taskContext),
			abortSignal: abortController.signal
		});
	}
}
