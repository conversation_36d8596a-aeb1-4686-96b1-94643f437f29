import { exec, spawn } from 'node:child_process';
import RepoRepository from '@/components/repo/repo.repository';
import type { ExportToFileType, TaskContextType } from '@gaio/shared/types';
import { ensureDirSync, remove } from 'fs-extra';
import {
	getFilenameBySessionId,
	logComment,
	outputsFolder
} from '../runner.tools';

export default async function (
	taskData: ExportToFileType,
	_: TaskContextType,
	abortController: AbortController
) {
	// const { sessionid } = taskContext;

	const folder = outputsFolder(taskData.appId);

	ensureDirSync(folder);

	const outputFilenameAsCsv = getFilenameBySessionId(
		taskData.tableName,
		'', // no session id
		'csv'
	);
	const outputFilenameAsZip = getFilenameBySessionId(
		taskData.tableName,
		'', // no session id
		'zip'
	);

	const outputFilePath = `${folder}/${outputFilenameAsCsv}`;
	const outputFileZipPath = `${folder}/${outputFilenameAsZip}`;

	await remove(outputFilePath);
	await remove(outputFileZipPath);

	const repoData = await RepoRepository.getRepoCredentials(taskData);

	let baseQuery = '';

	if (taskData.separator) {
		switch (taskData.separator) {
			case '|':
				baseQuery = ` --format=CSVWithNames --format_csv_delimiter="|" --query="SELECT * FROM ${taskData.databaseName}.${taskData.tableName}"`;
				break;
			case ';':
				baseQuery = ` --format=CSVWithNames --format_csv_delimiter=";" --query="SELECT * FROM ${taskData.databaseName}.${taskData.tableName}"`;
				break;
			case '~':
				baseQuery = ` --format=CSVWithNames --format_csv_delimiter="~" --query="SELECT * FROM ${taskData.databaseName}.${taskData.tableName}"`;
				break;
			case 'TabSeparatedWithNames':
				baseQuery = ` --query="SELECT * FROM ${taskData.databaseName}.${taskData.tableName} FORMAT TabSeparatedWithNames"`;
				break;
			default:
				baseQuery = ` --query="SELECT * FROM ${taskData.databaseName}.${taskData.tableName} FORMAT CSVWithNames"`;
				break;
		}
	}

	const query = [
		'clickhouse-client',
		logComment(taskData),
		`--host=${repoData.host}`,
		`--user=${repoData.user}`,
		`--password=${repoData.password}`,
		baseQuery,
		` | sed 's/\\\\N/\"\"/g'  > ${outputFilePath}`
	];

	const shell = spawn(query.join(' '), [], {
		detached: false,
		shell: true,
		signal: abortController.signal
	});

	let error = '';

	shell.stderr.on('data', (data) => {
		const err = Buffer.from(data);
		error += err.toString();
	});

	shell.stdout.on('data', () => {});

	await new Promise((resolve, reject) => {
		if (error) {
			return reject({
				message: `Error while exporting.${error}`
			});
		}

		if (taskData.compress) {
			exec(
				`cd ${folder};
					zip -r ${outputFilenameAsZip} ${outputFilenameAsCsv}
				`,
				async (errZip) => {
					if (errZip) {
						return reject({
							error: true,
							message: 'File exported, but cant compress it (Zip File)'
						});
					}

					await remove(outputFilePath);

					return resolve({
						message: 'Success to export and compress file'
					});
				}
			);
		}

		return resolve({ message: 'Success to export' });
	});

	return { status: 'done' };
}
