import { QueryBuilder } from '@/components/builder/use-cases/builder';
import type { TaskContextType } from '@gaio/shared/types';
import type { BuilderTaskType } from '@gaio/shared/types';
import {
	type TaskWithSchema,
	bucketConnection,
	createTableAsSelectAtBucket,
	createTableAtBucket,
	dropOrTruncateUsedSource,
	dropTableIfExist,
	getColumnSchema,
	insertAsSelect
} from '../runner.tools';

async function optimizeTable(
	taskData: BuilderTaskType,
	taskContext?: TaskContextType
) {
	if (taskData.optimize) {
		const connection = await bucketConnection(taskData, taskContext);

		await connection.query(`OPTIMIZE TABLE ${taskData.resultTable} FINAL`);
	}
}

export default async function (
	taskData: BuilderTaskType,
	taskContext: TaskContextType,
	_: AbortController
) {
	const { params } = taskContext;
	if (!taskData.insertMode) {
		await dropTableIfExist(taskData, taskContext);
	}

	const columns = await getColumnSchema(taskData as TaskWithSchema);

	const query = await new QueryBuilder().generate(taskData, params);

	if (taskData.insertMode) {
		await createTableAtBucket(taskData, columns, taskContext);

		await insertAsSelect(taskData, columns.fields, query, taskContext);

		await optimizeTable(taskData, taskContext);
	} else {
		await createTableAsSelectAtBucket(taskData, columns, query, taskContext);

		await optimizeTable(taskData, taskContext);
	}

	await dropOrTruncateUsedSource(taskData, taskContext);

	return { success: true };
}
