import { QueryBuilder } from '@/components/builder/use-cases/builder';
import type { TaskContextType } from '@gaio/shared/types';
import type { UpdateRowTaskType } from '@gaio/shared/types';
import { executeQuery, killMutation } from '../runner.tools';

export default async function (
	taskData: UpdateRowTaskType,
	taskContext: TaskContextType,
	abortController: AbortController
) {
	const { params } = taskContext;

	if (taskData.clearMutation) {
		await killMutation({
			taskData,
			taskContext
		});
	}

	await executeQuery({
		abortSignal: abortController.signal,
		at: taskData,
		sql: await new QueryBuilder().generate(taskData, params),
		taskContext
	});

	return { status: 'done' };
}
