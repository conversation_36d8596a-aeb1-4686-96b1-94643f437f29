import SourceRepository from '@/components/source/source.repository';
import templateString from '@/utils/template-string';
import type { TaskContextType } from '@gaio/shared/types';
import type { CloudFileType } from '@gaio/shared/types';
import {
	dropTableIfExist,
	executeQuery,
	prepareSchemaInference
} from '../runner.tools';

export default async function (
	taskData: CloudFileType,
	taskContext: TaskContextType,
	abortController: AbortController
) {
	const { params } = taskContext;
	const sourceData = await SourceRepository.getSourceById(taskData.sourceId);

	const filePathWithParameters = templateString(
		taskData.fileData.filePath,
		params
	);

	const filePath = `${sourceData.host}/${filePathWithParameters}`
		.replace(/\/\//g, '/')
		.replace('https:/', 'https://');

	let fileFormatType = taskData.fileFormatType;
	let delimiter = ',format_csv_allow_single_quotes=1';
	if (
		!['TabSeparatedWithNames', 'CSVWithNames'].includes(taskData.fileFormatType)
	) {
		fileFormatType = 'CSVWithNames';
		delimiter = `, format_csv_delimiter='${taskData.fileFormatType}', format_csv_allow_single_quotes=1`;
	}

	delimiter += ',input_format_null_as_default=1';

	try {
		if (taskData.insertMode) {
			await executeQuery({
				abortSignal: abortController.signal,
				at: taskData,
				sql: `
					INSERT INTO ${taskData.resultTable} SELECT * FROM s3( '${filePath}',
						'${sourceData.accessKey}',
						'${sourceData.secretKey}',
						'${fileFormatType}') 
					SETTINGS input_format_skip_unknown_fields = 1
						${delimiter}`,
				taskContext
			});
		} else {
			let schemaInference = '';
			if (!taskData.insertMode) {
				await dropTableIfExist(taskData, taskContext);
				schemaInference = prepareSchemaInference(
					taskData.fileFormatType,
					taskData.schemaInference
				);
			}

			await executeQuery({
				abortSignal: abortController.signal,
				at: taskData,
				sql: `
					CREATE TABLE ${taskData.resultTable} 
					ENGINE = MergeTree ORDER BY tuple()
					AS SELECT * FROM
						s3( '${filePath}',
								'${sourceData.accessKey}',
								'${sourceData.secretKey}',
								'${fileFormatType}') 
					SETTINGS input_format_skip_unknown_fields = 1, 
						${schemaInference}
						${delimiter}`,
				taskContext
			});
		}

		return { status: 'success' };
	} catch (error) {
		if (error?.data?.[0]) {
			const { message, code } = error.data[0];
			throw { code, error: true, message };
		}

		if (error?.data?.message) {
			const { message, code } = error.data;
			throw { code, error: true, message };
		}

		const { message, code, errno, query } = error;
		throw { code, errno, error: true, message, query };
	}
}
