import type { InsertTableTaskType, TaskContextType } from '@gaio/shared/types';
import { getBucketNameFromAppId } from '@gaio/shared/utils';
import { executeQuery } from '../runner.tools';

export default async function (
	taskData: InsertTableTaskType,
	taskContext: TaskContextType,
	abortController: AbortController
) {
	const resultTable = `${getBucketNameFromAppId(taskData.appId)}.${taskData.resultTable}`;
	const sourceTable = `${getBucketNameFromAppId(taskData.appId)}.${taskData.tableName}`;

	const listTarget: unknown[] = [];
	const listSource: unknown[] = [];

	taskData.schema.select.forEach((o) => {
		if (o.targetColumn !== 'none') {
			listTarget.push(o.targetColumn);
			listSource.push(o.columnName);
		}
	});

	return await executeQuery({
		abortSignal: abortController.signal,
		at: taskData,
		sql: `
			INSERT INTO ${resultTable} (${listTarget.toString()})
			SELECT ${listSource.toString()}
			FROM ${sourceTable}`,
		taskContext
	});
}
