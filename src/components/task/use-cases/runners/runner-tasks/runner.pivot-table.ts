import type { PivotTaskType, TaskContextType } from "@gaio/shared/types";
import { getBucketNameFromAppId } from "@gaio/shared/utils";
import { dropTableIfExist, executeQuery } from "../runner.tools";

export default async function (
  taskData: PivotTaskType,
  taskContext: TaskContextType,
  abortController: AbortController,
) {
  try {
    await dropTableIfExist(taskData, taskContext);

    const pivotQuery = await genPivotTableQuery(taskData);

    await executeQuery({
      abortSignal: abortController.signal,
      at: taskData,
      sql: `
				CREATE TABLE ${getBucketNameFromAppId(taskData.appId)}.${taskData.resultTable}
					ENGINE = MergeTree
					ORDER BY tuple
				(
				) AS ${pivotQuery}
			`,
      taskContext,
    });

    return { status: "done" };
  } catch (error) {
    console.error("Error executing pivot table task:", error);
    throw error;
  }
}

async function genPivotTableQuery(taskData: PivotTaskType): Promise<string> {
  let query = "SELECT ";
  const union: string[] = [];

  if (
    taskData.extraFieldsPosition === "start" &&
    taskData.extraFields.length > 0
  ) {
    union.push(...taskData.extraFields);
  }

  const taskColumns = taskData.columns || [];

  taskColumns.forEach((col) => {
    let doCase = `
      CASE WHEN ${taskData.transposeColumn} = '${col[taskData.transposeColumn]}'
      THEN ${taskData.transposeColumnValue}
      END
    `;

    doCase = `${aggregateIfNeeded(taskData.transposeAggregator, doCase)} AS \`${col.transposeName}\``;
    union.push(doCase);
  });

  if (
    taskData.extraFieldsPosition === "end" &&
    taskData.extraFields.length > 0
  ) {
    union.push(...taskData.extraFields);
  }

  query += union.join(", ");

  if (taskData.transposeAggregator && taskData.transposeAggregator !== "none") {
    if (taskData.extraFields && taskData.extraFields.length > 0) {
      query += ` FROM ${getBucketNameFromAppId(taskData.appId)}.${taskData.tableName} GROUP BY ${taskData.extraFields.join(", ")}`;
    } else {
      query += ` FROM ${getBucketNameFromAppId(taskData.appId)}.${taskData.tableName}`;
    }
  } else {
    query += ` FROM ${getBucketNameFromAppId(taskData.appId)}.${taskData.tableName}`;
  }

  console.log("run this", query);

  return query;
}

function aggregateIfNeeded(aggregator: string, query: string): string {
  if (aggregator !== "none") {
    if (aggregator === "count") {
      return `${aggregator}(${query})`;
    }
    return `${aggregator}(toFloat32(${query}))`;
  }
  return query;
}
