import type { SampleTaskType, TaskContextType } from '@gaio/shared/types'
import { dropTableIfExist, executeQuery } from '../runner.tools'

const calculateLimit = (taskData: Partial<SampleTaskType>) => {
	if (taskData.calcType === 'percent') {
		return ` LIMIT CAST(toInt64(round((SELECT count(*) FROM ${taskData.databaseName}.${
			taskData.tableName
		}) * ${Number(taskData.calcValue)}, 0)), 'Int64')`
	}
	return ` LIMIT ${Number(taskData.calcValue)} `
}

const handleError = (error: unknown) => {
	const { message, code, errno, query } =
	Boolean(error.data?.[0]) || error.data || error
	throw { code, errno, error: true, message, query }
}

export default async function(
	taskData: SampleTaskType,
	taskContext: TaskContextType,
	abortController: AbortController
) {
	await dropTableIfExist(taskData, taskContext)
	const limit = calculateLimit(taskData)

	try {
		return await executeQuery({
			abortSignal: abortController.signal,
			at: taskData,
			sql: `
				CREATE TABLE
					${taskData.databaseName}.${taskData.resultTable}
					ENGINE = MergeTree
					ORDER BY tuple
				(
				) as
				SELECT generateUUIDv4() as uuid, *
				FROM ${taskData.databaseName}.${taskData.tableName}
				ORDER BY uuid ASC
					${limit}`
			,
			taskContext
		})
	} catch (error) {
		handleError(error)
	}
}
