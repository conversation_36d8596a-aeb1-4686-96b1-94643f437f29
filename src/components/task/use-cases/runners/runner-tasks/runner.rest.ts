import RepoRepository from '@/components/repo/repo.repository';
import { fixBadDataAtJson } from '@/utils/index';
import { renderStringContext } from '@/utils/template-string';
import type { GenericType, ParamType } from '@gaio/shared/types';
import type { RestTaskType, TaskContextType } from '@gaio/shared/types';
import { getBucketNameFromAppId } from '@gaio/shared/utils';
import { at, isArray, isObject } from 'lodash-es';
import { ofetch } from 'ofetch';
import * as qs from 'qs';
import { shell } from '../runner.shell';

import {
	createCsvFileFromAnArray,
	createTableAtBucket,
	dropTableIfExist,
	executeQuery,
	getColumnSchema,
	getFilenameBySessionId,
	logComment,
	makeSureFolderExist,
	removeLocal,
	resourcesFolder,
	sanitizeColumnName
} from '../runner.tools';

interface ValueFromRest {
	[key: string]: unknown;
}

interface LogEntry {
	type: 'success' | 'error';
	message: string;
	created_at?: Date;
	[key: string]: unknown;
}

interface ErrorResponse {
	code?: string;
	data?: unknown;
	message?: string;
	msn?: string;
	status?: string;
	statusText?: string;
}

type RestResult = GenericType[] | GenericType;

interface ContextType {
	params: Record<string, string>;
	table: Record<string, GenericType>;
}

// interface SourceValuesType {
// 	[key: string]: unknown;
// }

export default async function (
	taskData: RestTaskType,
	taskContext: TaskContextType,
	abortController: AbortController
) {
	if (taskData.resultTableTruncate) {
		await dropTableIfExist(taskData, taskContext);
	}

	if (taskData.tableName) {
		await listDataPaginated(taskData, taskContext, abortController);
	} else {
		await requestExternal(taskData, {}, taskContext, abortController);
	}

	return { status: 'done' };
}

async function listDataPaginated(
	taskData: RestTaskType,
	taskContext: TaskContextType,
	abortController: AbortController,
	page = 0
): Promise<void> {
	let hasMoreData = true;

	while (hasMoreData) {
		const restResult = await executeQuery({
			at: taskData,
			sql: `select * from ${taskData.tableName} limit 100 offset ${page}`,
			taskContext,
			abortSignal: abortController.signal
		});

		const list = restResult.data as GenericType[];
		if (!list?.length) {
			hasMoreData = false;
			return;
		}

		const batchSize = 10;
		for (let i = 0; i < list.length; i += batchSize) {
			const batch = list.slice(i, i + batchSize);
			await Promise.all(
				batch.map((obj) =>
					requestExternal(taskData, obj, taskContext, abortController)
				)
			);
			batch.length = 0;
		}

		list.length = 0;
		await new Promise((resolve) => setTimeout(resolve, 100));
		page += 100;
	}
}

async function requestExternal(
	taskData: RestTaskType,
	sourceData: GenericType,
	taskContext: TaskContextType,
	abortController: AbortController
): Promise<{ status: string }> {
	let hasUrlencoded = false;
	let dataFromSource: RestResult = null;

	for (const header of taskData.headers) {
		if (header.value?.toLowerCase().includes('urlencoded')) {
			hasUrlencoded = true;
		}
	}

	const params: ParamType[] = taskContext?.params || [];
	const canSendBody = ['POST', 'PUT', 'PATCH'].includes(taskData.method);

	const context: ContextType = {
		params: params.reduce(
			(acc, param) => {
				acc[param.paramName] = param.paramValue;
				return acc;
			},
			{} as Record<string, string>
		),
		table: {}
	};

	if (taskData.tableName) {
		context.table[taskData.tableName] = sourceData;
	}

	const headers: Record<string, string> = taskData.headers?.length
		? Object.fromEntries(
				taskData.headers.map(({ prop, value }) => [prop, value])
			)
		: {};

	const url = await renderStringContext(taskData.url, context);
	const body = canSendBody
		? await renderStringContext(taskData.body, context)
		: null;

	if (taskData.basicPassword) {
		taskData.basicPassword = await renderStringContext(
			taskData.basicPassword,
			context
		);
	}

	if (taskData.basicUsername) {
		taskData.basicUsername = await renderStringContext(
			taskData.basicUsername,
			context
		);
	}

	if (headers) {
		for (const [key, value] of Object.entries(headers)) {
			headers[key] = await renderStringContext(value, context);
		}
	}

	if (!headers.Connection) {
		headers.Connection = 'close';
	}

	const controller = new AbortController();
	const timeout = setTimeout(() => controller.abort(), 30000);

	try {
		if (taskData.basicPassword && taskData.basicUsername) {
			const basicAuth = Buffer.from(
				`${taskData.basicUsername}:${taskData.basicPassword}`
			).toString('base64');
			headers.Authorization = `Basic ${basicAuth}`;
		}

		const response = await ofetch.raw(url, {
			body:
				taskData.method === 'GET'
					? undefined
					: canSendBody
						? hasUrlencoded
							? qs.stringify(JSON.parse(body))
							: JSON.parse(body)
						: null,
			headers: new Headers(headers),
			method: taskData.method,
			signal: controller.signal
		});

		if (!response.ok) {
			await saveLog(
				taskData,
				{
					message: `HTTP error! status: ${response.status}`
				},
				'error',
				sourceData,
				taskContext
			);
			return { status: 'error' };
		}

		try {
			if (typeof response._data === 'string') {
				dataFromSource = JSON.parse(response._data);
			} else {
				dataFromSource = response._data;
			}
		} catch (e) {
			throw new Error(
				'Error parsing JSON response. Please check the response format.'
			);
		}

		const result = await saveResult(
			taskData,
			dataFromSource,
			sourceData,
			taskContext,
			abortController
		);
		return result;
	} catch (e) {
		await saveLog(
			taskData,
			e.response ? e.response : e,
			'error',
			sourceData,
			taskContext
		);
		return { status: 'error' };
	} finally {
		clearTimeout(timeout);
		dataFromSource = null;
	}
}

async function saveResult(
	taskData: RestTaskType,
	result: unknown,
	sourceData: GenericType,
	taskContext: TaskContextType,
	_: AbortController
): Promise<{ status: string }> {
	if (taskData.restType === 'sms') {
		// deprecated
		await saveLog(taskData, result, 'success', sourceData, taskContext);
		return { status: 'finished' };
	}

	if (taskData.resultTable && result) {
		const finalResult = retrieveDataEndpoint(taskData, result);

		if (taskData.createTableType === 'automatic') {
			const keys = Object.keys(
				isArray(finalResult)
					? finalResult[0]
					: isObject(finalResult)
						? finalResult
						: {}
			);

			keys.forEach((key) => {
				taskData.resultTableFields.push({
					columnName: sanitizeColumnName(key),
					dataType: 'Nullable(String)'
				});
			});
		}

		await prepareResultTable(taskData, taskContext);

		const fields = taskData.resultTableFields.map((o) => o.columnName);

		const prepareValues = (item: GenericType): unknown[] => {
			const valueFromRest: ValueFromRest = {};

			for (const [key, value] of Object.entries(item)) {
				valueFromRest[sanitizeColumnName(key)] = value;
			}

			return fields.map((field) => valueFromRest[field]);
		};

		const insert = (item: GenericType): Record<string, unknown> => {
			const values: Record<string, unknown> = {};
			const valueResults = prepareValues(item);

			fields.forEach((field, index) => {
				values[field] =
					isArray(valueResults[index]) || isObject(valueResults[index])
						? JSON.stringify(valueResults[index])
						: valueResults[index];
			});

			if (taskData.tableName && taskData.transferSourceData?.length) {
				taskData.transferSourceData.forEach((sourceColumn) => {
					values[sourceColumn] = sourceData[sourceColumn];
				});
			}

			return values;
		};

		const folderOfTheInputCsvFile = resourcesFolder(taskData.appId);
		const filename = getFilenameBySessionId(
			taskData.id,
			taskContext?.sessionid,
			'csv'
		);
		const filePath = `${folderOfTheInputCsvFile}/${filename}`;

		try {
			await makeSureFolderExist(folderOfTheInputCsvFile);

			const chunkSize = 1000;
			const items = isArray(finalResult) ? finalResult : [finalResult];

			for (let i = 0; i < items.length; i += chunkSize) {
				const chunk = items.slice(i, i + chunkSize);
				const baseToInsert = chunk.map((item) =>
					!taskData.dontFixBadData
						? fixBadDataAtJson(insert(item))
						: insert(item)
				);

				if (baseToInsert.length > 0) {
					await createCsvFileFromAnArray(baseToInsert, filePath);

					const repoData = await RepoRepository.getRepoCredentials(taskData);
					const query = `insert into ${getBucketNameFromAppId(taskData.appId)}.${
						taskData.resultTable
					} FORMAT CSVWithNames`;

					const localArgs = [
						filePath,
						'|',
						'clickhouse-client',
						`--host=${repoData.host}`,
						`--user=${repoData.user}`,
						`--password=${repoData.password} `,
						'--format_csv_allow_single_quotes=1',
						'--format_csv_allow_double_quotes=1',
						'--date_time_input_format="best_effort"',
						'--input_format_null_as_default=1',
						'--input_format_defaults_for_omitted_fields=1',
						'--input_format_skip_unknown_fields=1',
						'--input_format_tsv_empty_as_default=1',
						logComment(taskData),
						`--query="${query}"`
					];

					await shell('pipe data and transfer to repository', 'cat', [
						...localArgs
					]);
				}

				chunk.length = 0;
				baseToInsert.length = 0;
			}
		} finally {
			removeLocal([filePath]);
		}

		return { status: 'done:3' };
	}

	return { status: 'finished' };
}

async function prepareResultTable(
	taskData: RestTaskType,
	taskContext: TaskContextType
) {
	if (
		taskData.resultTable &&
		taskData.resultTable !== '' &&
		taskData.resultTableFields.length > 0
	) {
		if (taskData.tableName) {
			for (const sourceColumn of taskData.transferSourceData) {
				if (
					!taskData.resultTableFields.some((o) => o.columnName === sourceColumn)
				) {
					taskData.resultTableFields.unshift({
						columnLength: undefined,
						columnName: sourceColumn,
						dataType: 'Nullable(String)',
						sourceType: 'source'
					});
				}
			}
		}
		await createTableAtBucket(
			taskData,
			await getColumnSchema({
				...taskData,
				columns: taskData.resultTableFields
			}),
			taskContext
		);
	}
	return 'success';
}

function retrieveDataEndpoint(taskData: RestTaskType, result: RestResult) {
	return taskData.resultTableProp
		? at(result, [taskData.resultTableProp])[0]
		: result;
}

async function saveLog(
	taskData: RestTaskType,
	res: unknown,
	type: 'success' | 'error',
	sourceData: GenericType,
	taskContext: TaskContextType
): Promise<LogEntry | undefined> {
	if (!taskData.keepLogTable) return undefined;
	sourceData = sourceData || {};

	const log: object | string =
		res instanceof Error
			? res.message
			: typeof res === 'object'
				? res
				: String(res);

	if (
		(taskData.type === 'rest' && type === 'error') ||
		(taskData.type !== 'rest' && type === 'success')
	) {
		const logTable = `log_${taskData.keepLogTable}`;
		const extraColumn = taskData.keepLogTableExtraColumn
			? `${taskData.keepLogTableExtraColumn} Nullable(String),`
			: '';

		await executeQuery({
			at: taskData,
			sql: `
				CREATE TABLE IF NOT EXISTS ${getBucketNameFromAppId(taskData.appId)}.${logTable}
				(
					${extraColumn}
					created_at Datetime DEFAULT now(),
					type Nullable(String),
					message Nullable(String)
				)
				engine = MergeTree
				ORDER BY tuple()
			`,
			taskContext
		});

		let message: string;

		try {
			message = JSON.stringify(log);
		} catch (e) {
			message = typeof log === 'string' ? log : 'Unknown';
		}

		const fixBadChars = (str: string): string => {
			return `${str || ''}`
				.replace(/\\t/g, ' ')
				.replace(/\t/g, ' ')
				.replace(/\\r/g, ' ')
				.replace(/\r/g, ' ')
				.replace(/\n/g, ' ')
				.replace(/\\n/g, ' ')
				.replace(/</g, '')
				.replace(/>/g, '')
				.replace(/"/g, ' ')
				.replace(/'/g, ' ');
		};

		const columnNameToExtra = taskData.keepLogTableExtraColumn
			? ` ${taskData.keepLogTableExtraColumn}`
			: '';
		const valueToExtra = taskData.keepLogTableExtraColumn
			? `, '${sourceData[taskData.keepLogTableExtraColumn]}'`
			: '';

		const bucketName = getBucketNameFromAppId(taskData.appId);

		await executeQuery({
			at: taskData,
			sql: `insert into ${bucketName}.${logTable} (type, message${columnNameToExtra})
						values ('${type}', '${fixBadChars(message)}'${valueToExtra})`,
			taskContext
		});

		if (taskData.keepLogDays) {
			await executeQuery({
				at: taskData,
				sql: `ALTER TABLE ${bucketName}.${logTable} DELETE WHERE created_at < subtractDays(now(), ${
					Number(taskData.keepLogDays) || 2
				})`,
				taskContext
			});
		}
	} else {
		if (res) {
			const errorRes = res as ErrorResponse;
			return {
				type: 'error',
				message: errorRes.message || errorRes.msn || 'Unknown error',
				code: errorRes.code || '',
				data: errorRes.data || '',
				status: errorRes.status || '',
				statusText: errorRes.statusText || ''
			};
		}
		return {
			type: 'error',
			message: 'Unknown error'
		};
	}
}
