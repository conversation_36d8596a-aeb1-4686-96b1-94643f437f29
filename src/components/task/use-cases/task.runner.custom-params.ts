import templateString from '@/utils/template-string';
import type { ParamType, TaskType } from '@gaio/shared/types';
import { $ } from 'bun';

export const runnerCustomParam = async (
	taskData: TaskType,
	paramValue: string,
	params: ParamType[] = []
): Promise<string> => {
	if (paramValue.includes('params.')) {
		const preParam = templateString(
			`${paramValue.replace(/params./gi, '')}`,
			params
		);

		return await runnerCustomParam(taskData, `{{${preParam}}}`, params);
	}

	const { stdout } =
		await $`clickhouse-local -q "SELECT (${paramValue.replace(/\{{|}}/gi, '')}) AS paramValue limit 1"`;

	return stdout.toString()?.trim()?.split('\n')?.join(' ');
};

export const getCustomParamValue = async (params: ParamType[]) => {
	for await (const param of params) {
		try {
			const { stdout } =
				await $`clickhouse-local -q "SELECT (${param.paramValue.replace(
					/\{{|}}/gi,
					''
				)}) AS paramValue limit 1"`; // 120
			param.paramValue = stdout.toString()?.trim()?.split('\n')?.join(' ');
		} catch {
			param.paramValue = '';
		}
	}

	return params;
};
