import type { ParamType, QueryTaskType } from '@gaio/shared/types';
import type { z } from 'zod';
import type { taskRunRequestSchema } from './task.schema';

export type TaskQueryRequestDTO = {
	params: ParamType[];
	taskData: QueryTaskType;
	flowId: string;
};

export type TaskLogsRequestDTO = {
	appId: string;
	logType: string;
};

export type TaskAbortRequestDTO = {
	taskLogId: string;
};

export type TaskStatusRequestDTO = {
	appId: string;
};

export type TaskRunRequestDTO = z.infer<typeof taskRunRequestSchema>;

// export type TaskRunnerDTO = TaskRunRequestDTO & { sessionid: string; userId: string }
