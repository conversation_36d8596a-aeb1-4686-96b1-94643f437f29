import { taskAbort } from '@/components/task/use-cases/task.abort';
import { taskListLog } from '@/components/task/use-cases/task.list.log';
import { taskQuery } from '@/components/task/use-cases/task.query';
import type { HttpRequestData } from '@/components/task/use-cases/task.rest';
import { taskRest } from '@/components/task/use-cases/task.rest';
import { prepareFlowAndRun } from '@/components/task/use-cases/task.runner';
import { getCustomParamValue } from '@/components/task/use-cases/task.runner.custom-params';
import { taskStatus } from '@/components/task/use-cases/task.status';
import jwtGuard from '@/server/middleware/jwt.guard';
import { readBody } from '@/server/middleware/readBody';
import type { ParamType, QueryTaskType } from '@gaio/shared/types';
import type { UserType } from '@gaio/shared/types';
import { type Context, Hono } from 'hono';
import type { TaskRunRequestDTO } from './definitions/task.dtos';

import { type TimingVariables, endTime, startTime } from 'hono/timing';

type Variables = TimingVariables & { user: UserType };

const app = new Hono<{ Variables: Variables }>()
	.post('/query', jwtGuard('user').isAuth, async (c) => {
		const { user, params, taskData, flowId } = await readBody<{
			params: ParamType[];
			taskData: QueryTaskType;
			flowId: string;
		}>(c);

		return c.json(await taskQuery(taskData, params, flowId, user));
	})

	.post('/test-rest', jwtGuard('user').isAuth, async (c) => {
		const { ...requestData } = await readBody<HttpRequestData>(c);

		return c.json(await taskRest(requestData));
	})

	.post('/run', jwtGuard('user').isAuth, async (c: Context) => {
		startTime(c, 'run');
		const { user, flowId, appId, params, taskExecutionScope } =
			await readBody<TaskRunRequestDTO>(c);
		const { userId, name: userName } = user;
		const sessionid = c.get('sessionid');
		const logFrom = c.get('logFrom');

		const result = await prepareFlowAndRun({
			appId,
			flowId,
			logFrom,
			params,
			sessionid,
			userId,
			taskExecutionScope,
			userName
		});

		endTime(c, 'run');

		return c.json(result);
	})

	.post('/logs', jwtGuard('dev').isAuth, async (c: Context) => {
		const { appId, user, all, logFrom } = await readBody<{
			appId: string;
			all: boolean;
			logFrom: string;
		}>(c);

		return c.json(await taskListLog(user, appId, logFrom, all));
	})

	.post('/abort', jwtGuard('dev').isAuth, async (c) => {
		const { taskLogId } = await readBody<{ taskLogId: string }>(c);

		return c.json(taskAbort(taskLogId));
	}) // dev)

	.get('/status', jwtGuard('dev').isAuth, async (c) => {
		const { user, appId } = await readBody<{ appId: string }>(c);

		return c.json(await taskStatus(user.userId, appId, c));
	}) // dev // stream todo!

	.post('/custom-param', jwtGuard('user').isAuth, async (c) => {
		const params = (await c.req.json()).params;

		return c.json(await getCustomParamValue(params));
	}); // dev

export default app;
