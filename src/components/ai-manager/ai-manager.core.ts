import {
	deleteAiManagerModel,
	getAiManagerById,
	listAiManager,
	updateAiManagerOptions,
	updateOrNewAiModel
} from '@/components/ai-manager/ai-manager.repository';
import type { AiManagerType } from '@gaio/shared/types';
import AppRepository from '../app/app.repository';

export async function saveAiModel(
	aiManagerData: AiManagerType,
	userId: string
) {
	return updateOrNewAiModel(aiManagerData, userId);
}

export async function defineAiModelAtAllApps(aiManagerId: string) {
	await AppRepository.updateAiModelAtAllApps(aiManagerId);
	return {
		message: 'AI model defined at all apps',
		aiManagerId
	};
}

export async function saveAiModelOptions(aiManagerData: AiManagerType) {
	return updateAiManagerOptions(
		aiManagerData.options,
		aiManagerData.aiManagerId
	);
}

export async function listAiModels() {
	return await listAiManager();
}

export async function getAiModelById(aiManagerId: string) {
	return await getAiManagerById(aiManagerId);
}

export async function deleteAiModel(aiManagerId: string) {
	return await deleteAiManagerModel(aiManagerId);
}
