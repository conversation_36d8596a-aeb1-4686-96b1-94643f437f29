import {
	defineAiModelAtAllApps,
	deleteAiModel,
	getAiModelById,
	listAiModels,
	saveAiModel,
	saveAiModelOptions
} from '@/components/ai-manager/ai-manager.core';
import jwtGuard from '@/server/middleware/jwt.guard';
import { readBody } from '@/server/middleware/readBody';
import type { AiManagerType } from '@gaio/shared/types';
import { Hono } from 'hono';

const app = new Hono()
	.post('/save-options', jwtGuard('admin').isAuth, async (c) => {
		const { aiManagerData } = await readBody<{
			aiManagerData: AiManagerType;
		}>(c);
		return c.json(await saveAiModelOptions(aiManagerData));
	})
	.post('/define-model/:aiManagerId', jwtGuard('admin').isAuth, async (c) => {
		const aiManagerId = c.req.param('aiManagerId');
		return c.json(await defineAiModelAtAllApps(aiManagerId));
	})
	.post('/save', jwtGuard('admin').isAuth, async (c) => {
		const { user, aiManagerData } = await readBody<{
			aiManagerData: AiManagerType;
		}>(c);
		return c.json(await saveAiModel(aiManagerData, user?.userId || ''));
	})
	.get('/list/:aiManagerId', jwtGuard('user').isAuth, async (c) => {
		const { aiManagerId } = c.req.param();
		return c.json(await getAiModelById(aiManagerId));
	})
	.get('/list', jwtGuard('dev').isAuth, async (c) => {
		return c.json(await listAiModels());
	})
	.delete('/delete/:aiManagerId', jwtGuard('admin').isAuth, async (c) => {
		const { aiManagerId } = c.req.param();
		return c.json(await deleteAiModel(aiManagerId));
	});

export default app;
