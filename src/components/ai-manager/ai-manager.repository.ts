import { dbGaio } from '@/db/db.gaio';
import { currentDatetime } from '@/utils/helpers';
import type { AiManagerType, GenericType } from '@gaio/shared/types';
import { getId } from '@gaio/shared/utils';

export async function updateOrNewAiModel(
	aiManagerData: GenericType,
	userId: string
) {
	aiManagerData.aiManagerId = aiManagerData.aiManagerId || getId();
	await dbGaio().updateOrInsert({
		primaryKeys: ['aiManagerId'],
		table: 'ai_manager',
		values: {
			aiManagerId: aiManagerData.aiManagerId,
			aiManagerName: aiManagerData.aiManagerName,
			credentials: JSON.stringify(aiManagerData.credentials || {}),
			options: JSON.stringify(aiManagerData.options || {}),
			supplier: aiManagerData.supplier,
			createdBy: userId,
			updatedBy: userId,
			updatedAt: currentDatetime().replace('T', ' ').replace('Z', '')
		}
	});

	return aiManagerData;
}

export async function updateAiManagerOptions(
	options: GenericType,
	aiManagerId: string
) {
	await dbGaio('update-options-ai-manager').query<AiManagerType[]>(
		`
			ALTER TABLE ai_manager
			UPDATE options = {options: String}
			WHERE aiManagerId = {aiManagerId: String}`,
		{
			params: {
				options: JSON.stringify(options),
				aiManagerId
			}
		}
	);

	return options;
}

export async function listAiManager() {
	const aiManagerList = await dbGaio('list-ai-manager').query<AiManagerType>(
		`
			SELECT aiManagerId,
						 aiManagerName,
						 credentials,
						 options
			FROM ai_manager
		`,
		{
			parse: ['credentials', 'options']
		}
	);

	return aiManagerList
		.map((o) => {
			o.credentials = o.credentials || {};
			o.credentials.apiKey = undefined;
			return {
				...o
			};
		})
		.sort((o) => {
			if (o?.options?.preferential) {
				return -1;
			}
			return 1;
		});
}

export async function getAiManagerById(
	aiManagerId: string
): Promise<AiManagerType> {
	const aiManagerList = await dbGaio(
		'get-ai-manager-by-id'
	).query<AiManagerType>(
		`
			SELECT credentials, options
			FROM ai_manager
			WHERE aiManagerId = {aiManagerId: String}`,
		{
			params: {
				aiManagerId
			},
			parse: ['credentials', 'options']
		}
	);

	return aiManagerList[0];
}

export async function getAiOpenAiToken(aiManagerId: string) {
	const aiManagerData = await getAiManagerById(aiManagerId);
	const { credentials } = aiManagerData;
	const apiKey = credentials?.apiKey;

	return apiKey;
}

export async function deleteAiManagerModel(aiManagerId: string) {
	await dbGaio('delete-ai-manager-model').query<AiManagerType[]>(
		`
			ALTER TABLE ai_manager
			DELETE
			WHERE aiManagerId = {aiManagerId: String}`,
		{
			params: {
				aiManagerId
			}
		}
	);

	return await listAiManager();
}
