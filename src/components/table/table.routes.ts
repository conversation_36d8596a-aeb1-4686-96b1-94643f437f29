import { tableQuery } from '@/components/table/table.query';
import { tableHistogram } from '@/components/table/use-cases/table-histogram';
import { tableCount } from '@/components/table/use-cases/table.count';
import { tableDescribe } from '@/components/table/use-cases/table.describe';
import { tableDistinct } from '@/components/table/use-cases/table.distinct';
import { tableEmpty } from '@/components/table/use-cases/table.empty';
import { tableFields } from '@/components/table/use-cases/table.fields';
import type { LocalTaskType } from '@/components/table/use-cases/table.frequency';
import { tableFrequency } from '@/components/table/use-cases/table.frequency';
import { tableList } from '@/components/table/use-cases/table.list';
import { tableMinMax } from '@/components/table/use-cases/table.min.max';
import { tablePreviewSqlQuery } from '@/components/table/use-cases/table.preview-sql-query';
import { tableReport } from '@/components/table/use-cases/table.report';
import { tableRows } from '@/components/table/use-cases/table.rows';
import { tableStats } from '@/components/table/use-cases/table.stats';
import runnerQuery from '@/components/task/use-cases/runners/runner-tasks/runner.query';
import jwtGuard from '@/server/middleware/jwt.guard';
import { readBody } from '@/server/middleware/readBody';
import type { TablePreviewSqlQueryDTO } from '@/types/http/dtos/table.dtos';
import type {
	BuilderTaskType,
	CommonBuilderTaskType,
	GenericType,
	ParamType,
	ReportNodeType,
	SchemaFilterType,
	TaskContextType,
	TaskType
} from '@gaio/shared/types';
import type { DeleteTaskType } from '@gaio/shared/types';
import type { Context } from 'hono';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { HTTPException } from 'hono/http-exception';
import { tableDocs } from './table.docs';
import { tableDrop, tableDropView } from './use-cases/table.drop';
import { tableDownload } from './use-cases/table.download';
import { tableTruncate } from './use-cases/table.truncate';

const app = new Hono()
	.post('/field', jwtGuard().isAuth, async (c) => {
		const { taskData } = await readBody<{ taskData: TaskType }>(c);
		return c.json(await tableFields(taskData));
	})

	.post('/describe', jwtGuard().isAuth, async (c) => {
		const { taskData } = await readBody<{ taskData: CommonBuilderTaskType }>(c);
		return c.json(await tableDescribe(taskData));
	})

	.post('/query', jwtGuard().isAuth, async (c) => {
		const { taskData, schema } = await readBody<{
			taskData: CommonBuilderTaskType;
			schema: GenericType;
		}>(c);
		return c.json(await tableQuery(taskData, schema));
	})

	.post('/frequency', jwtGuard().isAuth, async (c) => {
		const { taskData, filters } = await readBody<{
			taskData: LocalTaskType;
			filters: SchemaFilterType[];
		}>(c);
		return c.json(await tableFrequency(taskData, filters));
	})

	.post('/rows', jwtGuard().isAuth, async (c: Context) => {
		const { taskData, params } = await readBody<{
			taskData: BuilderTaskType;
			params: ParamType[];
		}>(c);

		const taskContext: TaskContextType = {
			logFrom: c.get('logFrom'),
			sessionid: c.get('sessionid'),
			params: params
		};
		return c.json(await tableRows(taskData, taskContext));
	})

	.post('/list', jwtGuard().isAuth, async (c) => {
		const { taskData } = await readBody<{ taskData: TaskType }>(c);
		return c.json(await tableList(taskData));
	})

	.post('/download', jwtGuard().isAuth, async (c: Context) => {
		const { taskData, params } = await readBody<{
			taskData: BuilderTaskType;
			params: ParamType[];
		}>(c);

		const taskContext: TaskContextType = {
			logFrom: c.get('logFrom'),
			sessionid: c.get('sessionid'),
			params: params
		};

		return tableDownload(taskData, taskContext, c);
	})

	.post('/count', jwtGuard().isAuth, async (c) => {
		const { taskData, filters } = await readBody<{
			taskData: CommonBuilderTaskType;
			filters: SchemaFilterType[];
		}>(c);
		return c.json(await tableCount(taskData, filters));
	})

	.post('/stats', jwtGuard().isAuth, async (c) => {
		const { taskData, filters } = await readBody<{
			taskData: CommonBuilderTaskType;
			filters: SchemaFilterType[];
		}>(c);
		return c.json(await tableStats(taskData, filters));
	})

	.post('/empty', jwtGuard().isAuth, async (c) => {
		const { taskData, filters } = await readBody<{
			taskData: CommonBuilderTaskType;
			filters: SchemaFilterType[];
		}>(c);
		return c.json(await tableEmpty(taskData, filters));
	})

	.post('/count-distinct', jwtGuard().isAuth, async (c) => {
		const { taskData, filters } = await readBody<{
			taskData: CommonBuilderTaskType;
			filters: SchemaFilterType[];
		}>(c);
		return c.json(await tableDistinct(taskData, filters));
	})

	.post('/min-max', jwtGuard().isAuth, async (c) => {
		const { taskData, filters } = await readBody<{
			taskData: CommonBuilderTaskType;
			filters: SchemaFilterType[];
		}>(c);
		return c.json(await tableMinMax(taskData, filters));
	})

	.post('/histogram', jwtGuard().isAuth, async (c) => {
		const { taskData } = await readBody<{ taskData: CommonBuilderTaskType }>(c);
		return c.json(await tableHistogram(taskData));
	})

	.post('/report', jwtGuard().isAuth, async (c: Context) => {
		const { taskData, params } = await readBody<{
			taskData: BuilderTaskType;
			params: ParamType[];
		}>(c);

		const taskContext: TaskContextType = {
			logFrom: c.get('logFrom'),
			sessionid: c.get('sessionid'),
			params: params
		};

		return c.json(await tableReport(taskData, taskContext));
	})

	.post(
		'/preview-sql-query',
		describeRoute(tableDocs.previewSqlQuery),
		jwtGuard('dev').isAuth,
		async (c: Context) => {
			try {
				const { tableData, params } =
					await readBody<TablePreviewSqlQueryDTO>(c);

				const abortController = new AbortController();

				// TODO: add abort controller listener to abort preview

				const query = tableData.query;

				if (tableData.sourceType === 'bucket') {
					return c.json({
						message: 'previewSuccessfully',
						output: await runnerQuery(
							{
								...tableData,
								query
							},
							{
								params,
								sessionid: c.get('sessionid'),
								logFrom: c.get('logFrom'),
								user: c.get('user')
							} as TaskContextType,
							abortController
						),
						timestamp: new Date().toISOString()
					});
				}

				const preview = await tablePreviewSqlQuery({
					client: tableData.client,
					params,
					query,
					sourceId: tableData.sourceId
				});

				return c.json({
					message: 'previewSuccessfully',
					output: preview,
					timestamp: new Date().toISOString()
				});
			} catch (err) {
				console.error('error preview sql ---- ', err);

				if (err instanceof HTTPException) {
					throw err;
				}

				throw new HTTPException(500, {
					cause: 'errorPreviewingSqlQuery',
					message: err
				});
			}
		}
	)
	.post('/drop', jwtGuard().isAuth, async (c) => {
		const { taskData } = await readBody<{ taskData: DeleteTaskType }>(c);

		const result = await tableDrop(taskData);

		return c.json({
			status: result.status
		});
	})

	.post('/drop-view', jwtGuard().isAuth, async (c: Context) => {
		const { taskData, params } = await readBody<{
			taskData: ReportNodeType;
			params: ParamType[];
		}>(c);

		const taskContext: TaskContextType = {
			logFrom: c.get('logFrom'),
			sessionid: c.get('sessionid'),
			params: params
		};

		await tableDropView(taskData, taskContext);

		return c.json({
			status: 'done'
		});
	})

	.post('/truncate', jwtGuard().isAuth, async (c) => {
		const { taskData } = await readBody<{ taskData: DeleteTaskType }>(c);

		const result = await tableTruncate(taskData);

		return c.json({
			status: result.status
		});
	});

export default app;
