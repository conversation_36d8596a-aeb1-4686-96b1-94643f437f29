import { DbService } from '@/db/db.service';
import type { CommonBuilderTaskType } from '@gaio/shared/types';
import { describeQuery } from './info-helpers/describe.query';

type DescribeResult = {
	engine: string;
	exists: boolean;
	modifiedAt: string;
	totalBytes: number;
	totalColumns: number;
	totalRows: number;
};

export const tableDescribe = async (taskData: CommonBuilderTaskType) => {
	const db = await new DbService().connect(taskData);
	if (taskData.client !== 'clickhouse') {
		throw 'Client not supported';
	}

	const query = describeQuery['clickhouse'](taskData);

	try {
		const res = await db.query<Array<DescribeResult>>(query);
		if (res.data && res.data.length > 0) {
			return {
				exists: true,
				...res.data[0]
			};
		}

		return {
			engine: '',
			exists: false,
			modifiedAt: '',
			totalBytes: '',
			totalColumns: '',
			totalRows: ''
		};
	} catch (err) {
		return {
			error: true,
			message: err.message,
			query
		};
	}
};
