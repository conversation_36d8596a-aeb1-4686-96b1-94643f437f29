import { DbService } from '@/db/db.service';
import type {
	BuilderTaskType,
	CommonBuilderTaskType,
	ConnectionResultType,
	SchemaFilterType
} from '@gaio/shared/types';
import { statsQuery } from './info-helpers/stats.query';
import { getFilters } from '@/utils/table-where-extractor';

export const tableEmpty = async (
	taskData: CommonBuilderTaskType,
	filters: SchemaFilterType[]
) => {
	const db = await new DbService().connect(taskData);

	const stats = statsQuery;

	const filter: string = await getFilters(taskData as BuilderTaskType, filters);

	const query = stats.withoutValues[taskData.client]
		.replace(/@db/g, taskData.databaseName)
		.replace(/@schema/g, taskData.schemaName)
		.replace(/@tb/g, taskData.tableName)
		.replace(/@cl/g, taskData.columnName)
		.replace(/@filter/g, filter);

	try {
		const res = await db.query<ConnectionResultType>(query);

		if (res?.data && Array.isArray(res.data) && res.data.length > 0) {
			return res.data[0];
		}

		return null;
	} catch (err) {
		return {
			error: true,
			message: (err as Error).message,
			query
		};
	}
};
