import type { TaskType } from '@gaio/shared/types';

export default (databaseData: TaskType) => {
	let setSQL: string;

	const { client, logFrom, showTemps } = databaseData;

	if (client === 'mysql' || client === 'mariadb' || client === 'memsql') {
		const removeTemps =
			logFrom === 'studio' && !showTemps
				? ` and table_name not like 'tmp_gaio%' `
				: '';

		setSQL = `
			SELECT
				TABLE_SCHEMA AS databaseName,
      	'${client}' AS client,
        'table' AS type,
        TABLE_ROWS AS tableRows,
        table_name AS tableName,
        table_comment AS tableComment 
      FROM information_schema.tables
      WHERE
				TABLE_SCHEMA = '${databaseData.databaseName}' ${removeTemps}
      GROUP BY
				table_name, table_comment, TABLE_SCHEMA,TABLE_ROWS
      ORDER BY
				table_name`;
	} else if (client === 'pg') {
		setSQL = `
			SELECT
				table_catalog AS "databaseName",
      	'pg' AS client,
        'table' AS type,
        '${databaseData.schemaName}' AS schemaName,
        table_name AS "tableName" 
      FROM information_schema.tables
      WHERE
				table_schema = '${databaseData.schemaName}'
					AND table_catalog = '${databaseData.databaseName}'
      GROUP BY
				table_name, table_schema, table_catalog
      ORDER BY
				table_name`;
	} else if (client === 'redshift') {
		setSQL = `
			SELECT
				table_catalog AS "databaseName",
        'redshift' AS client,
        'table' AS type,
        '${databaseData.schemaName}' AS schemaName,
        table_name AS "tableName" 
      FROM information_schema.tables
      WHERE
				table_schema = '${databaseData.schemaName}'
					AND table_catalog = '${databaseData.databaseName}'
      GROUP BY
				table_name, table_schema, table_catalog
    	ORDER BY
				table_name`;
	} else if (client === 'oracle') {
		setSQL = `
			SELECT 
        owner AS "databaseName", 
        'oracle' AS "client", 
      	'table' AS "type",
        table_name AS "tableName"
      FROM
				all_tables 
      WHERE
				owner = '${databaseData.databaseName}' 
      GROUP BY
				owner, table_name 
      UNION 
        SELECT 
          owner AS "databaseName",
          'oracle' AS "client", 
          'table' AS "type",
          view_name AS "tableName" 
        FROM all_views
				WHERE
					owner = '${databaseData.databaseName}'
        GROUP BY
					owner, view_name`;
	} else if (client === 'mssql') {
		setSQL = `
			USE ${databaseData.databaseName};
      SELECT
				'${databaseData.databaseName}' AS databaseName,
        'mssql' AS client,
        'table' AS type, 
        TABLE_NAME AS tableName,
        table_schema AS schemaName
      FROM
				INFORMATION_SCHEMA.COLUMNS 
      GROUP BY
				TABLE_NAME, table_schema
      ORDER BY
				TABLE_NAME`;
	} else if (client === 'clickhouse') {
		setSQL = `
      SELECT
  			count(system.columns.name) AS columns,
  			name AS tableName,
  			database AS databaseName,
        total_rows AS totalRows,
        total_bytes AS totalBytes,
        engine,
        'table' AS type,
        'clickhouse' AS client
			FROM
  			system.tables
  		LEFT JOIN
				system.columns ON system.tables.name = system.columns.table
  		WHERE
				database = '${databaseData.databaseName}'
    	AND
				startsWith(name, 'insights_gaio') = 0
    	AND
				startsWith(name, 'tmp_gaio') = 0
    	AND
				startsWith(name, 'g_ai_') = 0
    	GROUP BY
				name, databaseName, totalRows, totalBytes, engine, type, client`;
	} else {
		setSQL = `SHOW TABLES IN ${databaseData.schemaName}.${databaseData.databaseName || databaseData.database}`;
	}
	return setSQL;
};
