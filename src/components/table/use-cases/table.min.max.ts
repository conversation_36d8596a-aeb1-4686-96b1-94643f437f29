import { QueryBuilder } from '@/components/builder/use-cases/builder';
import { DbService } from '@/db/db.service';
import type {
	BuilderTaskType,
	CommonBuilderTaskType,
	SchemaFilterType
} from '@gaio/shared/types';
import { statsQuery } from './info-helpers/stats.query';
import { getFilters } from '@/utils/table-where-extractor';

export const tableMinMax = async (
	taskData: CommonBuilderTaskType,
	filters: SchemaFilterType[]
) => {
	const db = await new DbService().connect(taskData);
	const filter: string = await getFilters(taskData as BuilderTaskType, filters);

	const query = statsQuery.minMax[taskData.client]
		.replace(/@db/g, taskData.databaseName)
		.replace(/@schema/g, taskData.schemaName)
		.replace(/@tb/g, taskData.tableName)
		.replace(/@filter/g, filter)
		.replace(/@cl/g, taskData.columnName);

	try {
		const res = await db.query(query);
		let max;
		let min;
		let median;

		if (res.data && res.data[0]) {
			min = res.data[0].minimum;
			max = res.data[0].maximum;
			median = res.data[0].median;
		} else if (res && res[0]) {
			min = res[0].minimum;
			max = res[0].maximum;
			median = res[0].median;
		}

		return { max, median, min };
	} catch (err) {
		return {
			error: true,
			message: (err as Error).message,
			query
		};
	}
};
