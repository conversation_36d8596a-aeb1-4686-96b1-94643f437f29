import { DbService } from '@/db/db.service';
import type {
	BuilderTaskType,
	CommonBuilderTaskType,
	SchemaFilterType
} from '@gaio/shared/types';
import { statsQuery } from './info-helpers/stats.query';
import { getFilters } from '@/utils/table-where-extractor';

export const tableCount = async (
	taskData: CommonBuilderTaskType,
	filters: SchemaFilterType[]
) => {
	const db = await new DbService().connect(taskData);

	const filter: string = await getFilters(taskData as BuilderTaskType, filters);
	const stats = statsQuery;

	let query = stats.count[taskData.client]
		.replace(/@db/g, taskData.databaseName)
		.replace(/@schema/g, taskData.schemaName)
		.replace(/@tb/g, taskData.tableName)
		.replace(/@filter/g, filter);

	if (taskData.countDistinct) {
		query = query.replace(/@cl/g, taskData.columnName);
	} else {
		query = query
			.replace(', count(distinct(@cl)) as countDistinct', '')
			.replace(', count(distinct("@cl")) as "countDistinct"', '')
			.replace(', count(distinct("@cl")) "countDistinct"', '');
	}

	try {
		const res = await db.query<{ qtd: string; countDistinct: unknown }>(query);
		let totalRows = 0;
		let totalRowsDistinct = 0;

		if (res.data && res.data[0]) {
			totalRows = Number(res.data[0].qtd);
			totalRowsDistinct = res.data[0].countDistinct as number;
		}

		return { totalRows, totalRowsDistinct };
	} catch (err) {
		return {
			error: true,
			message: (err as Error).message,
			query
		};
	}
};
