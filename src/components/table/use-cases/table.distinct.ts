import { DbService } from '@/db/db.service';
import type {
	CommonBuilderTaskType,
	BuilderTaskType,
	SchemaFilterType
} from '@gaio/shared/types';
import { statsQuery } from './info-helpers/stats.query';
import { getFilters } from '@/utils/table-where-extractor';

export const tableDistinct = async (
	taskData: CommonBuilderTaskType,
	filters?: SchemaFilterType[]
) => {
	const db = await new DbService().connect(taskData);
	const filter: string = await getFilters(taskData as BuilderTaskType, filters);

	const query = statsQuery.countDistinct[taskData.client]
		.replace(/@db/g, taskData.databaseName)
		.replace(/@schema/g, taskData.schemaName)
		.replace(/@tb/g, taskData.tableName)
		.replace(/@filter/g, filter)
		.replace(/@cl/g, taskData.columnName);

	try {
		const res = await db.query(query);
		let totalDistinctRows = 0;
		let totalRows = 0;

		if (res.data && res.data[0]) {
			totalDistinctRows = Number(res.data[0].countDistinct || 0);
			totalRows = Number(res.data[0].qtd);
		} else if (res && res[0]) {
			totalDistinctRows = Number(res[0].countDistinct || 0);
			totalRows = Number(res[0].qtd);
		}

		return { totalDistinctRows, totalRows };
	} catch (err) {
		return {
			error: true,
			message: (err as Error).message,
			query
		};
	}
};
