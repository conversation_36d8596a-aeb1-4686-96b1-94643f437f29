import repoRepository from '@/components/repo/repo.repository';
import type { BuilderTaskType, TaskContextType } from '@gaio/shared/types';
import { prepareQuery } from './table.report';
import type { Context } from 'hono';
import { dbClickhouseConnection } from '@/db/connections/db.clickhouse';
import { transformTemporaryName } from '@/utils/helpers';

export const tableDownload = async (
	taskData: BuilderTaskType,
	taskContext: TaskContextType,
	c: Context
) => {
	if (taskData.settings.downloadFullTable) taskData.schema.limit = null;
	else taskData.schema.limit = taskData.settings.downloadRows;

	const repoData = await repoRepository.getRepoCredentials(taskData);
	const query = await prepareQuery(taskData, taskContext);

	// Create direct ClickHouse connection for CSV streaming
	const connection = dbClickhouseConnection(repoData, {
		log_comment: 'CSV Download from Gaio'
	});

	const result = await connection.query({
		query: transformTemporaryName(query, taskContext, false),
		format: 'CSVWithNames'
	});

	// Get the stream from the result
	const clickhouseStream = result.stream();

	// Convert ClickHouse StreamReadable to web standard ReadableStream
	const webStream = new ReadableStream({
		async start(controller) {
			try {
				for await (const chunk of clickhouseStream) {
					// ClickHouse chunks are arrays of objects with 'text' property
					if (Array.isArray(chunk)) {
						// Process each row in the chunk array
						for (const row of chunk) {
							if (row && typeof row === 'object' && 'text' in row) {
								const textRow = row as { text: string };
								controller.enqueue(`${textRow.text}\n`);
							}
						}
					} else if (chunk && typeof chunk === 'object' && 'text' in chunk) {
						controller.enqueue((chunk as { text: string }).text);
					} else if (typeof chunk === 'string') {
						controller.enqueue(chunk);
					} else {
						// Fallback
						controller.enqueue(String(chunk));
					}
				}
				controller.close();
			} catch (error) {
				controller.error(error);
			}
		}
	});

	// Set appropriate headers for CSV download
	c.header('Content-Type', 'text/csv');
	c.header('Content-Disposition', 'attachment; filename="data.csv"');
	return c.body(webStream);
};
