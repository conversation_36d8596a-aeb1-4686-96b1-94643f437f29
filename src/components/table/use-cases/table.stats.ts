import { DbService } from '@/db/db.service';
import { getFilters } from '@/utils/table-where-extractor';
import type {
	BuilderTaskType,
	CommonBuilderTaskType,
	SchemaFilterType
} from '@gaio/shared/types';
import { statsQuery } from './info-helpers/stats.query';

export const tableStats = async (
	taskData: CommonBuilderTaskType,
	filters: SchemaFilterType[]
) => {
	const db = await new DbService().connect(taskData);

	const filter: string = await getFilters(taskData as BuilderTaskType, filters);

	const query = statsQuery.statistics[taskData.client]
		.replace(/@db/g, taskData.databaseName)
		.replace(/@schema/g, taskData.schemaName)
		.replace(/@tb/g, taskData.tableName)
		.replace(/@cl/g, taskData.columnName)
		.replace(/@filter/g, filter);

	return db.query(query).catch((err: Error) => {
		return {
			error: true,
			message: err.message,
			query
		};
	});
};
