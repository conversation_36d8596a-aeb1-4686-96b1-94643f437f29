import { DbService } from '@/db/db.service';
import type { TaskType } from '@gaio/shared/types';
import { HTTPException } from 'hono/http-exception';
import tableSchema from './info-helpers/table.schema';

export async function tableList(taskData: TaskType) {
	try {
		const db = await new DbService().connect(taskData);
		const query = tableSchema(taskData);

		return db.query(query).then((res) => {
			if (!Array.isArray(res.data)) {
				return res;
			}

			res.data.map((item) => {
				item.sourceId = taskData.sourceId;
				item.sourceType = taskData.sourceType || 'bucket';

				if (item.sourceType === 'bucket') {
					const appNumberId = taskData.appId.split(':')[1];
					const bucketSourceAppId = item.databaseName.split('_')[1];

					if (appNumberId !== bucketSourceAppId) {
						item.shared = true;
					}
				}

				if (taskData.client === 'snowflake') {
					item = {
						databaseName: item.database_name,
						schemaName: item.schema_name,
						tableName: item.name,
						sourceType: 'source',
						client: 'snowflake',
						type: 'table',
						appId: taskData.appId,
						repoId: taskData.repoId
					};
				} else if (taskData.client === 'redshift') {
					item.databaseName =
						item.databasename || item.databasename?.toUpperCase();
					item.schemaName = item.schemaName || item.schemaName?.toUpperCase();
					item.tableName = item.tablename || item.tablename?.toUpperCase();

					delete item.databasename;
					delete item.schemaName;
					delete item.tablename;
				} else if (taskData.client === 'databricks') {
					item.databaseName = item.database;
					item.schemaName = taskData.schemaName;
					item.sourceType = 'source';
					item.client = 'databricks';
					item.type = 'table';

					delete item.database;
					delete item.isTemporary;
				}

				return item;
			});

			return res;
		});
	} catch (err) {
		throw new HTTPException(500, {
			cause: err,
			message: err.message
		});
	}
}
