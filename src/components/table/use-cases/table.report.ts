import { QueryBuilder } from '@/components/builder/use-cases/builder';
import { DbService } from '@/db/db.service';
import type {
	BuilderTaskType,
	FieldType,
	GenericType,
	TaskContextType,
	TaskType
} from '@gaio/shared/types';
import { tableRows } from './table.rows';

const groupRules = (o: FieldType) => {
	return (
		(!o.type || o.type === 'value' || o.type === 'none') && o.type !== 'measure'
	);
};

type StatisticsType = {
	elapsed: number;
	rows_read: number;
	bytes_read: number;
};

type ApiResponse = {
	data: GenericType[];
	rows: number;
	rows_before_limit_at_least: number;
	success: boolean;
	statistics: StatisticsType;
	ref: string;
};

export async function prepareQuery(
	taskData: BuilderTaskType,
	taskContext: TaskContextType
) {
	let query = '';

	if (taskData.query) {
		query = taskData.query;
	} else if (taskData.schema) {
		const hasAggregation = taskData.schema.select.some(
			(o) => !['none', 'value'].includes(o.type)
		);

		if (hasAggregation) {
			taskData.schema.group = taskData.schema.select.filter(groupRules);
		}

		taskData.schema.sort = taskData.schema.sort || [];

		if (taskData.schema.sort.length === 0) {
			taskData.schema.sort = taskData.schema.select.filter(
				(o) => o.type === 'value' || o.type === 'none'
			);
		}

		if (!taskData.schema.limit && !taskData.settings.downloadFullTable) {
			taskData.schema.limit = 50;
		}

		query = await new QueryBuilder().generate(
			taskData as TaskType,
			taskContext.params
		);
	}

	return query.replace(/`/g, '');
}

export const tableReport = async (
	taskData: BuilderTaskType,
	taskContext: TaskContextType
) => {
	const db = await new DbService().connect(taskData, taskContext);
	const query = await prepareQuery(taskData, taskContext);

	if (taskData.reportType === 'table') {
		return tableRows(taskData, taskContext);
	}

	return await db
		.query<ApiResponse>(query)
		.then((o) => {
			return { ...o, ref: query };
		})
		.catch((err: Error) => {
			return {
				error: true,
				message: err.message,
				query
			};
		});
};
