import { QueryBuilder } from '@/components/builder/use-cases/builder';
import { DbService } from '@/db/db.service';
import type { CommonBuilderTaskType } from '@gaio/shared/types';

export const tableHistogram = async (taskData: CommonBuilderTaskType) => {
	let fromThis = taskData.tableName;
	const range = taskData.range;

	const baseTicks = ['mysql', 'mariadb', 'memsql'].includes(taskData.client) ? '`' : '"';

	if (taskData.client === 'clickhouse' && taskData.hasFilters) {
		fromThis = `(${await new QueryBuilder().generate(taskData, [])})`;
	}

	const bColumnName = [...new Array(taskData.bins)].map((o, i) => `b${i + 1}`);

	const countB = bColumnName.map((o, i) => {
		if (i <= 0) {
			return `count(
                            case
                            when ${taskData.columnName} >= f2.${baseTicks}minValue${baseTicks} and  ${taskData.columnName} <  f2.${o}
                                then 1
                            end
                        ) as q${i + 1}`;
		}
		return `count(
                            case
                            when ${taskData.columnName} >= f2.b${i} and  ${taskData.columnName} < f2.${o}
                            then 1
                            end
                        ) as q${i + 1}`;
	});

	const crossJoinList = bColumnName.map((o, i) => {
		if (i <= 0) {
			return `min(${taskData.columnName}) + (max(${taskData.columnName}) - min(${taskData.columnName})) / ${taskData.bins} as b1`;
		}

		if (i === taskData.bins - 1) {
			return `max(${taskData.columnName}) as ${o}`;
		}

		return `min(${taskData.columnName}) + ${i + 1} * (max(${taskData.columnName}) - min(${
			taskData.columnName
		})) / ${taskData.bins} as ${o}`;
	});

	let defineRange = '';

	if (taskData.range && taskData.range.length === 2) {
		defineRange = `where ${taskData.columnName} between ${range[0]} and ${range[1]}`;
	}
	const db = await new DbService().connect(taskData);

	const query = `
                    select
											f2.${baseTicks}minValue${baseTicks} AS min,
    									f2.${baseTicks}maxValue${baseTicks} AS max,
    									f2.${baseTicks}binRange${baseTicks},
                      ${bColumnName.join(',')},
                      ${countB.join(',')}
                    from
                      ${fromThis} f1
                      cross join (
                        select
                          1 as ${baseTicks}key${baseTicks},
                          min(${taskData.columnName}) as ${baseTicks}minValue${baseTicks},
                          max(${taskData.columnName}) as ${baseTicks}maxValue${baseTicks},
                          (max(${taskData.columnName}) - min(${taskData.columnName})) as ${baseTicks}range${baseTicks},
                          (max(${taskData.columnName}) - min(${taskData.columnName})) / ${taskData.bins} as ${baseTicks}binRange${baseTicks},
                          ${crossJoinList.join(',')}
                        from
                          ${fromThis}
                            ${defineRange} 
                      ) as f2
                    group by
    									f2.${baseTicks}minValue${baseTicks},
    									f2.${baseTicks}maxValue${baseTicks},
    									f2.${baseTicks}binRange${baseTicks},
                      ${bColumnName.join(',')}`;

	const result = await db.query(query).catch((err: Error) => {
		return {
			error: true,
			message: err.message,
			query
		};
	});

	return {
		...result
	};
};
