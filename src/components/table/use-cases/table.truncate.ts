import { dbGaio } from '@/db/db.gaio';
import type { DeleteTaskType } from '@gaio/shared/types';

export async function tableTruncate(taskData: DeleteTaskType) {
	const db = dbGaio();

	if (taskData.tableName === 'tmp_dashboard') {
		const listDashTemps = await db.query(
			`SELECT
				name, engine
			FROM
				system.tables
			WHERE database = '${taskData.databaseName}'
				AND
					startsWith(name,'tmp_gaio')`
		);
		for (const table of listDashTemps) {
			await db.query(
				`TRUNCATE TABLE IF EXISTS ${taskData.databaseName}.${table.name}`
			);
			await db.query(
				`TRUNCATE TEMPORARY TABLE IF EXISTS ${taskData.databaseName}.${taskData.tableName}`
			);
		}
	} else {
		await db.query(
			`TRUNCATE TABLE IF EXISTS ${taskData.databaseName}.${taskData.tableName}`
		);
		await db.query(
			`TRUNCATE TEMPORARY TABLE IF EXISTS ${taskData.databaseName}.${taskData.tableName}`
		);
	}

	return { status: 'done' };
}
