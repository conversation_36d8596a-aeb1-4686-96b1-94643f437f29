import SourceRepository from '@/components/source/source.repository';
import { TemplateService } from '@/utils/template.service';
import type { CredentialsType } from '@gaio/shared/types';
import type { ParamType } from '@gaio/shared/types';
import { $ } from 'bun';
import { HTTPException } from 'hono/http-exception';

export async function tablePreviewSqlQuery({
	params,
	query,
	client,
	sourceId
}: {
	params: ParamType[];
	query: string;
	client: string;
	sourceId: string;
}) {
	let sql: string;

	if (client === 'clickhouse') {
		sql = `SELECT *
					 FROM (${query}) AS a LIMIT 100  FORMAT JSON`;
	} else if (client === 'oracle') {
		sql = `SELECT *
					 FROM (${query})
					 WHERE rownum <= 100`;
	} else if (client === 'mssql') {
		sql = `SELECT top 100 *
					 FROM (${query}) AS a`;
	} else {
		sql = `SELECT *
					 FROM (${query}) AS a LIMIT 100`;
	}

	const sourceCredentials =
		await SourceRepository.getSourceCredentials(sourceId);

	sql = new TemplateService().render(sql, params);

	const command = connectionString({
		client,
		credentials: sourceCredentials,
		sql
	});

	const shellCommand = await $`${command}`.catch((err) => {
		if (err.stderr) {
			throw new HTTPException(400, {
				cause: 'stderrPreviewingSqlQuery',
				message: err.stderr
			});
		}

		throw new HTTPException(500, {
			cause: 'errorRunningShellCommandPreviewingSqlQuery',
			message: err
		});
	});

	return shellCommand.json();
}

function connectionString({
	client,
	credentials,
	sql
}: { client: string; sql: string; credentials: CredentialsType }) {
	const {
		host,
		port,
		user,
		password,
		database,
		encryptSource,
		schemaName,
		sid,
		serviceName,
		oracleAlternativeDriver,
		secure,
		tcpPort,
		account,
		databricksConfigFile
	} = credentials;

	const miniQuery = `${sql}`
		.replace(/--.*\n/g, '')
		.replace(/\/\*[\s\S]*?\*\//g, '')
		.replace(' =', '=')
		.replace('= ', '=')
		.replace('( ', '(')
		.replace(' )', ')')
		.replace('\r', ' ')
		.replace('\\n', ' ')
		.replace('\\r', ' ')
		.replace('\n', ' ')
		.replace(/`/g, '')
		.replace(/ +/g, ' ')
		.replace(/^ /g, '')
		.replace(/\s+/g, ' ')
		.trim();

	const dbPort =
		client === 'clickhouse'
			? tcpPort
				? tcpPort
				: '9000'
			: client === 'snowflake'
				? 443
				: port;

	const conn = `${user}:${encodeURIComponent(password)}@${host}:${Number(dbPort)}`;

	if (client === 'clickhouse') {
		return [
			'usql',
			'--json',
			'-q',
			`ch://${conn}/${database}${secure ? '?secure=true' : ''}`,
			'-w',
			'--command',
			miniQuery,
			'--set',
			'SHOW_HOST_INFORMATION=false'
		];
	}

	if (
		client === 'mysql' ||
		client === 'memsql' ||
		client === 'aurora' ||
		client === 'mariadb'
	) {
		return [
			'usql',
			'--json',
			'-q',
			`my://${conn}/${database}`,
			'-w',
			'--command',
			miniQuery,
			'--set',
			'SHOW_HOST_INFORMATION=false'
		];
	}

	if (client === 'mssql') {
		return [
			'usql',
			'--json',
			'-q',
			`mssql://${conn}/${database}${!encryptSource ? '' : '?encrypt=true'}`,
			'-w',
			'--command',
			miniQuery,
			'--set',
			'SHOW_HOST_INFORMATION=false'
		];
	}

	if (client === 'pg') {
		return [
			'usql',
			'--json',
			'-q',
			`pg://${conn}/${database}?sslmode=disable`,
			'-w',
			'--command',
			miniQuery,
			'--set',
			'SHOW_HOST_INFORMATION=false'
		];
	}

	if (client === 'redshift') {
		return [
			'usql',
			'--json',
			'-q',
			`postgres://${conn}/${database}?sslmode=disable`,
			'-w',
			'--command',
			miniQuery,
			'--set',
			'SHOW_HOST_INFORMATION=false'
		];
	}

	if (client === 'snowflake') {
		return [
			'usql',
			'--json',
			'-q',
			`snowflake://${conn}/${database}/${schemaName}?account=${account}`,
			'-w',
			'--command',
			miniQuery,
			'--set',
			'SHOW_HOST_INFORMATION=false'
		];
	}

	if (client === 'oracle') {
		if (oracleAlternativeDriver) {
			return [
				'usql',
				'--json',
				'-q',
				`gr://${conn}/${sid || serviceName}`,
				'-w',
				'--command',
				miniQuery,
				'--set',
				'SHOW_HOST_INFORMATION=false'
			];
		}

		return [
			'usql',
			'--json',
			'-q',
			`or://${conn}/${sid || serviceName}`,
			'-w',
			'--command',
			miniQuery,
			'--set',
			'SHOW_HOST_INFORMATION=false'
		];
	}

	if (client === 'databricks') {
		return [
			'usql',
			'--config',
			databricksConfigFile,
			'run',
			'--json',
			'-q',
			'-w',
			'--command',
			miniQuery,
			'--set',
			'SHOW_HOST_INFORMATION=false'
		];
	}

	return '';
}
