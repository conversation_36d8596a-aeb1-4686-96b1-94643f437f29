import { QueryBuilder } from '@/components/builder/use-cases/builder';
import { DbService } from '@/db/db.service';
import type { BuilderTaskType, TaskContextType } from '@gaio/shared/types';
import { statsQuery } from './info-helpers/stats.query';

export const tableRows = async (
	taskData: BuilderTaskType,
	taskContext: TaskContextType
) => {
	const db = await new DbService().connect(taskData, taskContext);

	const query = await new QueryBuilder().generate(taskData, taskContext.params);
	const countQuery = await new QueryBuilder().generate(
		{
			...taskData,
			schema: {
				...taskData.schema,
				limit: null,
				offset: null
			}
		},
		taskContext.params
	);

	const [resultCount, resultQuery] = await Promise.all([
		db
			.query(
				statsQuery.subqueryCount[taskData.client].replace(/@query/g, countQuery)
			)
			.then((o) => {
				return { ...o, ref: countQuery };
			}),
		db
			.query(query.replace(/`/g, ''))
			.then((o) => {
				return { ...o, ref: query };
			})
			.catch((err: Error) => {
				return {
					error: true,
					message: err.message,
					query
				};
			})
	]);

	return {
		...resultQuery,
		rows_before_limit_at_least: resultCount.data[0]?.qtd
	};
};
