import { dbGaio } from '@/db/db.gaio';
import { DbService } from '@/db/db.service';
import type {
	DeleteTaskType,
	ReportNodeType,
	TaskContextType
} from '@gaio/shared/types';

export async function tableDrop(taskData: DeleteTaskType) {
	const db = dbGaio();

	if (taskData.tableName === 'tmp_dashboard') {
		const listDashTemps = await db.query(
			`SELECT
        name, engine
      FROM
        system.tables
      WHERE database = '${taskData.databaseName}'
        AND
          startsWith(name,'tmp_gaio')`
		);
		for (const table of listDashTemps) {
			await db.query(
				`DROP TABLE IF EXISTS ${taskData.databaseName}.${table.name}`
			);
			await db.query(
				`DROP TEMPORARY TABLE IF EXISTS ${taskData.databaseName}.${taskData.tableName}`
			);
		}
	} else {
		await db.query(
			`DROP TABLE IF EXISTS ${taskData.databaseName}.${taskData.tableName}`
		);
		await db.query(
			`DROP TEMPORARY TABLE IF EXISTS ${taskData.databaseName}.${taskData.tableName}`
		);
	}

	return { status: 'done' };
}

export async function tableDropView(
	taskData: ReportNodeType,
	taskContext: TaskContextType
) {
	const db = await new DbService().connect(taskData, taskContext);

	await db.query(
		`DROP TABLE IF EXISTS ${taskData.databaseName}.g_ai_${taskData.sharedId}`
	);
}
