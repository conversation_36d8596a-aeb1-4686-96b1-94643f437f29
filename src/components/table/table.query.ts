import { QueryBuilder } from '@/components/builder/use-cases/builder';
import { DbService } from '@/db/db.service';
import type { GenericType, TaskType } from '@gaio/shared/types';

const miniQueryBuilder = (schema: GenericType) => {
	if (!schema.from) {
		throw new Error('FROM clause is required. Use .from() to specify a table.');
	}

	let sql = `SELECT ${schema.select.join(', ')} FROM ${schema.from}`;

	// Add filter if exists
	if (!schema.filter) {
	} else {
		const { field, operator, value } = schema.filter;
		const valueStr = typeof value === 'string' ? `'${value}'` : value;

		// Handle special operators
		switch (operator) {
			case 'like':
			case 'ilike':
				sql += ` WHERE ${field} ${operator} '${value}'`;
				break;
			case 'in':
				sql += ` WHERE ${field} IN (${Array.isArray(value) ? value.join(', ') : value})`;
				break;
			case 'is':
				sql += ` WHERE ${field} IS ${value ? 'NOT NULL' : 'NULL'}`;
				break;
			default: {
				const opMap: Record<string, string> = {
					eq: '=',
					neq: '!=',
					gt: '>',
					gte: '>=',
					lt: '<',
					lte: '<='
				};
				sql += ` WHERE ${field} ${opMap[operator] || operator} ${valueStr}`;

				break;
			}
		}
	}

	// Add group by
	if (schema.groupBy && schema.groupBy.length > 0) {
		sql += ` GROUP BY ${schema.groupBy.join(', ')}`;
	}

	// Add order by
	if (schema.orderBy && schema.orderBy.length > 0) {
		sql += ` ORDER BY ${schema.orderBy
			.map((order) => {
				let orderStr = `${order.field} ${order.direction.toUpperCase()}`;
				if (order.nullsPosition) {
					orderStr += ` NULLS ${order.nullsPosition.toUpperCase()}`;
				}
				return orderStr;
			})
			.join(', ')}`;
	}

	// Add limit and offset
	if (schema.limit !== undefined) {
		sql += ` LIMIT ${schema.limit}`;
	}

	if (schema.offset !== undefined) {
		sql += ` OFFSET ${schema.offset}`;
	}

	return sql;
};

export const tableQuery = async (taskData: TaskType, schema: GenericType) => {
	const db = await new DbService().connect(taskData);
	const query = miniQueryBuilder(schema);

	return db.query(query.replace(/`/g, '')).catch((err: Error) => {
		return {
			error: true,
			message: err.message,
			query
		};
	});
};
