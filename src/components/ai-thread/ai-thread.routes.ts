import AiThreadRepository from '@/components/ai-thread/ai-thread.repository';
import jwtGuard from '@/server/middleware/jwt.guard';
import { readBody } from '@/server/middleware/readBody';
import type { AiMessageType, TalkData, UserType } from '@gaio/shared/types';
import { type Context, Hono } from 'hono';
import { deleteThread, renameThread } from './ai-thread.service';

const app = new Hono()
	.get('/list', jwtGuard().isAuth, async (c: Context) => {
		const { appId } = c.req.query();
		const user = c.get('user');

		if (appId) {
			return c.json(await AiThreadRepository.listAiThread(appId, user.userId));
		}

		const threadDataList = await AiThreadRepository.listAllThreads(user.userId);

		return c.json(
			threadDataList.map((thread) => ({
				...thread,
				options: {
					...thread.options,
					messages:
						(thread.options?.messages || []).filter(
							(msg: AiMessageType) => msg.isChat
						) || []
				}
			}))
		);
	})

	.get('/messages/:threadId', jwtGuard().isAuth, async (c) => {
		const { threadId } = c.req.param();

		const threadData = await AiThreadRepository.getThreadMessages(threadId);

		return c.json({
			...threadData,
			messages: threadData?.messages.filter((msg: AiMessageType) => msg.isChat)
		});
	})

	.post('/save', jwtGuard().isAuth, async (c) => {
		const { threadId, talkData, user } = await readBody<{
			threadId: string;
			talkData: TalkData;
			user: UserType;
		}>(c);

		await AiThreadRepository.upsertAiThread(threadId, talkData, user.userId);

		return c.json({ success: true });
	})

	.delete('/:threadId', jwtGuard().isAuth, async (c) => {
		const { threadId } = c.req.param();

		await deleteThread(threadId);

		return c.json({ success: true });
	})

	.post('/rename/:threadId', jwtGuard().isAuth, async (c) => {
		const { threadId } = c.req.param();

		const { threadName } = await readBody<{
			threadName: string;
		}>(c);

		await renameThread(threadId, threadName);

		return c.json({ success: true });
	});

export default app;
