import { dbGaio } from '@/db/db.gaio';
import { currentDatetime } from '@/utils/helpers';
import type { GenericType } from '@gaio/shared/types';
import { getId } from '@gaio/shared/utils';

async function upsertAiThread(
	threadId: string,
	talkData: GenericType,
	userId: string
) {
	talkData.messages = talkData.messages.map((ms: GenericType) => {
		return {
			messageId: ms.messageId || getId(),
			...ms
		};
	});

	const savedThread = await getThreadMessages(threadId);

	if (savedThread) {
		const newMessages = talkData.messages.filter((ms: GenericType) => {
			return !savedThread.messages.some(
				(sm: GenericType) => sm.messageId === ms.messageId
			);
		});

		talkData.messages = [...(savedThread.messages || []), ...newMessages];
	}

	await dbGaio().updateOrInsert({
		primaryKeys: ['threadId'],
		table: 'ai_thread',
		values: {
			threadId,
			appId: talkData.appId,
			options: JSON.stringify(talkData),
			messages: JSON.stringify(talkData.messages || []),
			createdBy: userId,
			updatedBy: userId,
			updatedAt: currentDatetime().replace('T', ' ').replace('Z', ''),
			threadName: talkData.threadName
		}
	});
}

async function listAllThreads(userId: string) {
	return await dbGaio('list-ai-thread').query<GenericType>(
		`SELECT distinct threadId, threadName, createdAt, updatedAt, appId, options
				FROM ai_thread
				INNER JOIN tag ON tag.refId = ai_thread.appId
			WHERE createdBy = {userId: String}`,
		{
			params: {
				userId
			},
			parse: ['options']
		}
	);
}

async function listAiThread(appId: string, userId: string) {
	return await dbGaio('list-ai-thread').query<GenericType>(
		`SELECT distinct threadId, threadName, createdAt, updatedAt, appId, options
				FROM ai_thread
				INNER JOIN tag ON tag.refId = ai_thread.appId
			WHERE appId = {appId: String} AND createdBy = {userId: String}`,
		{
			params: {
				appId,
				userId
			},
			parse: ['options']
		}
	);
}

async function getThreadMessages(threadId: string) {
	return await dbGaio('get-thread-messages')
		.query<GenericType>(
			`SELECT messages, threadId, options
				FROM ai_thread
			WHERE threadId = {threadId: String}`,
			{
				params: {
					threadId
				},
				parse: ['messages', 'options']
			}
		)
		.then((res) => {
			if (res[0]) {
				return {
					...res[0],
					messages: res[0].messages
				};
			}

			return null;
		})
		.catch((_err) => {
			return null;
		});
}

async function deleteAiThread(threadId: string) {
	await dbGaio().query(
		'ALTER TABLE ai_thread DELETE WHERE threadId = {threadId: String}',
		{
			params: {
				threadId
			}
		}
	);
}

async function deleteAiThreadByUserId(userId: string) {
	await dbGaio().query(
		'ALTER TABLE ai_thread DELETE WHERE createdBy = {userId: String}',
		{
			params: {
				userId
			}
		}
	);
}

async function renameAiThreadName(threadId: string, threadName: string) {
	await dbGaio().query(
		'ALTER TABLE ai_thread UPDATE threadName = {threadName: String} WHERE threadId = {threadId: String}',
		{
			params: {
				threadId,
				threadName
			}
		}
	);
}

async function deleteAiThreadByAppId(appId: string) {
	await dbGaio().query(
		'ALTER TABLE ai_thread DELETE WHERE appId = {appId: String}',
		{
			params: {
				appId
			}
		}
	);
}

export default {
	upsertAiThread,
	listAllThreads,
	listAiThread,
	getThreadMessages,
	deleteAiThread,
	deleteAiThreadByAppId,
	renameAiThreadName,
	deleteAiThreadByUserId
};
