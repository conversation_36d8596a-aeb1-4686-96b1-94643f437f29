import { getAppNumberReference } from '@gaio/shared/utils';
import type { Context } from 'hono';
import { join } from 'node:path';
import { existsSync } from 'node:fs';
import { appFilesFolderPath } from '../content/use-cases/content.core';
import { deburr, camelCase } from 'lodash';

export async function readBackup(c: Context) {
	const appIdNumberReference = getAppNumberReference(c.req.param('appId'));
	const appExportFolder = join(
		appFilesFolderPath,
		appIdNumberReference,
		'backup-import'
	);

	const files = [
		// to flow table and related
		'flows.json',
		'code_snippets.json',
		'cron_flow.json',

		// to apps table
		'params.json',
		'forms.json',

		// to meta table and related
		'meta.json',
		'cron_meta.json',

		// to api and api key tables
		'api.json',
		'api_key.json',

		// to map table
		'map.json',

		// to source table
		'sources.json'
	];

	const result: Record<string, unknown> = {};

	for (const file of files) {
		const filePath = join(appExportFolder, file);
		const key = camelCase(deburr(file.replace('.json', '')));

		if (existsSync(filePath)) {
			try {
				const content = await Bun.file(filePath).json();
				result[key] = content;
			} catch (error) {
				console.error(`Error reading file ${file}:`, error);
				result[key] = [];
			}
		} else {
			// If file doesn't exist, add an empty array
			result[key] = [];
		}
	}

	return result;
}
