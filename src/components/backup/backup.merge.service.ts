import { join } from 'node:path';
import { dbGaio } from '@/db/db.gaio';
import type {
	ApiKeyType,
	ApiType,
	AppImportResult,
	AppType,
	CodeSnippetsEntity,
	FlowEntity,
	FlowType,
	FormType,
	MapType,
	MetaType,
	ParamType
} from '@gaio/shared/types';
import { getAppNumberReference } from '@gaio/shared/utils';
import { copy } from 'fs-extra';
import type { CodeSnippetEntity } from '../code-snippet/code-snippet.entity';
import { appFilesFolderPath } from '../content/use-cases/content.core';
import type { CronEntity } from '../cron/cron.entity';
import type { MetaEntity } from '../meta/meta.entity';
import { contentFolder } from '../task/use-cases/runners/runner.tools';

export async function mergeBackup(backupData: AppImportResult, appId: string) {
	await mergeFlows(backupData, appId);
	await mergeCronFlow(backupData, appId);
	await mergeCodeSnippets(backupData, appId);
	await mergeMeta(backupData, appId);
	await mergeCronMeta(backupData, appId);
	await mergeMap(backupData, appId);
	await mergeApi(backupData, appId);
	await mergeApiKey(backupData, appId);
	await mergeAppData(backupData, appId);

	return {
		success: true,
		message: 'Backup merged successfully'
	};
}

async function mergeFlows(backupData: AppImportResult, appId: string) {
	const { flows } = backupData;

	const flowValues = flows.create.concat(flows.replace) as FlowType[];

	if (flowValues.length === 0) {
		return;
	}

	await dbGaio().query(
		'ALTER TABLE flow DELETE WHERE appId = {appId: String} AND flowId IN ({flowIds: Array(String)})',
		{
			params: {
				appId: appId,
				flowIds: flowValues.map((f) => f.flowId)
			}
		}
	);

	await dbGaio().insert('flow', flowValues as unknown as FlowEntity[], {
		stringify: ['workflow', 'options']
	});
}

async function mergeCronFlow(backupData: AppImportResult, appId: string) {
	const { cronFlow } = backupData;

	const cronFlowValues = cronFlow.create.concat(
		cronFlow.replace
	) as CronEntity[];

	if (cronFlowValues.length === 0) {
		return;
	}

	await dbGaio().query(
		'ALTER TABLE cron DELETE WHERE appId = {appId: String} AND refId IN {cronIds: Array(String)}',
		{
			params: {
				appId: appId,
				cronIds: cronFlowValues.map((f) => f.refId)
			}
		}
	);

	await dbGaio().insert('cron', cronFlowValues as unknown as CronEntity[], {
		stringify: ['cronBase']
	});
}

async function mergeCodeSnippets(backupData: AppImportResult, appId: string) {
	const { codeSnippets } = backupData;

	const codeSnippetsValues = codeSnippets.create.concat(
		codeSnippets.replace
	) as CodeSnippetsEntity[];

	if (codeSnippetsValues.length === 0) {
		return;
	}

	await dbGaio().query(
		'ALTER TABLE code_snippet DELETE WHERE appId = {appId: String} AND codeSnippetId IN {codeSnippetIds: Array(String)}',
		{
			params: {
				appId: appId,
				codeSnippetIds: codeSnippetsValues.map((f) => f.codeSnippetId)
			}
		}
	);

	await dbGaio().insert(
		'code_snippet',
		codeSnippetsValues as unknown as CodeSnippetEntity[],
		{
			stringify: ['options']
		}
	);
}

async function mergeMeta(backupData: AppImportResult, appId: string) {
	const { meta } = backupData;

	const metaValues = meta.create.concat(meta.replace) as MetaType[];

	if (metaValues.length === 0) {
		return;
	}

	await dbGaio().query(
		'ALTER TABLE meta DELETE WHERE appId = {appId: String} AND tableName IN {tableNames: Array(String)}',
		{
			params: {
				appId: appId,
				tableNames: metaValues.map((f) => f.tableName)
			}
		}
	);

	await dbGaio().insert('meta', metaValues as unknown as MetaEntity[], {
		stringify: ['fields', 'options']
	});
}

async function mergeCronMeta(backupData: AppImportResult, appId: string) {
	const { cronMeta } = backupData;

	const cronMetaValues = cronMeta.create.concat(
		cronMeta.replace
	) as CronEntity[];

	if (cronMetaValues.length === 0) {
		return;
	}

	await dbGaio().query(
		'ALTER TABLE cron DELETE WHERE appId = {appId: String} AND refId IN {refIds: Array(String)}',
		{
			params: {
				appId: appId,
				refIds: cronMetaValues.map((f) => f.refId)
			}
		}
	);

	await dbGaio().insert('cron', cronMetaValues as unknown as CronEntity[], {
		stringify: ['cronBase']
	});
}

async function mergeMap(backupData: AppImportResult, appId: string) {
	const { map } = backupData;

	const mapValues = map.create.concat(map.replace) as MapType[];

	if (mapValues.length === 0) {
		return;
	}

	await dbGaio().query(
		'ALTER TABLE map DELETE WHERE appId = {appId: String} AND mapId IN {mapIds: Array(String)}',
		{
			params: {
				appId: appId,
				mapIds: mapValues.map((f) => f.mapId)
			}
		}
	);

	await dbGaio().insert('map', mapValues as unknown as MapType[], {
		stringify: ['options']
	});

	const appIdNumberReference = getAppNumberReference(appId);
	const appExportFolder = join(
		appFilesFolderPath,
		appIdNumberReference,
		'backup-import',
		'geojson'
	);

	const targetFolder = join(contentFolder, 'maps');

	await copy(appExportFolder, targetFolder);
}

async function mergeApi(backupData: AppImportResult, appId: string) {
	const { api } = backupData;

	const apiValues = api.create.concat(api.replace) as ApiType[];

	if (apiValues.length === 0) {
		return;
	}

	await dbGaio().query(
		'ALTER TABLE api DELETE WHERE appId = {appId: String} AND apiId IN {apiIds: Array(String)}',
		{
			params: {
				appId: appId,
				apiIds: apiValues.map((f) => f.apiId)
			}
		}
	);

	await dbGaio().insert('api', apiValues as unknown as ApiType[], {
		stringify: ['options', 'assignedKeys']
	});
}

async function mergeApiKey(backupData: AppImportResult, appId: string) {
	const { apiKey } = backupData;

	const apiKeyValues = apiKey.create.concat(apiKey.replace) as ApiKeyType[];

	if (apiKeyValues.length === 0) {
		return;
	}

	await dbGaio().query(
		'ALTER TABLE api_key DELETE WHERE appId = {appId: String} AND apiKeyId IN {apiKeyIds: Array(String)}',
		{
			params: {
				appId: appId,
				apiKeyIds: apiKeyValues.map((f) => f.apiKeyId)
			}
		}
	);

	await dbGaio().insert('api_key', apiKeyValues as unknown as ApiKeyType[]);
}

async function mergeAppData(backupData: AppImportResult, appId: string) {
	const sourceParamValues = backupData.params.create.concat(
		backupData.params.replace
	) as ParamType[];

	const sourceFormsValues = backupData.forms.create.concat(
		backupData.forms.replace
	) as FormType[];

	if (sourceParamValues.length === 0 && sourceFormsValues.length === 0) {
		return;
	}

	const appData = await dbGaio().query(
		'SELECT * FROM app WHERE appId = {appId: String} LIMIT 1',
		{
			params: {
				appId: appId
			},
			parse: ['params', 'forms']
		}
	);

	const { params, forms } = appData[0] as AppType;

	const updateAndAdd = <T extends Record<string, unknown>>(
		target: T[],
		source: T[],
		key: keyof T
	) => {
		target.forEach((item, index) => {
			const sourceItem = source.find((s) => s[key] === item[key]);
			if (sourceItem) {
				target[index] = sourceItem;
			}
		});
		source.forEach((s) => {
			if (!target.find((item) => item[key] === s[key])) {
				target.push(s);
			}
		});
	};

	updateAndAdd(params, sourceParamValues, 'paramName');
	updateAndAdd(forms, sourceFormsValues, 'formName');

	await dbGaio().query(
		'ALTER TABLE app UPDATE params = {p: String}, forms = {f: String} WHERE appId = {appId: String}',
		{
			params: {
				appId: appId,
				p: JSON.stringify(params),
				f: JSON.stringify(forms)
			}
		}
	);
}
