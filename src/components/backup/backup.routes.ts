import jwtGuard from '@/server/middleware/jwt.guard';
import { readBody } from '@/server/middleware/readBody';
import type { AppImportResult, BackupExportType } from '@gaio/shared/types';
import { Hono } from 'hono';
import { HTTPException } from 'hono/http-exception';
import { exportBackup } from './backup.export.service';
import { importBackupFile } from './backup.import.service';
import { readBackup } from './backup.read.service';
import { mergeBackup } from './backup.merge.service';

const backup = new Hono();

backup.post('/export', jwtGuard('dev').isAuth, async (c) => {
	const { backupData } = await readBody<{ backupData: BackupExportType }>(c);
	return c.json(await exportBackup(backupData), { status: 200 });
});

backup.post('/import/file/:appId', jwtGuard('dev').isAuth, async (c) => {
	try {
		const result = await importBackupFile(c);
		return c.json(result, { status: 200 });
	} catch (err) {
		if (err instanceof HTTPException) {
			return c.json(
				{
					details: err.message,
					message: err.cause,
					timestamp: new Date().toISOString()
				},
				err.status
			);
		}

		return c.json(
			{
				message: 'internalErrorUploadingFile',
				timestamp: new Date().toISOString()
			},
			{ status: 500 }
		);
	}
});

backup.post('/import/merge/:appId', jwtGuard('dev').isAuth, async (c) => {
	const { appId } = c.req.param();
	const { backupData } = await readBody<{ backupData: AppImportResult }>(c);
	return c.json(await mergeBackup(backupData, appId), { status: 200 });
});

backup.get('/import/read/:appId', jwtGuard('dev').isAuth, async (c) => {
	return c.json(await readBackup(c), { status: 200 });
});

backup.get('/import/clear/:appId', jwtGuard('dev').isAuth, async (c) => {
	return c.json({ message: 'Backup clear' }, { status: 200 });
});

export default backup;
