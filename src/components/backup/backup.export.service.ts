import { copy, ensureDirSync, remove } from 'fs-extra';
import { join } from 'node:path';

import type {
	BackupExportType,
	MetaType,
	CodeSnippetsEntity,
	FlowType,
	MapType,
	ApiType,
	ApiKeyType,
	AppType,
	SourceEntity
} from '@gaio/shared/types';
import type { CronEntity } from '../cron/cron.entity';
import {
	appFilesFolderPath,
	mapAssetsFilesFolderPath
} from '../content/use-cases/content.core';
import { getAppNumberReference } from '@gaio/shared/utils';
import { dbGaio } from '@/db/db.gaio';
import { $ } from 'bun';
import dayjs from 'dayjs';

export async function exportBackup(backupData: BackupExportType) {
	const backupId = `bkp_${backupData.options.appId.replace(/:/g, '-')}_${dayjs().format('YY-MM-DD_HH-mm-ss')}`;
	const appIdNumberReference = getAppNumberReference(backupData.options.appId);

	const appExportFolder = join(
		appFilesFolderPath,
		appIdNumberReference,
		'assets',
		'backups'
	);

	ensureDirSync(appExportFolder);

	const appBackupFolder = join(appExportFolder, backupId);

	try {
		if (backupData.foundation.flow.length) {
			await exportFlows(
				backupData.options.appId,
				backupData.foundation.flow,
				appBackupFolder
			);
		}

		if (
			backupData.foundation.params.length ||
			backupData.foundation.forms.length
		) {
			await exportParamsAndForms(
				backupData.options.appId,
				backupData.foundation.params,
				backupData.foundation.forms,
				appBackupFolder
			);
		}

		if (backupData.foundation.meta.length) {
			await exportMeta(
				backupData.options.appId,
				backupData.foundation.meta,
				appBackupFolder
			);
		}

		if (backupData.foundation.api.length) {
			await exportApi(
				backupData.options.appId,
				backupData.foundation.api,
				appBackupFolder
			);
		}

		if (backupData.foundation.maps.length) {
			await exportMap(backupData.foundation.maps, appBackupFolder);
		}

		// zip the folder
		await zipFolder(appBackupFolder, join(appExportFolder, `${backupId}.zip`));
		await remove(appBackupFolder);

		return { status: 'done', output: `${backupId}.zip` };
	} catch (error) {
		console.error(error);
		await remove(appBackupFolder);
		return { status: 'error' };
	}
}

async function exportFlows(
	appId: string,
	flowIdList: string[],
	appExportFolder: string
) {
	const flowList = await dbGaio().query<FlowType>(
		'SELECT * FROM flow WHERE flowId IN {flowIdList: Array(String)} AND appId = {appId: String}',
		{
			params: {
				flowIdList,
				appId
			},
			parse: ['workflow', 'options']
		}
	);

	const codeSnippetIdList: string[] = [];
	const sourceIdList: string[] = [];

	for (const flow of flowList) {
		for (const node of flow.workflow.nodes) {
			if (node.codeSnippetId) {
				codeSnippetIdList.push(node.codeSnippetId);
			}

			if (node.sourceId) {
				sourceIdList.push(node.sourceId);
			}
		}
	}

	const codeSnippetList = await dbGaio().query<CodeSnippetsEntity>(
		'SELECT * FROM code_snippet WHERE codeSnippetId IN {codeSnippetIdList: Array(String)} AND appId = {appId: String}',
		{
			params: {
				codeSnippetIdList,
				appId
			}
		}
	);

	const sourceList = await dbGaio().query<SourceEntity>(
		'SELECT sourceId, sourceName, client FROM source WHERE sourceId IN {sourceIdList: Array(String)}',
		{
			params: { sourceIdList, appId }
		}
	);

	const cronScheduleList = await dbGaio().query<CronEntity>(
		"SELECT * FROM cron WHERE appId = {appId: String} AND refId IN {flowIdList: Array(String)} AND refType = 'flow'",
		{
			params: {
				appId,
				flowIdList
			},
			parse: ['cronBase']
		}
	);

	saveFile(join(appExportFolder, 'sources.json'), sourceList || []);
	saveFile(join(appExportFolder, 'flows.json'), flowList || []);
	saveFile(join(appExportFolder, 'code_snippets.json'), codeSnippetList || []);
	saveFile(join(appExportFolder, 'cron_flow.json'), cronScheduleList || []);
}

async function exportParamsAndForms(
	appId: string,
	params: string[],
	forms: string[],
	appExportFolder: string
) {
	const appData = await dbGaio()
		.query<AppType>(
			'SELECT params,forms FROM app WHERE appId = {appId: String}',
			{
				params: { appId },
				parse: ['params', 'forms']
			}
		)
		.then(([app]) => app);

	const paramsData = appData.params.filter((param) =>
		params.includes(param.paramName)
	);
	saveFile(join(appExportFolder, 'params.json'), paramsData || []);

	const formsData = appData.forms.filter((form) => forms.includes(form.formId));
	saveFile(join(appExportFolder, 'forms.json'), formsData || []);
}

async function exportMeta(
	appId: string,
	meta: string[],
	appExportFolder: string
) {
	const metaData = await dbGaio().query<MetaType>(
		'SELECT * FROM meta WHERE appId = {appId: String} AND metaId IN {meta: Array(String)}',
		{
			params: { appId, meta },
			parse: ['options', 'fields']
		}
	);

	const cronScheduleList = await dbGaio().query<CronEntity>(
		"SELECT * FROM cron WHERE appId = {appId: String} AND refId IN {meta: Array(String)} AND refType = 'meta'",
		{
			params: { appId, meta },
			parse: ['cronBase']
		}
	);

	saveFile(join(appExportFolder, 'meta.json'), metaData || []);
	saveFile(join(appExportFolder, 'cron_meta.json'), cronScheduleList || []);
}

async function exportApi(
	appId: string,
	apiIdList: string[],
	appExportFolder: string
) {
	const apiData = await dbGaio().query<ApiType>(
		'SELECT * FROM api WHERE appId = {appId: String} AND apiId IN {apiIdList: Array(String)}',
		{
			params: { appId, apiIdList }
		}
	);

	const apiKeyIdList: string[] = [];

	for (const api of apiData) {
		if (api.assignedKeys.length) {
			apiKeyIdList.push(...api.assignedKeys);
		}
	}

	const apiKeyData = await dbGaio().query<ApiKeyType>(
		'SELECT * FROM api_key WHERE apiKeyId IN {apiKeyIdList: Array(String)} AND appId = {appId: String}',
		{
			params: { appId, apiKeyIdList }
		}
	);

	saveFile(join(appExportFolder, 'api.json'), apiData || []);
	saveFile(join(appExportFolder, 'api_key.json'), apiKeyData || []);
}

async function exportMap(mapIdList: string[], appExportFolder: string) {
	const mapData = await dbGaio().query<MapType>(
		'SELECT * FROM map WHERE mapId IN {mapIdList: Array(String)}',
		{
			params: { mapIdList },
			parse: ['options']
		}
	);

	for (const map of mapData) {
		const folderPath = join(mapAssetsFilesFolderPath(''));
		const sourceMapFilePath = join(folderPath, `${map.mapId}.json`);
		const targetMapFilePath = join(
			appExportFolder,
			'geojson',
			`${map.mapId}.json`
		);

		try {
			await copy(sourceMapFilePath, targetMapFilePath);
		} catch {
			console.warn(`Map file ${sourceMapFilePath} not found, skipping...`);
		}
	}

	saveFile(join(appExportFolder, 'map.json'), mapData || []);
}

function saveFile(filePath: string, data: unknown) {
	return Bun.write(filePath, JSON.stringify(data, null, 2));
}

async function zipFolder(folderPath: string, zipFolderPath: string) {
	try {
		await $`cd ${folderPath} && zip -r ${zipFolderPath} .`;
	} catch (error) {
		console.error(error);
	}
}
