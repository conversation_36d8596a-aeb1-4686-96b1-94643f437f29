import { mkdir } from 'node:fs/promises';
import { dirname, join } from 'node:path';
import { appFilesFolderPath } from '../content/use-cases/content.core';
import { getAppNumberReference } from '@gaio/shared/utils';
import type { Context } from 'hono';
import { HTTPException } from 'hono/http-exception';
import { $ } from 'bun';
import fs from 'fs-extra';

export async function importBackupFile(c: Context) {
	const appIdNumberReference = getAppNumberReference(c.req.param('appId'));
	const appExportFolder = join(
		appFilesFolderPath,
		appIdNumberReference,
		'backup-import'
	);

	// Clear the backup-import folder before starting
	await fs.emptyDir(appExportFolder);

	// Create backup import directory if it doesn't exist
	await mkdir(appExportFolder, { recursive: true });

	// Parse the multipart form data
	const formData = await c.req.parseBody();
	const file = formData.file;

	if (!file) {
		throw new HTTPException(400, {
			cause: 'missingFile',
			message: 'No file provided'
		});
	}

	if (!(file instanceof File)) {
		throw new HTTPException(400, {
			cause: 'invalidFile',
			message: 'Invalid file format'
		});
	}

	// Validate file type
	const allowedTypes = ['application/json', 'application/zip'];
	if (!allowedTypes.includes(file.type)) {
		throw new HTTPException(400, {
			cause: 'invalidFileType',
			message: 'Only JSON and ZIP files are allowed'
		});
	}

	// Generate unique filename with timestamp
	const fileExtension = file.name.split('.').pop();

	const fileName = `backup.${fileExtension}`;
	const filePath = join(appExportFolder, fileName);

	// Save the file
	const buffer = await file.arrayBuffer();
	await Bun.write(filePath, Buffer.from(buffer));

	// unzip the file
	await unzipFile(filePath, appExportFolder);

	return {
		message: 'Backup file uploaded successfully'
	};
}

async function unzipFile(filePath: string, appExportFolder: string) {
	try {
		const parentDir = dirname(filePath);
		await $`cd ${parentDir} && unzip -o ${filePath} -d ${appExportFolder}`;

		// Delete the zip file after extraction
		await fs.remove(filePath);
	} catch (error) {
		console.error(error);
	}
}
