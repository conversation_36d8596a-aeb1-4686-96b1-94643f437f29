import type {
	MetaOptionsFilters,
	MetaOptionsInsightsList
} from '@/components/meta/meta.entity';

export type InsightsFoundationType = Partial<{
	deleteData: boolean;
	filters: MetaOptionsFilters[];
	measureType: string;
	period: string;

	colDate: string;
	colText?: string;
	colNumber?: string | MetaOptionsInsightsList;

	colPart?: string;
	colTextValue?: string;

	dateStart?: unknown;
	dateEnd?: unknown;

	dateColumn?: string;
	dateRef?: string;

	dimension?: string;
	dimensionValue?: string;

	growthPercentage?: number;

	measure?: string;

	nextLevel?: string;
}>;
