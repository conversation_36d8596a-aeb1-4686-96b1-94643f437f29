import { dbGaio } from '@/db/db.gaio';
import type { DatabaseConnectionQuery } from '@/db/db.hub';
import { DbService } from '@/db/db.service';
import type { MetaEntity } from '@/db/entities';
import type { InsightListMetadataRequestDTO } from '@/types/http/dtos/insight.dtos';
import { HTTPException } from 'hono/http-exception';

export const insightsListMetadataUseCase = async ({
	settings,
	userId
}: InsightListMetadataRequestDTO & { userId: string }) => {
	try {
		let finalQuery = '';


		const metaList = await dbGaio().query<
			Pick<MetaEntity, 'metaId' | 'repoId' | 'appId' | 'tableName' | 'options'>
		>(
			`
				SELECT 
					DISTINCT meta.metaId as metaId,
					meta.repoId as repoId,
					meta.appId as appId,
					app.appName as appName,
					meta.tableName as tableName,
					meta.options as options,
					'insights' as metaType
				FROM
					meta
				INNER JOIN tag ON
					meta.appId = tag.refId
				INNER JOIN app ON
					app.appId = meta.appId 
				LEFT JOIN repo ON
					meta.repoId = repo.repoId
				WHERE
					tag.userId = {userId: String} 
					AND has(meta.metaType, 'insights')
					AND repo.repoId IS NOT NULL
					AND repo.repoId != ''`,
					{ params: { userId }, parse: ['options'] }
		);

		const where = [];
		const period = settings.period;

		let order = '';
		if (period === 'd') {
			order = ' ORDER BY d_0 desc ';
		} else if (period === 'w') {
			order = ' ORDER BY w_0 desc ';
		} else if (period === 'm') {
			order = ' ORDER BY m_0 desc ';
		}

		if (settings.context && settings.context !== '_all') {
			where.push(`tableName = '${settings.context}'`);
		}

		if (settings.signal === 'u') {
			if (period === 'd') {
				where.push('diffDayWeekAgo > 0');
			} else if (period === 'w') {
				where.push('diffWeek4 > 0');
			} else if (period === 'm') {
				where.push('diffMonth1 > 0');
			}
		}

		if (settings.signal === 'd') {
			if (period === 'd') {
				where.push('diffDayWeekAgo < 0');
			} else if (period === 'w') {
				where.push('diffWeek4 < 0');
			} else if (period === 'm') {
				where.push('diffMonth1 < 0');
			}
		}

		if (!settings.type.length) {
			if (period === 'd') {
				where.push(
					'(isNull(threeDayDown) AND isNull(threeDayUp) AND isNull(greatDayDown) AND isNull(greatDayUp))'
				);
			} else if (period === 'w') {
				where.push(
					'(isNull(threeWeekDown) AND isNull(threeWeekUp) AND isNull(greatWeekDown) AND isNull(greatWeekUp))'
				);
			} else if (period === 'm') {
				where.push(
					'(isNull(threeMonthDown) AND isNull(threeMonthUp) AND isNull(greatMonthDown) AND isNull(greatMonthUp))'
				);
			}
		} else {
			if (settings.type.includes('x') && settings.type.includes('t')) {
				if (period === 'd') {
					where.push(
						'((greatDayDown = 1 OR greatDayUp = 1) AND (threeDayDown = 1 OR threeDayUp = 1))'
					);
				} else if (period === 'w') {
					where.push(
						'((greatWeekDown = 1 OR greatWeekUp = 1) AND (threeWeekDown = 1 OR threeWeekUp = 1))'
					);
				} else if (period === 'm') {
					where.push(
						'((greatMonthDown = 1 OR greatMonthUp = 1) AND (threeMonthDown = 1 OR threeMonthUp = 1))'
					);
				}
			} else if (settings.type.includes('x')) {
				if (period === 'd') {
					where.push('(greatDayDown = 1 OR greatDayUp = 1)');
				} else if (period === 'w') {
					where.push('(greatWeekDown = 1 OR greatWeekUp = 1)');
				} else if (period === 'm') {
					where.push('(greatMonthDown = 1 OR greatMonthUp = 1)');
				}
			} else if (settings.type.includes('t')) {
				if (period === 'd') {
					where.push('(threeDayDown = 1 OR threeDayUp = 1)');
				} else if (period === 'w') {
					where.push('(threeWeekDown = 1 OR threeWeekUp = 1)');
				} else if (period === 'm') {
					where.push(' (threeMonthDown = 1 OR threeMonthUp = 1)');
				}
			}
		}

		if (settings.dimension && settings.dimension !== '_all') {
			where.push(`dimColumn = '${settings.dimension}'`);
		}

		let dimValueWhere = '';

		if (settings.dimensionValue?.length) {
			dimValueWhere = ` and dimValue in ('${settings.dimensionValue.join("','")}') `;
		}

		if (
			settings.dimension &&
			settings.dimension !== '_all' &&
			settings.measure &&
			settings.measure !== '_all'
		) {
			where.push(`metColumn = '${settings.measure}'`);
		}

		const finalWhere = where.length > 0 ? ` AND ${where.join(' AND ')}` : '';

		const result = [];
		const dimValues = [];

		const appListByRepo: {
			[key: string]: unknown[];
		} = {};

		const repoConnection: {
			[key: string]: DatabaseConnectionQuery;
		} = {};

		const tableNameByAppId: {
			[key: string]: string[];
		} = {};

		const totalRows = [];

		for (const insight of metaList) {
			if (!repoConnection[insight.repoId]) {
				repoConnection[insight.repoId] = await new DbService().connect({
					appId: insight.appId,
					client: 'clickhouse',
					repoId: insight.repoId,
					sourceType: 'super'
				});
			}

			tableNameByAppId[insight.appId] = tableNameByAppId[insight.appId] || [];
			tableNameByAppId[insight.appId].push(insight.tableName);

			appListByRepo[insight.repoId] = appListByRepo[insight.repoId] || [];
			appListByRepo[insight.repoId].push(insight.appId);
		}

		for (const repo of Object.keys(appListByRepo)) {
			if (appListByRepo[repo].length === 0) continue;

			const tTable = await repoConnection[repo]
				.query<{ total_rows: unknown; metadata_modification_time: unknown }>(
					`
						SELECT
							total_rows, metadata_modification_time
						FROM
							system.tables
						WHERE
							name = 'insightRules'
					`
				)
				.then((res) => res.data[0]);

			const tableNames = appListByRepo[repo].map((appId) => {
				return tableNameByAppId[appId].map((tableName) => {
					return `'${tableName}'`;
				});
			});

			const countResults = await repoConnection[repo]
				.query<{ count_insights: number }>(
					`
						SELECT
							count(*) AS count_insights
						FROM
							default.insightRules
						WHERE
							appId IN (${appListByRepo[repo].map((i) => `'${i}'`).join(',')})
							AND tableName IN (${tableNames})
							${finalWhere}
					`
				)
				.then((res) => res.data[0]);

			totalRows.push({
				...tTable,
				count_insights: countResults.count_insights
			});

			finalQuery = `
					SELECT 
						*
					FROM
						default.insightRules
					WHERE
						appId IN (${appListByRepo[repo].map((i) => `'${i}'`).join(',')})
						AND tableName IN (${tableNames})
						${finalWhere} 
						${dimValueWhere}
					${order}
					LIMIT 100 BY tableName
				`;

			if (settings.dimension && settings.dimension !== '_all') {
				const dimValue = await repoConnection[repo]
					.query<{ dimValue: string }>(
						`
							SELECT DISTINCT
								dimValue
							FROM
								default.insightRules
							WHERE
								appId IN (${appListByRepo[repo].map((i) => `'${i}'`).join(',')})
								AND tableName IN (${tableNames})
								${finalWhere}
							LIMIT 100
						`
					)
					.then((res) => res.data);

				dimValues.push(...dimValue);
			}

			const finalQueryResult = await repoConnection[repo]
				.query(finalQuery)
				.then((res) => res.data);

			result.push(...finalQueryResult);
		}

		const appsData = new Map()
		metaList.forEach(item=> appsData.set(item.appId, item.appName))
		result.forEach((item, index)=> result[index].appName = appsData.get(item.appId))
		
		return {
			dimValues: dimValues.map((d) => {
				return {
					label: d.dimValue,
					value: d.dimValue
				};
			}),
			finalQuery,
			insights: result,
			metadata: metaList,
			totalRows
		};
	} catch (err) {
		if (err instanceof HTTPException) {
			throw err;
		}

		const message = `[INSIGHTS] Error on list insights metadata ${err.message}`;

		throw new HTTPException(500, { message });
	}
};
