import { readFileSync } from 'node:fs';
import { resolve } from 'node:path';
import type { InsightsFoundationType } from '@/components/insight/use-cases/insights-foundation.type';
import type {
	MetaOptionsFilters,
	MetaOptionsInsightsList
} from '@/components/meta/meta.entity';

export const generateFilters = (filters: Array<MetaOptionsFilters>) => {
	let filter = '';

	filters.forEach((fil) => {
		if (fil.value && fil.value !== '') {
			filter += ` and ${fil.columnName} ${fil.operator} '${fil.value}' `;
		}
	});

	return filter;
};

export const transformQuery = ({
	statement,
	foundation,
	tableName,
	databaseName
}: {
	statement: string;
	foundation: InsightsFoundationType;
	tableName: string;
	databaseName: string;
}) => {
	const {
		measureType,
		colDate,
		colPart,
		colText,
		colNumber,
		dateRef,
		growthPercentage,
		filters
	} = foundation;

	const processSql = readFileSync(resolve('content', 'insights-sql', statement))
		.toString()
		.replace(/tableName/g, tableName)
		.replace(/databaseName/g, databaseName)
		.replace(/colDate/g, colDate)
		.replace(/colPart/g, colPart)
		.replace(/colText/g, colText)
		.replace(/dateRef/g, dateRef)
		.replace(/filters/g, generateFilters(filters))
		.replace(/subGrowthPercentage/g, `${1 - Number(growthPercentage)}`)
		.replace(/addGrowthPercentage/g, `${1 + Number(growthPercentage)}`);

	if (measureType === 'computed') {
		const _colNumber = colNumber as MetaOptionsInsightsList;

		return processSql
			.replace(/colLeft/g, _colNumber.left)
			.replace(/colRight/g, _colNumber.right)
			.replace(/operator/g, _colNumber.operator);
	}

	return processSql.replace(/colNumber/g, colNumber as string);
};
