import type { InsightsFoundationType } from '../insights-foundation.type';
import { transformQuery } from './insight-filters';

export const query = ({
	foundation,
	isTotal,
	databaseName,
	tableName
}: {
	foundation: InsightsFoundationType;
	tableName: string;
	databaseName: string;
	isTotal: unknown;
}) => {
	const { colText } = foundation;
	const dimension = `${!isTotal ? '' + colText : ''} ${isTotal ? `'lang.GENERAL' as ${colText}` : ''}`;
	const doGroup = `${!isTotal ? 'GROUP BY ' + colText : ''}`;
	const doOrder = `${!isTotal ? 'ORDER BY ' + colText : ''}`;

	// create main table
	const createMainTable = transformQuery({
		databaseName,
		foundation,
		statement: 'query-main-table.sql',
		tableName
	})
		.replace(/dimension/g, dimension)
		.replace(/doOrder/g, doOrder)
		.replace(/doGroup/g, doGroup);

	// select table
	const selectMainTable = transformQuery({
		databaseName,
		foundation,
		statement: 'query-main-table-select.sql',
		tableName
	}).replace(/dimension/g, dimension);

	return {
		createMainTable,
		selectMainTable
	};
};
