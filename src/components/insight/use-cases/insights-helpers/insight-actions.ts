import type { InsightsFoundationType } from '../insights-foundation.type';

export const query = ({
	foundation,
	tableName,
	filters,
	measures
}: {
	foundation: InsightsFoundationType;
	tableName: string;
	filters: object[];
	measures: string[];
}) => {
	const { colDate, dateStart, dateEnd, dateRef, colText, colTextValue } =
		foundation;

	let agg = '';
	let daily = '';

	measures.forEach((num) => {
		agg += `
			sum(
				CASE
					WHEN ${colDate}
						BETWEEN "${dateStart}"
						AND "${dateRef}"
					THEN ${num}
				END
			) as ${num}_cur,
      sum(
				CASE
					WHEN ${colDate}
						BETWEEN ("${dateStart}" - interval 28 day)
      			AND ("${dateRef}" - interval 28 day)
					THEN ${num} 
				END
			) as ${num}_pre,`;

		daily += `
      sum(
				CASE
					WHEN ${colDate}
						BETWEEN "${dateStart}"
						AND least("${dateRef}", "${dateEnd}")
					THEN ${num}
				END
			) as ${num}_cur,
      sum(
				CASE
					WHEN ${colDate}
						BETWEEN date_sub("${dateStart}", interval 28 day)
						AND date_sub(least("${dateRef}", "${dateEnd}"), interval 28 day)
					THEN ${num}
				END
			) as ${num}_pre,
    `;
	});

	return `
    SELECT
			${agg.trim().substring(0, agg.trim().length - 1)}
    FROM
			${tableName}
    WHERE
			${colTextValue !== 'lang.GENERAL' ? colText + '= "' + colTextValue + '"' : colText + ' IS NOT NULL '}
			AND (${colDate} BETWEEN "${dateStart}" AND least("${dateRef}", "${dateEnd}")
		OR ${colDate} BETWEEN date_sub("${dateStart}", interval 28 day)
      AND date_sub(least("${dateRef}", "${dateEnd}"), interval 28 day));

    SELECT
			("${dateStart}" + interval (
				CASE
					WHEN ${colDate}
						BETWEEN "${dateStart}"
						AND least("${dateRef}", "${dateEnd}")
    			THEN datediff(${colDate}, "${dateStart}")
						WHEN ${colDate}
							BETWEEN date_sub("${dateStart}", interval 28 day)
    					AND date_sub(least("${dateRef}", "${dateEnd}"), interval 28 day)
    				THEN datediff(${colDate}, date_sub("${dateStart}", interval 28 day)) 
						END) day) as date,
			${daily.trim().substring(0, daily.trim().length - 1)}
    FROM
			${tableName}
    WHERE
			(${colTextValue !== 'lang.GENERAL' ? colText + '= "' + colTextValue + '"' : colText + ' IS NOT NULL '}
      	AND (${colDate}
					BETWEEN "${dateStart}"
				AND least("${dateRef}", "${dateEnd}")
    	  OR ${colDate}
					BETWEEN date_sub("${dateStart}", interval 28 day)
     			AND date_sub(least("${dateRef}", "${dateEnd}"), interval 28 day)))
		${filters}
		GROUP BY DATE
		ORDER BY DATE ASC;
  `;
};
