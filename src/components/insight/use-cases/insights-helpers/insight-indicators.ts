import type { InsightsFoundationType } from '../insights-foundation.type';
import { generateFilters, transformQuery } from './insight-filters';

export const query = ({
	foundation,
	tableName,
	databaseName,
	list,
	measure
}: {
	foundation: InsightsFoundationType;
	tableName: string;
	databaseName: string;
	list: Array<string>;
	measure: Array<string>;
}) => {
	const { measureType, colText, colTextValue } = foundation;
	const filters = generateFilters(foundation.filters);
	const measures = measureType === 'computed' ? list : measure;

	let daily = '';
	const dailyString = transformQuery({
		databaseName,
		foundation,
		statement: 'query-main-indicators-daily.sql',
		tableName
	});

	let weekly = '';
	const weeklyString = transformQuery({
		databaseName,
		foundation,
		statement: 'query-main-indicators-weekly.sql',
		tableName
	});

	let monthly = '';
	const monthlyString = transformQuery({
		databaseName,
		foundation,
		statement: 'query-main-indicators-monthly.sql',
		tableName
	});

	measures.forEach((num) => {
		daily += structuredClone(dailyString).replace(/itemNum/g, num);
		weekly += structuredClone(weeklyString).replace(/itemNum/g, num);
		monthly += structuredClone(monthlyString).replace(/itemNum/g, num);
	});

	const mainFilter =
		colTextValue !== 'lang.GENERAL'
			? colText + "= '" + colTextValue + "'"
			: colText + ' IS NOT NULL ';

	return {
		daily: `
			SELECT
				${daily.trim().substring(0, daily.trim().length - 1)}
			FROM
				${databaseName}.${tableName}
			WHERE
				${mainFilter}
			${filters}`,
		monthly: `  
			SELECT
				${monthly.trim().substring(0, monthly.trim().length - 1)}
			FROM
				${databaseName}.${tableName}
			WHERE
				${mainFilter}
			${filters} 
		`,
		weekly: `
			SELECT
				${weekly.trim().substring(0, weekly.trim().length - 1)}
			FROM
				${databaseName}.${tableName}
			WHERE
				${mainFilter}
			${filters}
		`
	};
};
