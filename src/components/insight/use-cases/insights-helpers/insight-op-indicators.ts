import type { InsightsFoundationType } from '@/components/insight/use-cases/insights-foundation.type';
import type { MetaOptionsInsightsList } from '@/components/meta/meta.entity';
import { generateFilters } from './insight-filters';

export const query = ({
	foundation,
	tableName,
	databaseName,
	list
}: {
	foundation: InsightsFoundationType;
	tableName: string;
	databaseName: string;
	list: Array<MetaOptionsInsightsList>;
}) => {
	const { colDate, colText, colTextValue, dateRef, filters } = foundation;

	const measures = list;

	let daily = '';
	let weekly = '';
	let monthly = '';

	measures.forEach((num) => {
		const d_last = `
			sum(
				CASE
					WHEN ${colDate} = toDate('${dateRef}')
					THEN ${num.left}
				END
			)
	    ${num.operator}
			sum(
				CASE
					WHEN ${colDate} = toDate('${dateRef}')
					THEN ${num.right}
				END
			)
		`;

		const d_previous = `
			sum(
				CASE
					WHEN ${colDate} = subtractWeeks(toDate('${dateRef}'),  1 )
					THEN ${num.left}
				END
			)
      ${num.operator}
      sum(
				CASE
					WHEN ${colDate} = subtractWeeks(toDate('${dateRef}'),  1 )
					THEN ${num.right}
				END
			)
		`;

		const w_last = `
			sum(
				CASE
					WHEN ${colDate}
						BETWEEN addDays(toDate('${dateRef}'), (1 - toDayOfWeek(toDate('${dateRef}'))) )
						AND toDate('${dateRef}')
					THEN ${num.left}
				END
			)
      ${num.operator}
      sum(
				CASE
					WHEN ${colDate}
						BETWEEN addDays(toDate('${dateRef}'), (1 - toDayOfWeek(toDate('${dateRef}'))) )
						AND toDate('${dateRef}')
					THEN ${num.right}
				END
			)
		`;

		const w_previous = `
			sum(
				CASE
					WHEN ${colDate}
						BETWEEN subtractDays(toDate('${dateRef}'), (toDayOfWeek(toDate('${dateRef}'))+8) )
						AND subtractDays(toDate('${dateRef}'),  7 ) 
					THEN ${num.left}
				END
			) 
      ${num.operator}
      sum(
				CASE
					WHEN ${colDate}
						BETWEEN subtractDays(toDate('${dateRef}'), (toDayOfWeek(toDate('${dateRef}'))+8) )
					AND subtractDays(toDate('${dateRef}'),  7 )
				THEN ${num.right}
				END
			)
		`;

		const m_last = `
      sum(
				CASE
					WHEN ${colDate}
						BETWEEN subtractDays(toDate('${dateRef}'),  toDayOfMonth(toDate('${dateRef}'))-1 )
						AND toDate('${dateRef}')
					THEN ${num.left}
				END
			)
      ${num.operator}
      sum(
				CASE
					WHEN ${colDate}
						BETWEEN subtractDays(toDate('${dateRef}'), toDayOfMonth(toDate('${dateRef}'))-1 )
						AND toDate('${dateRef}')
					THEN ${num.right}
				END
			)
		`;

		const m_previous = `
	    sum(
				CASE
					WHEN ${colDate}
						BETWEEN subtractMonths(subtractDays(toDate('${dateRef}'), toDayOfMonth(toDate('${dateRef}') )-1 ),  1 )
						AND date_sub(date_sub(date_add(toDate('${dateRef}'), interval 1 day), interval 1 month), interval 1 day)
					THEN ${num.left}
				END
			)
      ${num.operator}
   	 sum(
				CASE
					WHEN ${colDate}
						BETWEEN subtractMonths(subtractDays(toDate('${dateRef}'), toDayOfMonth(toDate('${dateRef}') )-1 ),  1 )
						AND subtractDays(subtractMonths(subtractDays(toDate('${dateRef}'),  1 ),  1 ),  1 ) 
					THEN ${num.right} 
				END
			)
		`;

		daily += `
			${d_previous} as ${num.left}_${num.right}_previous,
			${d_last} as ${num.left}_${num.right}_last,
			(${d_last}) - (${d_previous}) as ${num.left}_${num.right}_dif_value,
			((${d_last}) - (${d_previous})) / ${d_previous} as ${num.left}_${num.right}_dif_pct,
		`;

		weekly += `
			${w_last} as ${num.left}_${num.right}_last,
			${w_previous} as ${num.left}_${num.right}_previous,
			${w_last} - ${w_previous} as ${num.left}_${num.right}_dif_value,
			((${w_last}) - (${w_previous})) / ${w_previous} as ${num.left}_${num.right}_dif_pct,
		`;

		monthly += `
      ${m_last} as ${num.left}_${num.right}_last,
      ${m_previous} as ${num.left}_${num.right}_previous,
      ${m_last} - ${m_previous} as ${num.left}_${num.right}_dif_value,
      (toInt16(${m_last}) - toInt16(${m_previous})) / ${m_previous} as ${num.left}_${num.right}_dif_pct,
		`;
	});

	return {
		daily: `
			SELECT
				${daily.trim().substring(0, daily.trim().length - 1)}
			FROM 
				${databaseName}.${tableName}
			WHERE 
				${colTextValue !== 'lang.GENERAL' ? colText + '= "' + colTextValue + '"' : colText + ' IS NOT NULL '}
			${generateFilters(filters)}
		`,
		monthly: `
			SELECT
				${monthly.trim().substring(0, monthly.trim().length - 1)}
			FROM
				${databaseName}.${tableName}
			WHERE
				${colTextValue !== 'lang.GENERAL' ? colText + '= "' + colTextValue + '"' : colText + ' IS NOT NULL '}
			${generateFilters(filters)}
		`,
		weekly: `
			SELECT
				${weekly.trim().substring(0, weekly.trim().length - 1)}
			FROM
				${databaseName}.${tableName}
			WHERE
				${colTextValue !== 'lang.GENERAL' ? colText + '= "' + colTextValue + '"' : colText + ' IS NOT NULL '}
			${generateFilters(filters)}
		`
	};
};
