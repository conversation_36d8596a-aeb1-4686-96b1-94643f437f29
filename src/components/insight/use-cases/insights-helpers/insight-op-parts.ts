import type { InsightsFoundationType } from '../insights-foundation.type';
import { transformQuery } from './insight-filters';

export const query = ({
	foundation,
	tableName,
	databaseName
}: {
	foundation: InsightsFoundationType;
	tableName: string;
	databaseName: string;
}) => {
	const { colTextValue, colText } = foundation;
	const categoryFilter = `${
		colTextValue !== 'lang.GENERAL'
			? colText + "= '" + colTextValue + "'"
			: colText + ' IS NOT NULL '
	}`;

	return {
		daily: transformQuery({
			databaseName,
			foundation,
			statement: 'query-comp-parts-daily.sql',
			tableName
		}).replace(/categoryFilter/g, categoryFilter),
		monthly: transformQuery({
			databaseName,
			foundation,
			statement: 'query-comp-parts-monthly.sql',
			tableName
		}).replace(/categoryFilter/g, categoryFilter),
		weekly: transformQuery({
			databaseName,
			foundation,
			statement: 'query-comp-parts-weekly.sql',
			tableName
		}).replace(/categoryFilter/g, categoryFilter)
	};
};
