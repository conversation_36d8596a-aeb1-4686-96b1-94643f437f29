import { DbService } from '@/db/db.service';
import type { InsightListPartRequestDTO } from '@/types/http/dtos/insight.dtos';
import { HTTPException } from 'hono/http-exception';

export const insightsLoadPartUseCase = async ({
	foundation,
	appId,
	repoId,
	tableName,
	databaseName
}: InsightListPartRequestDTO) => {
	try {
		const {
			dateRef,
			dimension,
			dateColumn,
			dimensionValue,
			nextLevel,
			period,
			measure
		} = foundation;

		let whenRef = 'm_0';
		if (period === 'd') {
			whenRef = 'd_0';
		} else if (period === 'w') {
			whenRef = 'w_0';
		} else if (period === 'm') {
			whenRef = 'm_0';
		}

		const connection = await new DbService().connect({
			appId,
			client: 'clickhouse',
			databaseName,
			repoId,
			sourceType: 'bucket'
		});

		const sql = `
			SELECT
				${nextLevel} as dimension,
				SUM(
					CASE WHEN toDate(${dateColumn}) BETWEEN subtractDays(
						toDate('${dateRef}'),
						(
							toDayOfMonth(
								toDate('${dateRef}')
							) -1
						)
					)
					AND toDate('${dateRef}') THEN ${measure} END
				) AS m_0,
				SUM(
					CASE WHEN toDate(${dateColumn}) BETWEEN subtractMonths(
						subtractDays(
							toDate('${dateRef}'),
							toDayOfMonth(
								toDate('${dateRef}')
							) -1
						),
						1
					)
					AND subtractDays(
						subtractMonths(
							addDays(
								toDate('${dateRef}'),
								1
							),
							1
						),
						1
					) THEN ${measure} END
				) AS m_1,
				SUM(
					CASE WHEN toDate(${dateColumn}) BETWEEN addDays(
						toDate('${dateRef}'),
						(
								- toDayOfWeek(
								toDate('${dateRef}')
							)
						)
					)
					AND toDate('${dateRef}') THEN ${measure} END
				) AS w_0,
					SUM(
					CASE WHEN toDate(${dateColumn}) BETWEEN subtractDays(
						toDate('${dateRef}'),
						(
							(
								toDayOfWeek(
									toDate('${dateRef}')
								)
							) + 29
						)
					)
					AND subtractDays(
						toDate('${dateRef}'),
							28
					) THEN ${measure} END
				) AS w_4, 
				SUM(
					CASE WHEN toDate(${dateColumn}) = toDate('${dateRef}') THEN ${measure} END
				) AS d_0,
				SUM(
					CASE WHEN toDate(${dateColumn}) = subtractWeeks(
						toDate('${dateRef}'),
						1
					) THEN ${measure} END
				) AS d_7,
				(m_0 - m_1) / m_1 as m_diff,
				(w_0 - w_4) / w_4 as w_diff,
				(d_0 - d_7) / d_7 as d_diff
			FROM
				${databaseName}.${tableName} f
			WHERE
				${dimension} = '${dimensionValue}'
				GROUP BY ${nextLevel} WITH TOTALS
				ORDER BY ${whenRef} DESC
		`;

		return await connection.query(sql);
	} catch (err) {
		if (err instanceof HTTPException) {
			throw err;
		}

		const message = '[INSIGHTS] Error loading insights part';

		throw new HTTPException(500, { message });
	}
};
