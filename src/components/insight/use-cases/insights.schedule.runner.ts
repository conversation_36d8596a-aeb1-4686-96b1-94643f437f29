import MetaRepository from '@/components/meta/meta.repository';
import type { DatabaseConnectionQuery } from '@/db/db.hub';
import { DbService } from '@/db/db.service';
import type { InsightsFoundationType } from './insights-foundation.type';
import * as insightDate from './insights-helpers/insight-init';
import * as insightOpPrincipal from './insights-helpers/insight-op-init';

export const generateInsights = async (metaId: string) => {
	const insightsList = await MetaRepository.getMetasInsightsByMetaId(metaId);

	for (const insight of insightsList) {
		const report = insight;
		const options = report.options;

		const foundation: InsightsFoundationType = {
			deleteData: false,
			filters: options.filters || [],
			growthPercentage: options.growthPercentage,
			measureType: options.measureType,
			period: options.period
		};

		for (const colDate of options.date) {
			for (const colText of options.dimension) {
				foundation.colDate = colDate;
				foundation.colText = colText;

				if (options.measureType === 'computed') {
					for (const colNumber of options.list) {
						foundation.colNumber = colNumber;

						try {
							await prepareInsights({
								appId: insight.appId,
								databaseName: report.databaseName,
								foundation,
								metaId: report.metaId,
								repoId: insight.repoId,
								tableName: report.tableName
							});
						} catch (e) {
							console.log('insight error when computed', e.message);
						}
					}
				} else {
					for (const colNumber of options.measure) {
						foundation.colNumber = colNumber;

						try {
							await prepareInsights({
								appId: insight.appId,
								databaseName: report.databaseName,
								foundation,
								metaId: report.metaId,
								repoId: insight.repoId,
								tableName: report.tableName
							});
						} catch (e) {
							console.log('insight error when measure', e.message);
						}
					}
				}
			}
		}
	}

	return { status: 'done' };
};

const prepareInsights = async ({
	appId,
	databaseName,
	foundation,
	repoId,
	metaId,
	tableName
}: {
	appId: string;
	databaseName: string;
	foundation: InsightsFoundationType;
	repoId: string;
	metaId: string;
	tableName: string;
}) => {
	const connection = await new DbService().connect({
		appId,
		client: 'clickhouse',
		repoId,
		sourceType: 'super'
	});

	const mainTable = `insights_gaio${metaId}`;

	if (foundation && !foundation.deleteData) {
		foundation.deleteData = true;

		await prepareInsightRulesTable(connection);
		await removeInsightsByAppIdAndInsightId(appId, metaId, repoId);
	}

	// FIRST TIME ACCESS, GET MAX DATE
	if (!foundation.dateRef) {
		foundation.dateRef = await connection
			.query<{ dateRef: string }>(
				`
					SELECT
						toDate(max(${foundation.colDate})) AS dateRef 
					FROM
						${databaseName}.${tableName}
				`
			)
			.then((res) => (res.data[0] ? res.data[0].dateRef : null));
	}

	// let principalQueryTotal: { createMainTable: string; selectMainTable: string }
	let principalQueryList: { createMainTable: string; selectMainTable: string };

	switch (foundation.measureType) {
		case 'computed':
			// principalQueryTotal = insightOpPrincipal.query({foundation, databaseName, tableName, isTotal: true})

			principalQueryList = insightOpPrincipal.query({
				databaseName,
				foundation,
				isTotal: false,
				tableName
			});
			break;
		default:
			// principalQueryTotal = insightDate.query({foundation, databaseName, tableName, isTotal: true})
			principalQueryList = insightDate.query({
				databaseName,
				foundation,
				isTotal: false,
				tableName
			});
			break;
	}

	const finalQueryToRun = prepareFinalSelect({
		appId,
		dimension: foundation.colText,
		id: metaId,
		innerSelect: removeThreeLinesFrom(
			structuredClone(
				`${principalQueryList.createMainTable}`.replace(/mainTable/g, mainTable)
			)
		),
		maxDate: foundation.dateRef,
		measure: foundation.colNumber as string,
		tableName
	});

	await connection.query(finalQueryToRun);
};

const removeThreeLinesFrom = (str: string) => {
	const lines = structuredClone(str.split('\n'));

	lines.splice(0, 3);

	return lines.join('\n');
};

export const removeInsightsByAppIdAndInsightId = async (
	appId: string,
	insightId: string,
	repoId: string
) => {
	const connection = await new DbService().connect({
		appId,
		client: 'clickhouse',
		repoId,
		sourceType: 'super'
	});
	await connection.query(
		`DELETE FROM insightRules WHERE appId = '${appId}' AND id = '${insightId}'`
	);
};

const prepareFinalSelect = ({
	dimension,
	measure,
	innerSelect,
	appId,
	tableName,
	maxDate,
	id
}: {
	dimension: string;
	measure: string;
	innerSelect: string;
	appId: string;
	tableName: string;
	maxDate: string;
	id: string;
}) => {
	const dt = `${maxDate}`.trim();
	return `
		INSERT INTO
			default.insightRules
		SELECT
				generateUUIDv4() as rowId,
				'${id}' as id,
				'${appId}' as appId,
				'${tableName}' as tableName,
				toDate('${dt}') as datValue,
				'${dimension}' as dimColumn,
				${dimension} as dimValue,
				'${measure}' as metColumn,
				CASE
					WHEN
						d_0 < d_7
						AND d_7 < d_14
						AND d_14 < d_21
					THEN
						'1'
					END
						AS threeDayDown,
				CASE
					WHEN
						d_0 > d_7
						AND d_7 > d_14
						AND d_14 > d_21
					THEN
						'1'
				END
					AS threeDayUp,
				CASE
					WHEN
						w_0 < w_1
						AND w_1 < w_4
						AND w_4 < w_8
					THEN
						'1'
				END
					AS threeWeekDown,
				CASE
					WHEN
						w_0 > w_1
						AND w_1 > w_4
						AND w_4 > w_8
					THEN
						'1'
				END
					AS threeWeekUp,
				CASE
					WHEN
						m_0 < m_1
						AND m_1 < m_2
						AND m_2 < m_3
					THEN
						'1'
				END
					AS threeMonthDown,
				CASE
					WHEN
						m_0 > m_1 > m_2
						AND m_2 > m_3
					THEN
						'1'
				END
					AS threeMonthUp,
				CASE
					WHEN
						d_0 < 0.8 * d_7
					THEN
						'1'
				END
					AS greatDayDown,
				CASE
					WHEN
						d_0 > 1.2 * d_7
					THEN
						'1'
				END
					AS greatDayUp,
				CASE
					WHEN
						w_0 < 0.8 * w_1
					THEN
						'1'
				END
					AS greatWeekDown,
				CASE
					WHEN
						w_0 > 1.2 * w_1
					THEN
						'1'
				END
					AS greatWeekUp,
				CASE
					WHEN
						m_0 < 0.8 * m_1
					THEN
						'1'
				END
					AS greatMonthDown,
				CASE
					WHEN
						m_0 > 1.2 * m_1
					THEN
						'1'
				END
					AS greatMonthUp, 
			d_0 / d_1 - 1 AS diffDay, 
			w_0 / w_1 - 1 AS diffWeek, 
			w_0 / w_4 - 1 AS diffWeek4,
			w_0 / w_52 - 1 AS diffWeek52,
			m_0 / m_1 - 1 AS diffMonth1,
			m_0 / m_6 - 1 AS diffMonth6,
			m_0 / m_12 - 1 AS diffMonth12,
			d_0 / dayWeekAgo - 1 AS diffDayWeekAgo, 
			d_0 / dayMonthAgo - 1 AS diffDayMonthAgo, 
			d_0 / dayYearAgo - 1 AS diffDayYearAgo,
			dayMonthAgo,
			dayYearAgo,
			dayWeekAgo,
			d_0,
			d_1,
			d_2,
			d_3,
			d_4,
			d_5,
			d_6,
			d_7,
			d_8,
			d_9,
			d_10,
			d_11,
			d_12,
			d_13,
			d_14,
			w_0,
			w_1,
			w_2,
			w_3,
			w_4,
			w_5,
			w_6,
			w_7,
			w_8,
			w_9,
			w_10,
			m_0,
			m_1,
			m_2,
			m_3,
			m_4,
			m_5,
			m_6,
			m_7,
			m_8,
			m_9,
			m_10,
			m_11,
			m_12,
			now() as createdAt
		FROM (
			${innerSelect}
		)
		ORDER BY d_0 DESC
`;
};

const prepareInsightRulesTable = async (
	connection: DatabaseConnectionQuery
) => {
	await connection.query(
		`
			CREATE TABLE IF NOT EXISTS 
				default.insightRules 
			(
				rowId UUID DEFAULT generateUUIDv4(),
				id Nullable(String),
				appId Nullable(String),
				tableName Nullable(String),
				datValue Nullable(Date),
				dimColumn Nullable(String),
				dimValue Nullable(String),
				metColumn Nullable(String),
				threeDayDown Nullable(Int64),
				threeDayUp Nullable(Int64),
				threeWeekDown Nullable(Int64),
				threeWeekUp Nullable(Int64),
				threeMonthDown Nullable(Int64),
				threeMonthUp Nullable(Int64),
				greatDayDown Nullable(Int64),
				greatDayUp Nullable(Int64),
				greatWeekDown Nullable(Int64),
				greatWeekUp Nullable(Int64),
				greatMonthDown Nullable(Int64),
				greatMonthUp Nullable(Int64),
				diffDay Nullable(Float64),
				diffWeek Nullable(Float64),
				diffWeek4 Nullable(Float64),
				diffWeek52 Nullable(Float64),
				diffMonth1 Nullable(Float64),
				diffMonth6 Nullable(Float64),
				diffMonth12 Nullable(Float64),
				diffDayWeekAgo Nullable(Float64),
				diffDayMonthAgo Nullable(Float64),
				diffDayYearAgo Nullable(Float64),
				dayMonthAgo Nullable(Int64),
				dayYearAgo Nullable(Int64),
				dayWeekAgo Nullable(Int64),
				d_0 Nullable(Int64),
				d_1 Nullable(Int64),
				d_2 Nullable(Int64),
				d_3 Nullable(Int64),
				d_4 Nullable(Int64),
				d_5 Nullable(Int64),
				d_6 Nullable(Int64),
				d_7 Nullable(Int64),
				d_8 Nullable(Int64),
				d_9 Nullable(Int64),
				d_10 Nullable(Int64),
				d_11 Nullable(Int64),
				d_12 Nullable(Int64),
				d_13 Nullable(Int64),
				d_14 Nullable(Int64),
				w_0 Nullable(Int64),
				w_1 Nullable(Int64),
				w_2 Nullable(Int64),
				w_3 Nullable(Int64),
				w_4 Nullable(Int64),
				w_5 Nullable(Int64),
				w_6 Nullable(Int64),
				w_7 Nullable(Int64),
				w_8 Nullable(Int64),
				w_9 Nullable(Int64),
				w_10 Nullable(Int64),
				m_0 Nullable(Int64),
				m_1 Nullable(Int64),
				m_2 Nullable(Int64),
				m_3 Nullable(Int64),
				m_4 Nullable(Int64),
				m_5 Nullable(Int64),
				m_6 Nullable(Int64),
				m_7 Nullable(Int64),
				m_8 Nullable(Int64),
				m_9 Nullable(Int64),
				m_10 Nullable(Int64),
				m_11 Nullable(Int64),
				m_12 Nullable(Int64),
				createdAt DateTime DEFAULT now()
			)
			ENGINE = MergeTree
			ORDER BY tuple()`
	);
};
