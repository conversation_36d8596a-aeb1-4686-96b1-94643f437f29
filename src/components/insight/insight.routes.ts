import { insightsListMetadataUseCase } from '@/components/insight/use-cases/insights.list-metadata';
import { insightsLoadPartUseCase } from '@/components/insight/use-cases/insights.load-part';
import jwtGuard from '@/server/middleware/jwt.guard';
import { readBody } from '@/server/middleware/readBody';
import { readValidatedBody } from '@/server/middleware/readValidatedBody';
import { insightListMetadataRequestSchema } from '@/types/http/dtos/insight.dtos';
import type { GenericType } from '@gaio/shared/types';
import { Hono } from 'hono';

const app = new Hono()
	.post('list-metadata', jwtGuard().isAuth, async (c) => {
		const { settings, user } = await readValidatedBody(
			c,
			insightListMetadataRequestSchema
		);

		return c.json(
			await insightsListMetadataUseCase({ settings, userId: user.userId })
		);
	})

	.post('parts', jwtGuard().isAuth, async (c) => {
		const { databaseName, foundation, repoId, tableName, appId } =
			await readBody<GenericType>(c);

		return c.json(
			await insightsLoadPartUseCase({
				appId,
				databaseName,
				foundation,
				repoId,
				tableName
			})
		);
	});

export default app;
