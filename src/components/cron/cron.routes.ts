import { Hono } from 'hono';

const app = new Hono()
	.post('/prepare', async (c) => {
		return c.json({});
	})

	.post('/restart', async (c) => {
		return c.json({});
	})

	.post('/pause-schedule', async (c) => {
		return c.json({});
	})

	.post('/redefine-schedule', async (c) => {
		return c.json({});
	})

	.post('/pause-single-schedule', async (c) => {
		return c.json({});
	})

	.post('/redefine-single-schedule', async (c) => {
		return c.json({});
	})

	.post('/restart-drops', async (c) => {
		return c.json({});
	});

// @Post('/prepare')
// @UseGuards(AuthGuard('jwt'))
// async prepareSchedule() {
// 		return await this.cron.prepareSchedule();
// }

// @Post('/restart')
// @UseGuards(AuthGuard('jwt'))
// async cronByProcess() {
// 		return await this.cron.restart();
// }

// @Get('/pause-schedule')
// @UseGuards(AuthGuard('jwt'), new RoleGuard(['admin']))
// async pauseAllSchedule() {
// 		return await this.cron.pauseAllSchedule();
// }

// @Get('/redefine-schedule')
// @UseGuards(AuthGuard('jwt'), new RoleGuard(['admin']))
// async redefineAllSchedule() {
// 		return await this.cron.redefineSchedule();
// }

// @Post('/pause-single-schedule')
// @UseGuards(AuthGuard('jwt'), new RoleGuard(['admin', 'dev']))
// async pauseSingleSchedule(@Body('appId') appId) {
// 		return await this.cron.pauseSingleSchedule(appId);
// }

// @Post('/redefine-single-schedule')
// @UseGuards(AuthGuard('jwt'), new RoleGuard(['admin', 'dev']))
// async redefineSingleSchedule(@Body('appId') appId) {
// 		return await this.cron.redefineSingleSchedule(appId);
// }

// @Get('/restart-drops')
// @UseGuards(AuthGuard('jwt'))
// async restartDrops() {
// 		return await this.cron.dropTempSchedule();
// }

export default app;
