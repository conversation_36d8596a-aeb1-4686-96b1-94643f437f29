import {
	BULLMQ_CRON_QUEUE,
	addCronJobToRedis,
	gaioBullMQOptions,
	removeCronJobFromRedis
} from '@/components/cron/cron.schedules.tools';
import { dbGaio } from '@/db/db.gaio';
import type { AtLeastOne } from '@/types/utilities.type';
import { currentDatetime } from '@/utils/helpers';
import { Queue } from 'bullmq';
import type { CronEntity } from './cron.entity';

async function getAllActiveCron() {
	return await dbGaio().query<
		Pick<CronEntity, 'appId' | 'refId' | 'cron' | 'cronStatus' | 'refType'>
	>(
		`
			SELECT
				refId,
				appId,
				refType,
				cron,
				cronStatus
			FROM
				cron
			WHERE
				cron IS NOT NULL
				AND cron != ''
				AND cronStatus = 'active'
		`
	);
}

async function createCronReference({
	cron,
	userId
}: {
	cron: Pick<
		CronEntity,
		'appId' | 'cron' | 'cronBase' | 'cronStatus' | 'refId' | 'refType'
	>;
	userId: string;
}) {
	return await dbGaio().insert('cron', [
		{
			appId: cron.appId,
			createdBy: userId,
			cron: cron.cron || '',
			cronBase: cron.cronBase,
			cronStatus: cron.cronStatus,
			refId: cron.refId,
			refType: cron.refType,
			updatedBy: userId
		}
	]);
}

async function createCron({
	cron,
	userId
}: {
	cron: Pick<
		CronEntity,
		'appId' | 'cron' | 'cronBase' | 'cronStatus' | 'refId' | 'refType'
	>;
	userId: string;
}) {
	const cronQueue = new Queue(BULLMQ_CRON_QUEUE, gaioBullMQOptions);

	if (cron?.cron) {
		await addCronJobToRedis({
			appId: cron.appId,
			cron: cron.cron || '',
			cronStatus: cron.cronStatus,
			queue: cronQueue,
			refId: cron.refId,
			refType: cron.refType
		});
	}

	return await createCronReference({
		cron: { ...cron, cron: cron.cron || '' },
		userId
	});
}

async function updateCronByInsight({
	cron,
	userId
}: {
	cron: Pick<
		CronEntity,
		'appId' | 'cron' | 'cronBase' | 'cronStatus' | 'refId' | 'refType'
	>;
	userId: string;
}) {
	const cronQueue = new Queue(BULLMQ_CRON_QUEUE, gaioBullMQOptions);

	await addCronJobToRedis({
		appId: cron.appId,
		cron: cron.cron || '',
		cronStatus: cron.cronStatus,
		queue: cronQueue,
		refId: cron.refId,
		refType: cron.refType
	});

	await dbGaio().updateOrInsert({
		primaryKeys: ['refId', 'appId', 'refType'],
		table: 'cron',
		values: {
			appId: cron.appId,
			createdBy: userId,
			cron: cron.cron,
			cronBase: JSON.stringify(cron.cronBase),
			cronStatus: cron.cronStatus,
			refId: cron.refId,
			refType: cron.refType,
			updatedAt: currentDatetime().replace('T', ' '),
			updatedBy: userId
		}
	});
}

async function deleteCronReference(where: AtLeastOne<CronEntity>) {
	return await dbGaio().delete({ table: 'cron', where });
}

async function deleteCron(where: AtLeastOne<CronEntity>) {
	const cronQueue = new Queue(BULLMQ_CRON_QUEUE, gaioBullMQOptions);

	const cronFound = await dbGaio()
		.findAll({
			table: 'cron',
			where
		})
		.then((results) => results.data);

	try {
		for (const cron of cronFound) {
			await removeCronJobFromRedis({
				appId: cron.appId,
				queue: cronQueue,
				refId: cron.refId,
				refType: cron.refType
			});
		}
	} catch {
	} finally {
		await dbGaio().delete({ table: 'cron', where });
	}

	return { status: 'done' };
}

export default {
	createCron,
	createCronReference,
	deleteCron,
	deleteCronReference,
	getAllActiveCron,
	updateCronByInsight
};
