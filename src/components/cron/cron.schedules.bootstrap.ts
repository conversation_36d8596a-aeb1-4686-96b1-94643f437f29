import AppRepository from '@/components/app/app.repository';
import CronRepository from '@/components/cron/cron.repository';
import { generateInsights } from '@/components/insight/use-cases/insights.schedule.runner';
import { prepareFlowAndRun } from '@/components/task/use-cases/task.runner';
import { Queue, Worker } from 'bullmq';
import type { CronJobData } from './cron.schedules.tools';
import {
	BULLMQ_CRON_QUEUE,
	addCronJobToRedis,
	gaioBullMQOptions,
	generateCronJobId
} from './cron.schedules.tools';
import { getId } from '@gaio/shared/utils';

export async function cronSchedulesBootstrap() {
	const cronWorker = new Worker<CronJobData>(
		BULLMQ_CRON_QUEUE,
		async (job) => {
			const { appId, cronStatus, refId, refType } = job.data;

			const jobId = generateCronJobId({ appId, refId, refType });

			try {
				if (cronStatus === 'active') {
					if (refType === 'flow') {
						const appParams = await AppRepository.getAppParamsById(appId);

						prepareFlowAndRun({
							appId: appId,
							flowId: refId,
							logFrom: 'studio',
							userName: 'Schedule',
							params: appParams,
							userId: 'user:cron',
							sessionid: getId()
						});
					}

					if (refType === 'insight') {
						generateInsights(refId);
					}
				}
			} catch (err) {
				console.error('[CRON] Job failed to execute:', jobId);
			}
		},
		gaioBullMQOptions
	);

	cronWorker.on('ready', () => {
		console.info('[CRON] Cron Worker connected');
	});

	cronWorker.on('failed', (err) => {
		console.error('[CRON] Failure error:', err);

		console.error(
			`[CRON] Cron job failed somehow - JobId: ${generateCronJobId(err.data)}`
		);
	});

	cronWorker.on('error', (err) => {
		console.error(
			`[CRON] Something went wrong processing a cron job! - ${err.message}`
		);
	});

	try {
		const cronQueue = new Queue<CronJobData>(
			BULLMQ_CRON_QUEUE,
			gaioBullMQOptions
		);

		const cronList = await CronRepository.getAllActiveCron();
		const registeredJobs = await cronQueue.getJobSchedulers();

		const registeredJobsIds = registeredJobs.map((job) => job.key);

		const needToRegister = cronList.filter(
			(cron) =>
				!registeredJobsIds.includes(
					generateCronJobId({
						appId: cron.appId,
						refId: cron.refId,
						refType: cron.refType
					})
				)
		);

		for (const item of needToRegister) {
			if (item.cronStatus === 'active' && item.cron) {
				await addCronJobToRedis({
					appId: item.appId,
					cron: item.cron,
					cronStatus: item.cronStatus,
					queue: cronQueue,
					refId: item.refId,
					refType: item.refType
				});
			}
		}

		const cronListIds = cronList.map((cron) =>
			generateCronJobId({
				appId: cron.appId,
				refId: cron.refId,
				refType: cron.refType
			})
		);

		const needToRemoveKeys = registeredJobsIds.filter(
			(key) => !cronListIds.includes(key)
		);

		for (const jobKey of needToRemoveKeys) {
			await cronQueue.removeJobScheduler(jobKey);
		}

		console.info('[CRON] All cron jobs added to queue');
	} catch (err) {
		console.warn('[CRON] Something went wrong while initializing cron jobs');

		console.error(err);
	}
}
