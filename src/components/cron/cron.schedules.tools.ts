import type { CronEntity } from '@/components/cron/cron.entity';
import { env } from '@/utils/env';
import type { Queue, WorkerOptions } from 'bullmq';

export const gaioBullMQOptions: WorkerOptions = {
	connection: {
		host: env.GAIO_REDIS_HOST,
		port: env.GAIO_REDIS_PORT
	}
};

export type CronJobData = Pick<
	CronEntity,
	'appId' | 'cron' | 'cronBase' | 'cronStatus' | 'refId' | 'refType'
> & {
	jobId: string;
	type: string;
};

export const BULLMQ_CRON_QUEUE = 'crons';

export const generateCronJobId = ({
	appId,
	refId,
	refType
}: Pick<CronEntity, 'appId' | 'refId' | 'refType'>) => {
	return `app_${appId.replace('app:', '')}-${refType}_${refId}`;
};

export async function removeCronJobFromRedis({
	appId,
	refId,
	refType,
	queue
}: Pick<CronEntity, 'appId' | 'refId' | 'refType'> & { queue: Queue }) {
	const jobId = generateCronJobId({ appId, refId, refType });

	await queue.removeJobScheduler(jobId);
}

export async function addCronJobToRedis({
	appId,
	refId,
	refType,
	queue,
	cron,
	cronStatus
}: Pick<CronEntity, 'appId' | 'refId' | 'refType' | 'cron' | 'cronStatus'> & {
	queue: Queue;
}) {
	if (cron) {
		const jobId = generateCronJobId({ appId, refId, refType });

		await removeCronJobFromRedis({ appId, queue, refId, refType });

		await queue.upsertJobScheduler(
			jobId,
			{ pattern: cron },
			{
				data: {
					appId,
					cron,
					cronStatus,
					refId,
					refType
				},
				opts: { removeOnComplete: { count: 8 }, removeOnFail: { count: 8 } }
			}
		);
	}
}
