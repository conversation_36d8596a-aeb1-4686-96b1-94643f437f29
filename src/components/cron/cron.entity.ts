import type { EntityTimestamps } from '@/types/EntityTimestamps.type';

export type CronBase = Partial<{
	status: 'active' | 'inactive';
	every: number;
	current: string;
	minuteValues: number[];
	hourValues: number[];
	dayValues: number[];
	dayOfMonthValues: number[];
	monthValues: number[];
}>;

/**
 * Cron Entity
 *
 *	cronBase - Need "stringify"
 */
export type CronEntity = {
	appId: string;
	refId: string;
	refType: string;
	cron: string;
	cronStatus: 'active' | 'inactive';
	// cronBase: CronBase
	cronBase: string;
} & EntityTimestamps;
