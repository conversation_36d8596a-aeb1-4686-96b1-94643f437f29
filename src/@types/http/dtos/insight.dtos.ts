import { insightFoundationSchema } from '@/types/use-cases/insight.type';
import { z } from 'zod';

export const insightListMetadataRequestSchema = z.object({
	settings: z.object({
		context: z.string(),
		dimension: z.string(),
		dimensionValue: z.array(z.string()),
		measure: z.string(),
		period: z.unknown(),
		signal: z.string(),
		type: z.array(z.unknown())
	})
});

export const insightListPartRequestSchema = z.object({
	appId: z.string(),
	databaseName: z.string(),
	foundation: insightFoundationSchema,
	repoId: z.string(),
	tableName: z.string()
});

export type InsightListMetadataRequestDTO = z.infer<
	typeof insightListMetadataRequestSchema
>;

export type InsightListPartRequestDTO = z.infer<
	typeof insightListPartRequestSchema
>;
