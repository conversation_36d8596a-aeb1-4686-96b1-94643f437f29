import type { PasswordDataType } from '@/components/user/use-cases/user.update';
import type { UserOptionsType, UserType } from '@gaio/shared/types';

export type UserUpdatePasswordRequestDTO = {
	passwordData: PasswordDataType;
};

export type UserUpdateOptionsRequestDTO = {
	options: UserOptionsType;
};

export type UserBulkSaveRequestDTO = {
	users: UserType[];
};

export type UserResetTwoFactorRequestDTO = {
	userId: string;
};

export type UserDeleteUserRequestDTO = {
	userId: string;
};
