import type { AppOptionsType, FormType, ParamType } from '@gaio/shared/types';
import { z } from 'zod';

export type AppInitRequestDTO = {
	appId: string;
};

export const appSaveObjectSchema = z
	.object({
		appDescription: z.string().nullable().optional(),
		appName: z.string().optional(),
		forms: z.array(z.unknown()).optional(), // '[]',
		options: z.object({}).passthrough(),
		params: z.array(z.unknown()).optional(), //'[]',
		repoId: z.string().trim().min(1)
	})
	.passthrough();

export const appSaveRequestSchema = z
	.object({
		app: appSaveObjectSchema
	})
	.passthrough();

export type AppSaveObject = z.infer<typeof appSaveObjectSchema>;

export type AppSaveRequestDTO = z.infer<typeof appSaveRequestSchema>;

export type AppListRequestDTO = {
	appIds: string[];
};

export type AppRenewFlowKeyRequestDTO = {
	appId: string;
	flowId: string;
};

export type AppUpdateOptionsRequestDTO = {
	appId: string;
	options: AppOptionsType;
};

export type AppUpdateParamsRequestDTO = {
	appId: string;
	params: Array<ParamType>;
};

export type AppUpdateFormsRequestDTO = {
	appId: string;
	form: Array<FormType>;
};

export type AppDeleteRequestDTO = {
	appId: string;
};
