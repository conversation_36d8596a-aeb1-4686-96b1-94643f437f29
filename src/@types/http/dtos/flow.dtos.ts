import type { FlowType } from '@gaio/shared/types';

export type SchedulesList = Array<{ appId: string; flowId: string }>;

export type FlowSaveRequestDTO = { flowData: FlowType };

export type FlowSaveSchedulesRequestDTO = { schedulesList: SchedulesList };

export type FlowListRequestDTO = { appId: string };

export type FlowRemoveRequestDTO = { flowId: string; appId: string };

export type FlowCloneRequestDTO = { flowId: string; appId: string };

export type FlowRenewFlowKeyRequestDTO = { flowId: string; appId: string };

export type FlowUpdateOrderRequestDTO = { flowList: string[]; appId: string };
