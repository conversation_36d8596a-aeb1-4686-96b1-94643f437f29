import type { MetaEntity } from '@/db/entities';
import type { AppType } from '@gaio/shared/types';

export type DiscoveryListResponseDTO = Partial<AppType>;

export type DiscoveryMetaData = Partial<
	MetaEntity & {
		cron: string;
		cronBase: string;
		cronStatus: 'active' | 'inactive';
	}
>;

export type DiscoverySaveMetaRequestDTO = {
	metaData: DiscoveryMetaData;
};

export type DiscoveryUpdateHitsRequestDTO = {
	metaId: string;
};
