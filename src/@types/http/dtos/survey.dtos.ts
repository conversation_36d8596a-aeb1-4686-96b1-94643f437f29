import type {
	SurveyData,
	SurveyDataContent
} from '@/components/survey/survey.core';

export type SurveySaveRequestDTO = {
	data: SurveyData;
	status: string;
	token: string;
	appId: string;
};

export type SurveyUpdateStatusRequestDTO = {
	token: string;
	status: string;
};

export type SurveySubmitRequestDTO = {
	appId: string;
	repoId: string;
	content: SurveyDataContent[];
	databaseName: string;
	tableName: string;
	uniqueId: string;
	uniqueIdField: string;
};
