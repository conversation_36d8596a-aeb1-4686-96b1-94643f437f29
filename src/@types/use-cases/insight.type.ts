import { z } from 'zod';

export const insightFoundationSchema = z.object({
	colDate: z.string().optional(),
	colNumber: z
		.string()
		.or(
			z.object({
				label: z.string().optional(),
				left: z.string(),
				operator: z.enum(['/', '*', '=']),
				right: z.string()
			})
		)
		.optional(),
	colPart: z.string().optional(),

	colText: z.string().optional(),

	colTextValue: z.string().optional(),

	dateColumn: z.string().optional(),

	dateRef: z.string().optional(),

	dateStart: z.unknown().optional(),
	deleteData: z.boolean(),

	dimension: z.string().optional(),
	dimensionValue: z.string().optional(),

	filters: z.array(z.unknown()),
	growthPercentage: z.number().optional(),
	measure: z.string().optional(),
	measureType: z.string(),
	nextLevel: z.string().optional(),
	period: z.string()
});

export type InsightFoundation = z.infer<typeof insightFoundationSchema>;
