import { cronSchedulesBootstrap } from '@/components/cron/cron.schedules.bootstrap';
import { websocket } from '@/server/api';
import { app } from '@/server/app';
import { env } from '@/utils/env';
import { printHeapStats } from '@/utils/helpers';

async function bootstrap() {
	try {
		Bun.serve({
			fetch: app.fetch,
			port: env.PORT || 3000,
			reusePort: true,
			websocket,
			idleTimeout: 255,
			maxRequestBodySize: 999999999999999 // 1TB
		});

		console.info(`Server running on port: ${env.PORT || 3000}`);
		printHeapStats();

		console.info('[CRON] Initializing cron worker');
		await cronSchedulesBootstrap();

		return;
	} catch (err) {
		console.error('Error on bootstrap', err);
	}
}

await bootstrap();
