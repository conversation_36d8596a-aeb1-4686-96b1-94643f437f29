import aiFlowRoutes from '@/components/ai-flow/ai-flow.routes';
import aiManager from '@/components/ai-manager/ai-manager.routes';
import aiThreadRoutes from '@/components/ai-thread/ai-thread.routes';
import apiHookRoutes from '@/components/api-hook/api-hook.routes';
import appRoutes from '@/components/app/app.routes';
import authRoutes from '@/components/auth/auth.routes';
import gaioEcosystemRoutes from '@/components/gaio-ecosystem/gaio-ecosystem.routes';
import automlRoutes from '@/components/automl/automl.routes';
import backupRoutes from '@/components/backup/backup.routes';
import builderRoutes from '@/components/builder/builder.routes';
import codeSnippetsRoutes from '@/components/code-snippet/code-snippet.routes';
import commanderRoutes from '@/components/commander/commander.routes';
import contentRoutes from '@/components/content/content.routes';
import discoveryRoutes from '@/components/discovery/discovery.routes';
import flowRoutes from '@/components/flow/flow.routes';
import healthRoutes from '@/components/health/health.routes';
import insightRoutes from '@/components/insight/insight.routes';
import mapsRoutes from '@/components/maps/maps.routes';
import pythonRoutes from '@/components/python/python.routes';
import repoRoutes from '@/components/repo/repo.routes';
import restfulApiRoutes from '@/components/restful-api/restful-api.routes';
import settingsRoutes from '@/components/setting/setting.routes';
import sourceRoutes from '@/components/source/source.routes';
import { subCore } from '@/components/subscribe/sub.core';
import surveyRoutes from '@/components/survey/survey.routes';
import { systemRoutes } from '@/components/system/system.routes';
import tableRoutes from '@/components/table/table.routes';
import taskRoutes from '@/components/task/task.routes';
import timeCapsuleRoutes from '@/components/time-capsule/time-capsule.routes';
import userRoutes from '@/components/user/user.routes';
import jwtGuard from '@/server/middleware/jwt.guard';
import { env } from '@/utils/env';
import { apiReference } from '@scalar/hono-api-reference';
import type { ServerWebSocket } from 'bun';
import { Hono } from 'hono';
import { openAPISpecs } from 'hono-openapi';
import { createBunWebSocket, serveStatic } from 'hono/bun';
import { HTTPException } from 'hono/http-exception';
import { secureHeaders } from 'hono/secure-headers';
import { timing } from 'hono/timing';

const api = new Hono();

const { upgradeWebSocket, websocket } = createBunWebSocket<ServerWebSocket>();
api.use(timing());
// Serve user profile pictures
api.use(
	'/content/user/*',
	serveStatic({
		onNotFound: (path) => {},
		rewriteRequestPath: (path) => {
			return path.replace(/^\/api/, '');
		},
		root: './'
	})
);

api.get('/ws/:channel', jwtGuard('dev').isAuth, upgradeWebSocket(subCore));

const secureApp = new Hono();
const noSecureApp = new Hono();
if (env.NODE_ENV !== 'DEV')
	secureApp.use(secureHeaders({ crossOriginResourcePolicy: false }));

// * ROUTING

secureApp.route('/app', appRoutes);
secureApp.route('/repo', repoRoutes);
secureApp.route('/flow', flowRoutes);
secureApp.route('/commander', commanderRoutes);
secureApp.route('/user', userRoutes);
secureApp.route('/discovery', discoveryRoutes);
secureApp.route('/task', taskRoutes);
secureApp.route('/table', tableRoutes);
secureApp.route('/builder', builderRoutes);
secureApp.route('/settings', settingsRoutes);
secureApp.route('/source', sourceRoutes);
secureApp.route('/content', contentRoutes);
secureApp.route('/code-snippet', codeSnippetsRoutes);
secureApp.route('/hook', apiHookRoutes);
secureApp.route('/survey', surveyRoutes);
secureApp.route('/time-capsule', timeCapsuleRoutes);
secureApp.route('/insight', insightRoutes);
secureApp.route('/automl', automlRoutes);
secureApp.route('/python', pythonRoutes);
noSecureApp.route('/auth', authRoutes);
noSecureApp.route('/gaio-ecosystem', gaioEcosystemRoutes);
noSecureApp.route('/rest', restfulApiRoutes);
noSecureApp.route('/health', healthRoutes);
noSecureApp.route('/ai-manager', aiManager);
noSecureApp.route('/ai-flow', aiFlowRoutes);
noSecureApp.route('/ai-thread', aiThreadRoutes);
noSecureApp.route('/maps', mapsRoutes);
noSecureApp.route('/backup', backupRoutes);
noSecureApp.route('', systemRoutes);

api.route('/', secureApp);
api.route('/', noSecureApp);

// * GLOBAL ERROR HANDLING
api.notFound((c) => {
	// c.status(404)
	return c.json(
		{
			details: 'Endpoint not found',
			timestamp: new Date().toISOString()
		},
		{ status: 404 }
	);
});

api.onError(async (err, c) => {
	console.error('Error log from api.ts - ', err);
	if (env.SENTRY_KEY) {
		c.get('sentry').captureException(err);
	}

	if (err instanceof HTTPException) {
		return c.json(
			{
				details: err.cause,
				message: err.message,
				stack: env.NODE_ENV !== 'PRODUCTION' ? err.stack : null,
				status: err.status,
				timestamp: new Date().toISOString()
			},
			{ status: err.status }
		);
	}

	const message = err.message || 'Internal Server Error';

	return c.json(
		{
			details: err.cause,
			message: message,
			stack: env.NODE_ENV !== 'PRODUCTION' ? err.stack : null,
			timestamp: new Date().toISOString()
		},
		{ status: 500 }
	);
});

// * OPENAPI
api.get(
	'/openapi',
	openAPISpecs(api, {
		documentation: {
			info: { title: 'Gaio DataOS API', version: 'v0.0.indev' },
			servers: [
				{ description: 'Local Server', url: 'http://localhost:3000/api' },
				{ description: 'Development Server', url: 'https://dev.gaio.io' }
			]
		}
	})
);

// * Scalar API Reference
api.get(
	'/docs',
	apiReference({
		theme: 'saturn'
	})
);

export { api, websocket };
