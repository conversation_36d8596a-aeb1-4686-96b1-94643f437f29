import { env } from '@/utils/env';
import { sentry } from '@hono/sentry';
import { Hono } from 'hono';
import { serveStatic } from 'hono/bun';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { prettyJSON } from 'hono/pretty-json';
import { api } from './api';

const app = new Hono();

app.use(cors());
app.use(logger());
app.use(prettyJSON());

if (env.SENTRY_KEY) {
	app.use(
		'*',
		sentry({
			dsn: env.SENTRY_KEY,
			tracesSampleRate: env.SENTRY_TRACES_SAMPLE_RATE
		})
	);
}

//! The order of registration is IMPORTANT
//* Register api routes to the app through "/api" base path
app.route('/api', api);

//* Register static files to the app
app.get('/.env', (c) => c.notFound());
app.use('/assets/*', serveStatic({ root: './dist' }));
app.use('/*', serveStatic({ root: './dist' }));
app.get('*', serveStatic({ path: './dist/index.html' }));

export { app };
