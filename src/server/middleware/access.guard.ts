import type { SignInJWTPayload } from '@/components/auth/auth.core';
import UserRepository from '@/components/user/user.repository';
import type { Context, Next } from 'hono';
import { HTTPException } from 'hono/http-exception';

type AccessCheckDTO = {
	checkType: 'body' | 'path';
	checkLevel: 'soft' | 'hard';
};

/**
 *
 * @param object - `{ checkType, checkLevel }`
 * @description This middleware checks if the user has access to the app
 * - `checkType`: 'body' | 'path' - checks if the appId is in the body or path
 * - `checkLevel`: 'soft' | 'hard' - checks if the user has access to the app in the access token or in the database
 *
 */
export default function accessCheck(
	{ checkType, checkLevel }: AccessCheckDTO = {
		checkLevel: 'soft',
		checkType: 'body'
	}
) {
	return {
		hasAppsAccess: async (c: Context, next: Next) => {
			try {
				if (checkType === 'body') {
					const { userId, permissions } = c.get(
						'tokenPayload'
					) as SignInJWTPayload;


					if (!permissions || !userId) {
						throw new HTTPException(401, {
							cause: 'unauthorized',
							message: 'Unauthorized'
						});
					}

					if (c.req.method === 'GET' || c.req.method === 'DELETE') next();

					const body = await c.req.json();

					if (!Object.hasOwn(body, 'appId')) {
						throw new HTTPException(401, {
							cause: 'unauthorized',
							message: 'Unauthorized'
						});
					}

					// Looks at the access token
					if (checkLevel === 'soft') {
						const userAccessAppIds = permissions.map((permission) =>
							permission.split('::')[2].replace('appId', '')
						);

						if (
							!userAccessAppIds.includes(body['appId'].replace('appId', ''))
						) {
							throw new HTTPException(401, {
								cause: 'unauthorized',
								message: 'Unauthorized'
							});
						}

						return next();
					}

					// Looks at the database
					if (checkLevel === 'hard') {
						const userAppTags = await UserRepository.getUserTags(userId);
						const userDatabaseInfoAccessAppIds = userAppTags.map((tag) =>
							tag.refId.replace('appId', '')
						);

						if (
							!userDatabaseInfoAccessAppIds.includes(
								body['appId'].replace('appId', '')
							)
						) {
							throw new HTTPException(401, {
								cause: 'unauthorized',
								message: 'Unauthorized'
							});
						}

						return next();
					}
				}

				if (checkType === 'path') {
					const appId = c.req.param('appId');

					if (!appId) {
						throw new HTTPException(401, {
							cause: 'missingAppIdInPath',
							message: 'Missing AppId in path'
						});
					}

					const { userId, permissions } = c.get(
						'tokenPayload'
					) as SignInJWTPayload;

					if (!permissions || !userId) {
						throw new HTTPException(401, {
							cause: 'unauthorized',
							message: 'Unauthorized'
						});
					}

					// Looks at the access token
					if (checkLevel === 'soft') {
						const userAccessAppIds = permissions.map((permission) =>
							permission.split('::')[2].replace('app:', '')
						);

						if (!userAccessAppIds.includes(appId.replace('appId', ''))) {
							throw new HTTPException(401, {
								cause: 'unauthorized',
								message: 'Unauthorized'
							});
						}

						return next();
					}

					// Looks at the database
					if (checkLevel === 'hard') {
						const userAppTags = await UserRepository.getUserTags(userId);
						const userDatabaseInfoAccessAppIds = userAppTags.map((tag) =>
							tag.refId.replace('app:', '')
						);

						if (
							!userDatabaseInfoAccessAppIds.includes(appId.replace('app:', ''))
						) {
							throw new HTTPException(401, {
								cause: 'unauthorized',
								message: 'Unauthorized'
							});
						}

						return next();
					}

					return next();
				}

				next();
			} catch (err) {
				if (err instanceof HTTPException) {
					throw err;
				}

				throw new HTTPException(500, {
					cause: err,
					message: 'Internal Server Error [Access Middleware]'
				});
			}
		}
	};
}
