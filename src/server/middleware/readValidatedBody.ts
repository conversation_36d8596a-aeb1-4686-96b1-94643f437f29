import type { UserType } from '@gaio/shared/types';
import type { Context } from 'hono';
import { HTTPException } from 'hono/http-exception';
import type { z } from 'zod';

export async function readValidatedBody<TZodSchema extends z.ZodType<object>>(
	c: Context,
	schema: TZodSchema
) {
	const user = c.get('user') as UserType;

	const methodsSupported = ['POST', 'PUT', 'PATCH'];

	if (!methodsSupported.includes(c.req.method)) {
		throw new HTTPException(400, {
			message: `The ${c.req.method} method does not support a request body.`
		});
	}

	const jsonBody = (await c.req.json()) as z.infer<typeof schema>;

	const { success, data: _data, error } = await schema.safeParseAsync(jsonBody);

	if (!success) {
		throw new HTTPException(400, {
			cause: error.format(),
			message: 'Invalid request body'
		});
	}

	const data = _data as z.infer<typeof schema>;

	return { user, ...data };
}
