import { validateAccessToken } from '@/components/auth/auth.core';
import UserRepository from '@/components/user/user.repository';
import type { Context, Next } from 'hono';
import { HTTPException } from 'hono/http-exception';

const rolesAccess = {
	admin: ['user', 'dev', 'admin'],
	dev: ['dev', 'user'],
	user: ['user'],
	portal: ['user']
};

export default function guardLevel(level?: 'admin' | 'dev' | 'user') {
	return {
		isAccessTokenValid: async (c: Context, next: Next) => {
			try {
				let headerToken = c.req.header('Authorization');

				if (!headerToken) {
					const token = c.req.query('token');

					if (token) {
						headerToken = `Bearer ${token}`;
					} else {
						throw new HTTPException(401, {
							cause: 'invalidAuthorizationHeader',
							message: 'Make sure to use the Authorization header'
						});
					}
				}

				const jwt = headerToken.split(' ')[1];
				if (!jwt) {
					throw new HTTPException(401, {
						cause: 'invalidBearerType',
						message: 'Use Bearer Authorization'
					});
				}

				const tokenData = await validateAccessToken(jwt);
				if (!tokenData) {
					throw new HTTPException(401, {
						cause: 'invalidBearerToken',
						message: 'Token Invalid'
					});
				}

				c.set('apiKeyId', tokenData.apiKeyId);
				c.set('appId', tokenData.appId);

				await next();
			} catch (err) {
				if (err instanceof HTTPException) {
					throw err;
				}

				throw new HTTPException(500, {
					cause: err,
					message: 'Internal Server Error'
				});
			}
		},
		isAuth: async (c: Context, next: Next) => {
			try {
				let headerToken = c.req.header('Authorization');

				if (!headerToken) {
					const token = c.req.query('token');

					if (token) {
						headerToken = `Bearer ${token}`;
					} else {
						const baseCookies = c.req.header('Cookie');

						if (!baseCookies) {
							throw new HTTPException(401, {
								cause: 'invalidAuthorizationHeader',
								message: 'Missing Cookie Authorization header'
							});
						}

						const cookies = baseCookies.split('; ').map((cookie) => {
							const [name, ...rest] = cookie.split('=');
							return [name, rest.join('=')];
						});
						
						const authCookie = cookies.find(
							([name]) => name === 'gaio@token'
						)[1];

						if (authCookie) {
							headerToken = `Bearer ${authCookie}`;
						} else {
							throw new HTTPException(401, {
								cause: 'invalidAuthorizationHeader',
								message: 'Make sure to use the Authorization header'
							});
						}
					}
				}

				const jwt = headerToken.split(' ')[1];
				if (!jwt) {
					throw new HTTPException(401, {
						cause: 'invalidBearerType',
						message: 'Use Bearer Authorization'
					});
				}

				const tokenData = await validateAccessToken(jwt);
				if (!tokenData) {
					throw new HTTPException(401, {
						cause: 'invalidBearerToken',
						message: 'Token Invalid'
					});
				}

				const { userId } = tokenData;

				const user = await UserRepository.getUserById(userId);
				if (!user) {
					throw new HTTPException(401, {
						cause: 'userNotFound',
						message: 'User not found'
					});
				}

				if (level && rolesAccess[user.role].indexOf(level) === -1) {
					throw new HTTPException(401, {
						cause: 'invalidAccessLevel',
						message: 'Unauthorized'
					});
				}

				const logFrom = ['studio', 'preview', 'map'].includes(
					c.req.header('logfrom')
				)
					? 'studio'
					: 'dashboard';

				c.set('sessionid', tokenData.sessionid);
				c.set('logFrom', logFrom);

				// * set token payload on context for further use
				c.set('user', user);
				c.set('tokenPayload', tokenData);

				await next();
			} catch (err) {
				if (err instanceof HTTPException) {
					throw err;
				}

				throw new HTTPException(500, {
					cause: err,
					message: 'Internal Server Error'
				});
			}
		}
	};
}
