import { heapStats } from 'bun:jsc'
import { writeFile } from 'node:fs'
import type {
	FieldType,
	GenericType,
	TaskContextType
} from '@gaio/shared/types'
import { getBucketNameFromAppId, replacement } from '@gaio/shared/utils'
import dayjs from 'dayjs'
import { BasicConnectionInfoDTO } from '@/db/db.service'

export const currentDatetime = () => new Date().toISOString().slice(0, 19)
export const currentDatetime64 = () =>
	new Date().toISOString().replace('Z', '').slice(0, 19)
export const currentDate = () => new Date().toISOString().slice(0, 10)
export const clickDate = (dt: Date | string) =>
	dayjs(dt).format('YYYY-MM-DDTHH:MM:ss')

export function fixBadDataAtJson(chunk: Record<string, unknown>) {
	for (const [i] of Object.entries(chunk)) {
		if (chunk[i]) {
			if (chunk[i] === 'NULL') {
				chunk[i] = null
			} else {
				chunk[i] = `${chunk[i]}`
					.replace(/\\t/g, ' ')
					.replace(/\t/g, ' ')
					.replace(/\\r/g, ' ')
					.replace(/\r/g, ' ')
					.replace(/\n/g, ' ')
					.replace(/\\n/g, ' ')
					.replace(/</g, '')
					.replace(/>/g, '')
					.replace(/"/g, `\\"`)
					.replace(/'/g, `\\'`)
			}
		}
	}

	return chunk
}

export function defineConnectionName({
																			 sourceType,
																			 sourceId,
																			 appId,
																			 repoId,
																			 admin
																		 }: {
	sourceType: string,
	sourceId?: string,
	appId?: string,
	repoId?: string,
	admin?: unknown
}) {
	if (sourceType === 'source') {
		return `database_${sourceId.replace('source:', '')}`
	}

	if (sourceType === 'super') {
		return `database_${repoId.replace('repo:', '')}_super`
	}

	if (admin) {
		return `${getBucketNameFromAppId(appId)}_admin`
	}

	return getBucketNameFromAppId(appId)
}


export async function generateFile(
	path: string,
	data: string,
	opts = {}
): Promise<{ status: string }> {
	return new Promise((resolve, reject) => {
		writeFile(path, data, opts, (err) => {
			if (err) {
				reject(err)
			} else {
				resolve({ status: 'saved' })
			}
		})
	})
}

export const createBashScriptEnvRunner = (
	envKeyName: string,
	scriptFilePath: string
) => {
	// Removed 2>&1, decide later
	return [
		'#!/bin/bash',
		`eval "$(pyenv init -)"`,
		`eval "$(pyenv virtualenv-init -)"`,
		`pyenv activate ${envKeyName}`,
		`output=$(python3 ${scriptFilePath})`,
		`if echo "$output" | grep -q "MemoryError"; then
      echo "MemoryError found in the output"
      pyenv deactivate
      exit 1
      fi
    `,
		'pyenv deactivate'
	].join('\n')
}

export const defineLogComment = (data: GenericType) => {
	const { appId, taskId, userId, type, runningFlowId } = data

	const gaioQt = {
		appId: appId || '',
		flowId: runningFlowId || '',
		ref: 'gaio_qt',
		taskId: taskId || '',
		type: type || '',
		userId: userId || ''
	}

	return {
		queryOptions: {
			log_comment: `${JSON.stringify(gaioQt)}`
		}
	}
}

export function bridgeColumnLength(field: FieldType) {
	if (`${field.columnLength}`.includes(',')) {
		const decimalNumber = field.columnLength
			? field.columnLength?.split(',')
			: [2, 2]
		field.columnLength = decimalNumber[decimalNumber.length - 1]
	} else if (`${field.columnLength}`.includes('.')) {
		const decimalNumber = `${field.columnLength}`.split('.')
		field.columnLength = decimalNumber[decimalNumber.length - 1]
	} else {
		field.columnLength = field.columnLength || 2
	}
}

export function arrowDataType(field: FieldType) {
	if (!field.dataType) {
		field.columnLength = null
		field.dataType = 'Nullable(String)'
	} else if (field.dataType.includes('Array')) {
		field.columnLength = null
		return field
	}  else if (field.dataType.toLowerCase().includes('json')) {
		field.columnLength = null
		field.dataType = 'Nullable(JSON)'
		return field
	} else if (field.dataType.includes('Decimal')) {
		bridgeColumnLength(field)
		field.columnLength = field.columnLength || 2
		field.dataType = `Nullable(Decimal64(${field.columnLength || 2}))`
	} else if (field.dataType.includes('Float')) {
		field.columnLength = null
		bridgeColumnLength(field)
		field.dataType = 'Nullable(Float64)'
	} else {
		field.dataType = replacement[field.dataType]
			? replacement[field.dataType]
			: replacement[lowerType(field.dataType)]
				? replacement[lowerType(field.dataType)]
				: 'Nullable(String)'

		if (!field.dataType?.includes('Decimal')) {
			field.columnLength = null
		}
	}
	return field
}

export function lowerType(dataType: string) {
	return dataType
		.toLowerCase()
		.replace(/\(([^)]+)\)/, '')
		.replace('()', '')
		.replace(/\s/g, '')
		.replace('unsigned', '')
		.replace('zerofill', '')
		.trim()
}

export function formatHeapStatsFromBytes(bytes: number) {
	if (bytes >= 1e9) {
		// If greater than or equal to 1 GB
		return `${(bytes / 1e9).toFixed(2)} GB`
	}
	if (bytes >= 1e6) {
		// If greater than or equal to 1 MB
		return `${(bytes / 1e6).toFixed(2)} MB`
	}
	if (bytes >= 1e3) {
		// If greater than or equal to 1 KB
		return `${(bytes / 1e3).toFixed(2)} KB`
	}
	return `${bytes} bytes`
}

export function printHeapStats() {
	const stats = heapStats()

	console.debug('Current Heap Size:', formatHeapStatsFromBytes(stats.heapSize))
	console.debug(
		'Total Heap Capacity:',
		formatHeapStatsFromBytes(stats.heapCapacity)
	)
}

type MetaType = { name: string; type: string };

export const formatDecimalValues = (
	data: GenericType[],
	schemaSelect: FieldType[] | MetaType[]
) => {
	if (!data || !Array.isArray(data)) return data

	return data.map((row) => {
		const formattedRow = { ...row }
		schemaSelect.forEach((field) => {
			const isFieldType = 'dataType' in field
			const fieldType = isFieldType ? field.dataType : field.type
			const columnLength = isFieldType ? field.columnLength : 2
			const key = isFieldType ? field.alias || field.columnName : field.name

			if (fieldType?.includes('Float') || fieldType?.includes('Decimal')) {
				if (key in formattedRow) {
					formattedRow[key] = formatDecimalValue(formattedRow[key], {
						...field,
						columnLength,
						dataType: fieldType
					})
				}
			}
		})
		return formattedRow
	})
}

const formatDecimalValue = (value: unknown, field: FieldType) => {
	// Handle null, undefined, or empty string
	if (value === null || value === undefined || value === '') {
		return null
	}

	// If it's already a number, use it directly
	if (typeof value === 'number' && !Number.isNaN(value)) {
		const decimalSize = field.columnLength || 2
		const multiplier = 10 ** decimalSize
		return Math.floor(value * multiplier) / multiplier
	}

	// If it's a string, try to convert to number
	if (typeof value === 'string') {
		const num = Number(value)
		if (!Number.isNaN(num)) {
			const decimalSize = field.columnLength || 2
			const multiplier = 10 ** decimalSize
			return Math.floor(num * multiplier) / multiplier
		}
	}

	// If we can't convert to a valid number, return null
	return null
}

export const canTableBeTemporary = (logFrom: string) => {
	const options = ['studio']

	return !options.includes(logFrom)
}

export function transformTemporaryName(
	input: string,
	taskContext: TaskContextType,
	isAdmin: boolean
): string {
	if (
		taskContext?.logFrom &&
		canTableBeTemporary(taskContext.logFrom) &&
		!isAdmin
	) {
		const tmpName = `gaio${taskContext.sessionid}`
		return input
			.replace(/\btmp_/g, `tmp_${tmpName}_`)
			.replace(`tmp_${tmpName}_${tmpName}`, `tmp_${tmpName}_`)
	}
	return input
}
