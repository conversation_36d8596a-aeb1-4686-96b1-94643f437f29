import { randomUUID } from 'node:crypto';
import { z } from 'zod';

const envSchema = z.object({
	ACCESS_TOKEN_SECRET: z.string().default(randomUUID()),
	SENTRY_KEY: z.string().optional(),
	SENTRY_TRACES_SAMPLE_RATE: z.coerce.number().default(0).optional(),
	GAIO_REDIS_HOST: z.string(),
	GAIO_REDIS_PORT: z.coerce.number().default(6379),
	H2O_API_BASE_URL: z.string(),
	NODE_ENV: z.enum(['LOCAL', 'DEV', 'TEST', 'PRODUCTION', 'DOCKER']).default('DEV'),
	PORT: z.coerce.number().default(3001),
	REFRESH_TOKEN_SECRET: z.string().default(randomUUID()),
	GAIO_CLICKHOUSE_HOST: z.string(),
	GAIO_CLICKHOUSE_PORT: z.coerce.number().default(8123),
	GAIO_CLICKHOUSE_DATABASE: z.string().default('gaioadmin'),
	GAIO_CLICKHOUSE_USER: z.string().default('default'),
	GAIO_CLICKHOUSE_PASSWORD: z.string(),

	GAIO_CLICKHOUSE_SSL: z.boolean().default(false),
	GAIO_ECOSYSTEM_URL: z.string().default('http://localhost:3000'),
	GAIO_ECOSYSTEM_API_KEY: z.string().optional(),
	GAIO_ECOSYSTEM_TIMEOUT: z.coerce.number().default(15000),
	GAIO_LICENSE_SERVICE_URL: z.string().default('http://localhost:4000'),
	GAIO_LICENSE_SERVICE_TIMEOUT: z.coerce.number().default(15000)
});

const _env = envSchema.safeParse(process.env);

if (_env.success === false) {
	console.error('❌ Invalid environment variables', _env.error.format());

	throw new Error('Invalid environment variables');
}

export const env = _env.data;
