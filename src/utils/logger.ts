import type { Logger, LoggerOptions } from 'pino';
import pino from 'pino';

function createLogger(options: LoggerOptions = {}): Logger {
	const defaultOptions: LoggerOptions = {
		browser: {
			asObject: true
		},
		// Disable thread-stream by setting level higher than needed
		level: 'info'
	};

	return pino({ ...defaultOptions, ...options });
}

const logger = createLogger();

export default logger;
