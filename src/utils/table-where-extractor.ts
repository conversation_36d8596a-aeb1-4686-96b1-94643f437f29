import { QueryBuilder } from '@/components/builder/use-cases/builder';
import type {
	BuilderTaskType,
	SchemaFilterType,
	TaskBaseType,
	TaskType
} from '@gaio/shared/types';

export async function tableWhereExtractor(
	taskData: TaskType,
	filters: SchemaFilterType[]
) {
	if (!filters) {
		return '';
	}
	const q = await new QueryBuilder().generate(
		{
			...taskData,
			schema: {
				filter: filters,
				select: []
			}
		},
		[]
	);

	const where = q.match(/where\s(.*?)(?=\sgroup\sby|\sorder\sby|\slimit|$)/g);
	return (where?.[0] ? where[0] : '') as string;
}

export async function getFilters(
	taskData: BuilderTaskType,
	filter: SchemaFilterType[]
) {
	if (!filter || !filter.length || !filter[0].list || !filter[0].list.length) {
		return '';
	}
	const q = await new QueryBuilder().generate(
		{
			...taskData,
			schema: {
				filter,
				select: []
			}
		},
		[]
	);

	const where = q.match(/where\s(.*?)(?=\sgroup\sby|\sorder\sby|\slimit|$)/g);
	return (where?.[0] ? where[0] : '') as string;
}
