import type {
	AssociationRulesTaskType,
	AutoMLTaskType,
	ClusterTaskType,
	ForecastTaskType,
	PcaTaskType
} from '@gaio/shared/types';

import {
	dropTableIfExist,
	executeQuery,
	modelsFolder
} from '@/components/task/use-cases/runners/runner.tools';
import type { CredentialsType } from '@gaio/shared/types';
import { getBucketNameFromAppId } from '@gaio/shared/utils';

export const associationRulesScript = (
	repoData: CredentialsType,
	taskData: AssociationRulesTaskType,
	logComment: string
) => {
	return `
import resource
import platform
import sys
import os
import json

import pandas as pd
from mlxtend.preprocessing import TransactionEncoder
from mlxtend.frequent_patterns import association_rules, fpgrowth, apriori
import clickhouse_connect

import psutil
import functools

def memory_limit(limit=0.8):
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            process = psutil.Process()
            start_mem = process.memory_info().rss
            func(*args, **kwargs)
            end_mem = process.memory_info().rss
            mem_usage = end_mem - start_mem
            if mem_usage > limit * psutil.virtual_memory().total:
                raise MemoryError(f"MemoryError: Memory usage exceeded {limit*100:.2f}%")
        return wrapper
    return decorator

@memory_limit()
def main():
    import json  # Ensure json is imported in this scope
    
    g_host="${repoData.host}"
    g_username="${repoData.user}"
    g_password="${repoData.password}"
    g_port=${repoData.port || '8123'}
    g_database="${getBucketNameFromAppId(taskData.appId)}"
    g_table_name="${taskData.tableName}"
    g_result_name="${taskData.resultTable}"
    g_transaction="${taskData.columnReference}"
    g_item="${taskData.columnCategory}"
    g_support=${taskData.minSupport}
    g_min_threshold=${taskData.minThreshold}
    g_association_type="${taskData.associationType || ''}"

    if g_association_type == 'apriori':
        g_association_type = apriori
    else:
        g_association_type = fpgrowth

    db = clickhouse_connect.get_client(
        host=g_host,
        username=g_username,
        password=g_password,
        port=g_port,
        database=g_database,
        ${logComment}
    )

    def extract_values(obj):
        values = []
        for value in obj:
            values.append(str(value))
        return ', '.join(values)

    df = db.query_df(f"select * from {g_table_name} where {g_transaction} is not null and {g_item} is not null")
    id_transaction = g_transaction
    id_item = g_item
    type_association_rules = g_association_type

    transactions = df.groupby(id_transaction)[id_item].apply(list).values.tolist()

    te = TransactionEncoder()
    te_ary = te.fit_transform(transactions)
    encoded_df = pd.DataFrame(te_ary, columns=te.columns_)

    frequent_itemsets = type_association_rules(
        encoded_df, min_support=g_support, use_colnames=True
    )
    rules = association_rules(frequent_itemsets, metric="confidence", min_threshold=g_min_threshold)
    rules = rules.rename(columns=lambda x: x.replace(" ", "_"))
    rules[['antecedents', 'consequents']] = rules[['antecedents', 'consequents']].map(lambda x: extract_values(x))


    def map_data_type(dtype):
        # Map the Pandas data type to ClickHouse type
        type_mapping = {
            "Int64": "Nullable(Int64)",
            "float64": "Nullable(Float64)",
        }
        clickhouse_type = type_mapping.get(str(dtype))
        return clickhouse_type if clickhouse_type else "Nullable(String)"


    column_names = rules.columns.tolist()
    column_types = rules.dtypes.map(map_data_type).tolist()

    # Dropando tabela se ela já existir
    table_name = g_result_name
    drop_table_query = f"DROP TABLE IF EXISTS {table_name}"
    db.command(drop_table_query)

    # Criando tabela que vai receber o insert
    create_table_query = f"CREATE TABLE {table_name} ("
    column_definitions = [
        f"{name} {ctype}" for name, ctype in zip(column_names, column_types)
    ]
    create_table_query += ", ".join(column_definitions)
    create_table_query += ") ENGINE = MergeTree() ORDER BY tuple();"
    db.command(create_table_query)

    rules_str = rules.astype(str)
    db.insert_df(table_name, rules_str)

main()
`;
};

export function pcaScript(
	repoData: CredentialsType,
	taskData: PcaTaskType,
	logComment: string
) {
	const ignore =
		taskData.excludeColumns.length <= 0
			? ''
			: `ignored_columns=['${taskData.excludeColumns.join("','")}'],`;
	return `import resource
import platform
import sys
import os
import json

import pandas as pd
import clickhouse_connect

import psutil
import functools

import h2o
from h2o.estimators import H2OPrincipalComponentAnalysisEstimator
h2o.init()

def memory_limit(limit=0.8):
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            process = psutil.Process()
            start_mem = process.memory_info().rss
            func(*args, **kwargs)
            end_mem = process.memory_info().rss
            mem_usage = end_mem - start_mem
            if mem_usage > limit * psutil.virtual_memory().total:
                raise MemoryError(f"MemoryError: Memory usage exceeded {limit*100:.2f}%")
        return wrapper
    return decorator

@memory_limit()
def main():
    import json  # Ensure json is imported in this scope
    
    g_host="${repoData.host}"
    g_username="${repoData.user}"
    g_password="${repoData.password}"
    g_port=${repoData.port || '8123'}
    g_database="${getBucketNameFromAppId(taskData.appId)}"
    g_table_name="${taskData.tableName}"
    g_result_name="${taskData.resultTable}"
    g_k=${Number(taskData.k) || 5}
    g_impute_missing="${taskData.imputeMissing}"
    g_exclude_columns=${taskData.excludeColumns.join("','") || "''"}

    db = clickhouse_connect.get_client(
        host=g_host,
        username=g_username,
        password=g_password,
        port=g_port,
        database=g_database,
        ${logComment}
    )

    data = db.query_df(f"select * from {g_table_name}")
    data_h2o = h2o.H2OFrame(data)
    train, valid = data_h2o.split_frame(ratios=[.7], seed=1234)

    model = H2OPrincipalComponentAnalysisEstimator(
    k = g_k,
    use_all_factor_levels = True,
    ${ignore}
    pca_method = "glrm",
    seed = 1234,
    transform = "standardize",
    impute_missing = ${taskData.imputeMissing ? 'True' : 'False'}
    )

    model.train(
       training_frame = train
    )

    scoring = model.predict(data_h2o)

    def map_data_type(dtype):
        # Map the Pandas data type to ClickHouse type
        type_mapping = {
            "Int64": "Nullable(Int64)",
            "float64": "Nullable(Float64)",
        }
        clickhouse_type = type_mapping.get(str(dtype))
        return clickhouse_type if clickhouse_type else "Nullable(String)"

    scoring_h20_to_df = scoring.as_data_frame(use_multi_thread=True)
    scoring_df = pd.concat([data, scoring_h20_to_df], axis=1)
    column_names = scoring_df.columns.tolist()
    column_types = scoring_df.dtypes.map(map_data_type).tolist()

    # Dropando tabela se ela já existir
    table_name = g_result_name
    drop_table_query = f"DROP TABLE IF EXISTS {table_name}"
    db.command(drop_table_query)

    # Criando tabela que vai receber o insert
    create_table_query = f"CREATE TABLE {table_name} ("
    column_definitions = [
        f"{name} {ctype}" for name, ctype in zip(column_names, column_types)
    ]
    create_table_query += ", ".join(column_definitions)
    create_table_query += ") ENGINE = MergeTree() ORDER BY tuple();"
    db.command(create_table_query)

    scoring_str = scoring_df.astype(str)
    db.insert_df(table_name, scoring_str)

main()`;
}

export function clusterScript(
	repoData: CredentialsType,
	taskData: ClusterTaskType,
	logComment: string
) {
	const ignore =
		taskData.excludeColumns.length <= 0
			? ''
			: `ignored_columns=['${taskData.excludeColumns.join("','")}'],`;
	return `import resource
import platform
import sys
import os
import json

import pandas as pd
import clickhouse_connect

import psutil
import functools
import h2o
from h2o.estimators import H2OKMeansEstimator
h2o.init(strict_version_check=False)

def memory_limit(limit=0.8):
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            process = psutil.Process()
            start_mem = process.memory_info().rss
            func(*args, **kwargs)
            end_mem = process.memory_info().rss
            mem_usage = end_mem - start_mem
            if mem_usage > limit * psutil.virtual_memory().total:
                raise MemoryError(f"MemoryError: Memory usage exceeded {limit*100:.2f}%")
        return wrapper
    return decorator

@memory_limit()
def main():
    import json  # Ensure json is imported in this scope
    
    g_host="${repoData.host}"
    g_username="${repoData.user}"
    g_password="${repoData.password}"
    g_port=${repoData.port || '8123'}
    g_database="${getBucketNameFromAppId(taskData.appId)}"
    g_table_name="${taskData.tableName}"
    g_result_name="${taskData.resultTable}"


    db = clickhouse_connect.get_client(
        host=g_host,
        username=g_username,
        password=g_password,
        port=g_port,
        database=g_database,
        ${logComment}
    )


    data = db.query_df(f"select * from {g_table_name}")
    data_h2o = h2o.H2OFrame(data)
    train, valid = data_h2o.split_frame(ratios=[.7], seed=1234)
    h2o_km = H2OKMeansEstimator(
                          keep_cross_validation_models=False,
                          seed=1234,
                          ${ignore}
                          init="furthest",
                          estimate_k=${taskData.estimateK ? 'True' : 'False'},
                          k=${taskData.estimateK ? 20 : Number(taskData.clusterSize)},
                          max_runtime_secs=${Number(taskData.executionTime)},
                          standardize=True
                        )
    h2o_km.train(training_frame=train,validation_frame=valid)
    scoring = h2o_km.predict(data_h2o)


    def map_data_type(dtype):
        # Map the Pandas data type to ClickHouse type
        type_mapping = {
            "Int64": "Nullable(Int64)",
            "float64": "Nullable(Float64)",
        }
        clickhouse_type = type_mapping.get(str(dtype))
        return clickhouse_type if clickhouse_type else "Nullable(String)"

    scoring_h20_to_df = scoring.as_data_frame(use_multi_thread=True)
    scoring_df = pd.concat([data, scoring_h20_to_df], axis=1)
    column_names = scoring_df.columns.tolist()
    column_types = scoring_df.dtypes.map(map_data_type).tolist()

    # Dropando tabela se ela já existir
    table_name = g_result_name
    drop_table_query = f"DROP TABLE IF EXISTS {table_name}"
    db.command(drop_table_query)

    # Criando tabela que vai receber o insert
    create_table_query = f"CREATE TABLE {table_name} ("
    column_definitions = [
        f"{name} {ctype}" for name, ctype in zip(column_names, column_types)
    ]
    create_table_query += ", ".join(column_definitions)
    create_table_query += ") ENGINE = MergeTree() ORDER BY tuple();"
    db.command(create_table_query)

    scoring_str = scoring_df.astype(str)
    db.insert_df(table_name, scoring_str)

main()`;
}

export async function forecastScript(
	repoData: CredentialsType,
	taskData: ForecastTaskType,
	logComment: string
) {
	await dropTableIfExist(taskData);
	await dropTableIfExist({
		...taskData,
		resultTable: `pre_${taskData.resultTable}`
	});

	let groups = ['nd'];
	if (taskData.columnGrouped) {
		groups = ((await createClassification(taskData)).data || []).map(
			(o) => o.category
		);
	}

	taskData.posResulTableName = structuredClone(taskData.resultTable);
	taskData.resultTable = `pre_${taskData.resultTable}`;

	let compareGroupColumn = '';
	if (taskData.columnGrouped) {
		compareGroupColumn = ` AND p.category =  r.${taskData.columnGrouped}`;
	}

	return `import resource
import platform
import sys
import logging
import os

import pandas as pd
import clickhouse_connect

import psutil
import functools

from prophet import Prophet
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score, root_mean_squared_error

logging.getLogger('cmdstanpy').setLevel(logging.ERROR)
os.environ['CMDSTAN_QUIET'] = 'True'

def memory_limit(limit=0.8):
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            process = psutil.Process()
            start_mem = process.memory_info().rss
            func(*args, **kwargs)
            end_mem = process.memory_info().rss
            mem_usage = end_mem - start_mem
            if mem_usage > limit * psutil.virtual_memory().total:
                raise MemoryError(f"MemoryError: Memory usage exceeded {limit*100:.2f}%")
        return wrapper
    return decorator

def map_data_type(dtype):
    # Map the Pandas data type to ClickHouse type
    g_freq = "${taskData.freq || 'D'}"
    type_mapping = {
        "Int64": "Nullable(Int64)",
        "float64": "Nullable(Float64)",
        "datetime64[ns]": "Nullable(Date)" if g_freq != 'H' else "Nullable(DateTime)",
    }
    clickhouse_type = type_mapping.get(str(dtype))
    return clickhouse_type if clickhouse_type else "Nullable(String)"

@memory_limit()
def main():
    import json  # Ensure json is imported in this scope
    
    g_host="${repoData.host}"
    g_username="${repoData.user}"
    g_password="${repoData.password}"
    g_port=${repoData.port || '8123'}
    g_database="${getBucketNameFromAppId(taskData.appId)}"
    g_table_name="${taskData.tableName}"
    g_result_name="${taskData.resultTable}"
    g_result_metric_table="${taskData.resultMetricTable}"
    g_column_date="${taskData.columnDate}"
    g_column_measure="${taskData.columnMeasure}"
    g_freq="${taskData.freq || 'D'}"
    g_period=${taskData.periods}
    g_column_grouped="${taskData.columnGrouped || 'nd'}"

    db = clickhouse_connect.get_client(
        host=g_host,
        username=g_username,
        password=g_password,
        port=g_port,
        database=g_database,
        ${logComment}
    )

    db.command(f"""CREATE TABLE IF NOT EXISTS ${taskData.databaseName}.${taskData.resultTable} (
        category Nullable(String),
        ds Nullable(Date),
        yhat Nullable(Float64),
        yhat_lower Nullable(Float64),
        yhat_upper Nullable(Float64))
			ENGINE MergeTree
			ORDER BY tuple()""")


    for group_name in [${groups.map((g) => `"${g}"`).join(', ')}]:
      data = db.query_df(f"""SELECT
	    	{g_column_date} as ds,
	    	{g_column_measure} as y
	    FROM
	    	${taskData.databaseName}.{g_table_name}
	    WHERE
	    	{g_column_date} != '0000-00-00'
	    	AND
	    		{g_column_date} IS NOT NULL
        {'' if group_name == 'nd' else f"AND {g_column_grouped} = '{group_name}'"}""")
        
        
      print(f"""SELECT
	    	{g_column_date} as ds,
	    	{g_column_measure} as y
	    FROM
	    	${taskData.databaseName}.{g_table_name}
	    WHERE
	    	{g_column_date} != '0000-00-00'
	    	AND
	    		{g_column_date} IS NOT NULL
        {'' if group_name == 'nd' else f"AND {g_column_grouped} = '{group_name}'"}""")

      print(f"Data shape: {data.shape}")
      print("Data columns:", data.columns.tolist())
      print("Data types:", data.dtypes)
      print("First few rows:", data.head())
			
      m = Prophet()
      m.fit(data)
      
      future = m.make_future_dataframe(freq=g_freq, periods=g_period)
      
      forecast = m.predict(future)
      forecast['category'] = group_name
      forecast_preview = forecast[['category','ds', 'yhat', 'yhat_lower', 'yhat_upper']]

      column_names = forecast_preview.columns.tolist()
      column_types = forecast_preview.dtypes.map(map_data_type).tolist()

      # Dropando tabela se ela já existir
      table_name = g_result_name
      drop_table_query = f"DROP TABLE IF EXISTS pos_{table_name}"
      db.command(drop_table_query)

      # Criando tabela que vai receber o insert
      create_table_query = f"CREATE TABLE pos_{table_name} ("
      column_definitions = [
          f"{name} {ctype}" for name, ctype in zip(column_names, column_types)
      ]
      create_table_query += ", ".join(column_definitions)
      create_table_query += ") ENGINE = MergeTree() ORDER BY tuple()"

      db.command(create_table_query)

      db.insert_df(table_name, forecast_preview)

    db.command(f"""CREATE TABLE IF NOT EXISTS
				${taskData.databaseName}.${taskData.posResulTableName}
			ENGINE MergeTree
			ORDER BY tuple() AS
				(SELECT
					p.category,
					p.ds AS dt,
					r.${taskData.columnMeasure} AS real,
					CASE
						WHEN
							p.ds > (SELECT max(${taskData.columnDate}) FROM	${taskData.databaseName}.${taskData.tableName})
						THEN 'forecast'
						ELSE 'real'
					end AS type,
					p.yhat AS forecast,
					p.yhat_lower AS lower,
					p.yhat_upper AS upper
				FROM  ${getBucketNameFromAppId(taskData.appId)}.${taskData.resultTable} p
				LEFT JOIN ${taskData.databaseName}.${taskData.tableName} r
				ON p.ds = r.${taskData.columnDate} ${compareGroupColumn})""")

    db.command(f"""DROP TABLE IF EXISTS	${taskData.databaseName}.pos_{table_name}""")
    db.command(f'DROP TABLE IF EXISTS	${taskData.databaseName}.{g_result_metric_table}')

    count_group_index = 0

    for group_name in [${groups.map((g) => `"${g}"`).join(', ')}]:
        if g_result_metric_table:
            forecast_df = db.query_df(f"SELECT * FROM ${taskData.databaseName}.${taskData.posResulTableName} WHERE real IS NOT NULL AND category = '{group_name}'")
            y_true = forecast_df['real']
            y_pred = forecast_df['forecast']
            mse = mean_squared_error(y_true, y_pred)
            rmse = root_mean_squared_error(y_true, y_pred)
            r2 = r2_score(y_true, y_pred)
            mae = mean_absolute_error(y_true, y_pred)
            category = group_name
            
            db.command(f'CREATE TABLE IF NOT EXISTS 	${taskData.databaseName}.{g_result_metric_table} (category Nullable(String), mse Nullable(Float64), mae Nullable(Float64), rmse Nullable(Float64),  r2 Nullable(Float64)) ENGINE MergeTree ORDER BY tuple()')
            db.insert(g_result_metric_table, [[category,mse,mae,rmse,r2]], column_names=['category', 'mse', 'mae', 'rmse', 'r2'])
        count_group_index += 1

main()`;
}

export function autoMLScript(
	repoData: CredentialsType,
	taskData: AutoMLTaskType,
	logComment: string
) {
	const settings = taskData.settings || {};
	const removeColumns = settings.removeColumns || [];
	const projectDir = modelsFolder(taskData.appId, taskData.id);
	const ignore =
		removeColumns.length <= 0
			? ''
			: `ignored_columns=['${removeColumns.join("','")}'],`;

	return `
import resource
import platform
import sys
import os
import json
import pandas as pd
import clickhouse_connect
import psutil
import functools
import h2o
from h2o.automl import H2OAutoML
h2o.init(strict_version_check=False)

def memory_limit(limit=0.8):
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            process = psutil.Process()
            start_mem = process.memory_info().rss
            func(*args, **kwargs)
            end_mem = process.memory_info().rss
            mem_usage = end_mem - start_mem
            if mem_usage > limit * psutil.virtual_memory().total:
                raise MemoryError(f"MemoryError: Memory usage exceeded {limit*100:.2f}%")
        return wrapper
    return decorator

@memory_limit()
def main():
    import json  # Ensure json is imported in this scope
    
    g_host="${repoData.host}"
    g_username="${repoData.user}"
    g_password="${repoData.password}"
    g_port=${repoData.port || '8123'}
    g_database="${getBucketNameFromAppId(taskData.appId)}"
    g_table_name="${settings.trainingFrame || 'default_table'}"
    g_project_name="${settings.projectName || 'h2o_automl_project'}"
    g_project_id="${settings.projectId || ''}"
    g_result_name="${taskData.resultTable}"
    
    # Create model directory if it doesn't exist
    model_dir = "${projectDir}"
    os.makedirs(model_dir, exist_ok=True)
    
    # Create project directory
    project_dir = os.path.join(model_dir, g_project_name)
    os.makedirs(project_dir, exist_ok=True)

    db = clickhouse_connect.get_client(
        host=g_host,
        username=g_username,
        password=g_password,
        port=g_port,
        database=g_database,
        ${logComment}
    )

    # Fetch data from ClickHouse
    query = f"select * from {g_table_name}"
    if ${taskData.limitRows || 0} > 0:
        query += f" LIMIT ${taskData.limitRows}"
    data = db.query_df(query)
    
    # Convert to H2O frame
    data_h2o = h2o.H2OFrame(data)
    
    # Remove ignored columns if specified
    if ${removeColumns.length > 0 ? 'True' : 'False'}:
        data_h2o = data_h2o.drop([${removeColumns.map((col) => `"${col}"`).join(',')}])
    
    # Split data into train/test
    split_ratio = ${settings.splitFrame || 0.8}
    train, test = data_h2o.split_frame(ratios=[split_ratio], seed=${settings.seed || 1234})
    
    # Define target and features
    y = "${settings.responseColumn || ''}"
    if not y:
        raise ValueError("Response column must be specified")
    
    x = [col for col in train.columns if col != y]
    
    # Determine if classification or regression
    problem_type = "classification" if train[y].types[y] in ["enum", "categorical"] else "regression"
    print(f"Detected problem type: {problem_type}")
    
    # Initialize and run AutoML
    aml = H2OAutoML(
        max_runtime_secs=${settings.maxRuntimeSecs || 3600},
        max_models=${settings.maxModels || 20},
        seed=${settings.seed || 1234},
        project_name=g_project_name,
        nfolds=${settings.nfolds || 5},  # Number of folds for cross-validation
        balance_classes=${settings.balanceClasses || 'False'},  # Balance classes for classification
        class_sampling_factors=${settings.classSamplingFactors || 'None'},  # Class sampling factors
        max_after_balance_size=${settings.maxAfterBalanceSize || 5.0},  # Maximum relative size of the training data after balancing
        keep_cross_validation_models=${settings.keepCrossValidationModels || 'True'},  # Keep cross-validation models
        keep_cross_validation_predictions=${settings.keepCrossValidationPredictions || 'True'},  # Keep cross-validation predictions
        keep_cross_validation_fold_assignment=${settings.keepCrossValidationFoldAssignment || 'True'},  # Keep cross-validation fold assignments
        sort_metric=${settings.sortMetric || '"AUTO"'}  # Metric to sort models by
    )
    
    # Train models
    aml.train(
        x=x,
        y=y,
        training_frame=train,
        validation_frame=test
    )
    
    # Get the leaderboard
    lb = aml.leaderboard
    print("AutoML Leaderboard:")
    print(lb.head(10))
    
    # Save the best model
    model_path = h2o.save_model(model=aml.leader, path=project_dir, force=True)
    print(f"Best model saved to: {model_path}")
    
    # Save leaderboard info
    leaderboard_df = lb.as_data_frame(use_multi_thread=True)
    leaderboard_path = os.path.join(project_dir, "leaderboard.csv")
    leaderboard_df.to_csv(leaderboard_path, index=False)
    print(f"Leaderboard saved to: {leaderboard_path}")
    
    # Get and save variable importances
    varimp = aml.leader.varimp()
    if varimp is not None:
        # Drop existing table if it exists
        db.command(f"DROP TABLE IF EXISTS {g_database}.{g_result_name}")
        
        # Create table for variable importances
        db.command(f"""
            CREATE TABLE {g_database}.{g_result_name} (
                variable String,
                relative_importance Float64,
                scaled_importance Float64,
                percentage Float64
            ) ENGINE = MergeTree()
            ORDER BY tuple()
        """)
        
        # Convert variable importances to DataFrame
        varimp_df = pd.DataFrame(varimp, columns=['variable', 'relative_importance', 'scaled_importance', 'percentage'])
        
        # Insert data into ClickHouse
        db.insert_df(g_result_name, varimp_df)
        print(f"Variable importances saved to ClickHouse table: {g_result_name}")
    
    # Collect all metadata and metrics
    model_metadata = {}
    
    # Model Info - only add fields that exist
    model_info = {}
    if hasattr(aml.leader, '_model_json'):
        if "algo_full_name" in aml.leader._model_json:
            model_info["model_type"] = aml.leader._model_json["algo_full_name"]
        if "training_time_ms" in aml.leader._model_json:
            model_info["training_time_ms"] = aml.leader._model_json["training_time_ms"]
        if "model_id" in aml.leader._model_json:
            model_info["model_id"] = aml.leader._model_json["model_id"]["name"]
        if "timestamp_start" in aml.leader._model_json:
            model_info["timestamp_start"] = aml.leader._model_json["timestamp_start"]
        if "timestamp_end" in aml.leader._model_json:
            model_info["timestamp_end"] = aml.leader._model_json["timestamp_end"]
    if model_info:
        model_metadata["model_info"] = model_info

    # Performance metrics - only if available
    try:
        performance_metrics = {
            "training": aml.leader.model_performance(train).metrics,
            "validation": aml.leader.model_performance(test).metrics
        }
        # Get cross-validation metrics from the leader model
        try:
            cv_metrics = aml.leader.cross_validation_metrics_summary()
            if cv_metrics is not None:
                performance_metrics["cross_validation"] = cv_metrics
        except Exception as e:
            print(f"Warning: Could not collect cross-validation metrics: {str(e)}")
        model_metadata["performance_metrics"] = performance_metrics
    except Exception as e:
        print(f"Warning: Could not collect performance metrics: {str(e)}")

    # Variable importance - only if available
    if varimp is not None:
        var_importance = {"importances": varimp}
        if hasattr(aml.leader, '_model_json') and "variable_importances" in aml.leader._model_json:
            var_importance["top_features"] = aml.leader._model_json["variable_importances"]
        model_metadata["variable_importance"] = var_importance

    # Leaderboard - only if available
    try:
        model_metadata["leaderboard"] = {
            "models": lb.as_data_frame(use_multi_thread=True).to_dict(orient='records')
        }
    except Exception as e:
        print(f"Warning: Could not collect leaderboard data: {str(e)}")

    # Training parameters - only if available
    if hasattr(aml.leader, '_model_json'):
        training_params = {}
        if "parameters" in aml.leader._model_json:
            training_params["algorithm_params"] = aml.leader._model_json["parameters"]
        if "preprocessing" in aml.leader._model_json:
            training_params["preprocessing"] = aml.leader._model_json["preprocessing"]
        if "training_metrics" in aml.leader._model_json:
            training_params["training_metrics"] = aml.leader._model_json["training_metrics"]
        if training_params:
            model_metadata["training_parameters"] = training_params

    # Problem-specific metrics - only if available
    try:
        if 'problem_type' in locals() and problem_type == "classification":
            classification_metrics = {}
            try:
                classification_metrics["confusion_matrix"] = aml.leader.confusion_matrix().table.as_data_frame().to_dict(orient='records')
            except Exception as e:
                print(f"Warning: Could not collect confusion matrix: {str(e)}")
            try:
                classification_metrics["auc"] = aml.leader.auc()
            except Exception as e:
                print(f"Warning: Could not collect AUC: {str(e)}")
            try:
                classification_metrics["logloss"] = aml.leader.logloss()
            except Exception as e:
                print(f"Warning: Could not collect logloss: {str(e)}")
            try:
                classification_metrics["mean_per_class_error"] = aml.leader.mean_per_class_error()
            except Exception as e:
                print(f"Warning: Could not collect mean_per_class_error: {str(e)}")
            try:
                classification_metrics["scoring_history"] = aml.leader.scoring_history().as_data_frame().to_dict(orient='records')
            except Exception as e:
                print(f"Warning: Could not collect scoring_history: {str(e)}")
            
            if classification_metrics:
                model_metadata["classification_metrics"] = classification_metrics
        else:  # regression
            regression_metrics = {}
            try:
                regression_metrics["rmse"] = aml.leader.rmse()
            except Exception as e:
                print(f"Warning: Could not collect rmse: {str(e)}")
            try:
                regression_metrics["mse"] = aml.leader.mse()
            except Exception as e:
                print(f"Warning: Could not collect mse: {str(e)}")
            try:
                regression_metrics["mae"] = aml.leader.mae()
            except Exception as e:
                print(f"Warning: Could not collect mae: {str(e)}")
            try:
                regression_metrics["rmsle"] = aml.leader.rmsle()
            except Exception as e:
                print(f"Warning: Could not collect rmsle: {str(e)}")
            try:
                regression_metrics["r2"] = aml.leader.r2()
            except Exception as e:
                print(f"Warning: Could not collect r2: {str(e)}")
            try:
                regression_metrics["scoring_history"] = aml.leader.scoring_history().as_data_frame().to_dict(orient='records')
            except Exception as e:
                print(f"Warning: Could not collect scoring_history: {str(e)}")
            
            if regression_metrics:
                model_metadata["regression_metrics"] = regression_metrics
    except Exception as e:
        print(f"Warning: Could not collect problem-specific metrics: {str(e)}")

    # Only save metadata if we have any
    if model_metadata:
        # Save metadata to ClickHouse
        metadata_table = f"{g_result_name}_metadata"
        db.command(f"DROP TABLE IF EXISTS {g_database}.{metadata_table}")
        
        db.command(f"""
            CREATE TABLE {g_database}.{metadata_table} (
                category LowCardinality(String),
                metadata String
            ) ENGINE = MergeTree()
            ORDER BY category
        """)

        # Insert each category of metadata separately
        for category, data in model_metadata.items():
            if data:  # Only insert if we have data for this category
                # Convert all numeric values to strings and ensure proper JSON serialization
                try:
                    # Convert numpy/pandas objects to Python native types first
                    if hasattr(data, 'to_dict'):
                        data = data.to_dict()
                    serialized_data = json.dumps(data, default=str)
                    db.insert(metadata_table, [(category, serialized_data)], column_names=['category', 'metadata'])
                    print(f"Saved metadata for category: {category}")
                except Exception as e:
                    print(f"Warning: Could not save metadata for category {category}: {str(e)}")

        print(f"Model metadata saved to table: {metadata_table}")

main()`;
}

const createClassification = async (taskData: Partial<ForecastTaskType>) => {
	const query = `
    SELECT
      ${taskData.columnGrouped} as category
    FROM
      ${taskData.databaseName}.${taskData.tableName}
    WHERE
      ${taskData.columnDate} != '0000-00-00'
      AND
        ${taskData.columnDate} IS NOT NULL
      AND
        ${taskData.columnGrouped} != ''
      AND
        ${taskData.columnGrouped} IS NOT NULL
    GROUP BY
      ${taskData.columnGrouped}`;
	return await executeQuery<{ category: string }>({ at: taskData, sql: query });
};
