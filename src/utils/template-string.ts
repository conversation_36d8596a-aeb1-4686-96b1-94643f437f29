import type { GenericType, ParamType } from '@gaio/shared/types';
import { $ } from 'bun';
import { template, templateSettings } from 'lodash-es';
import nunjucks from 'nunjucks';

export default (query: string, params: ParamType[], extraData = {}) => {
	query = query || ' ';
	templateSettings.interpolate = /{{([\s\S]+?)}}/g;
	const compiled = template(query);
	return compiled(organizeParamsByKey(params, extraData));
};

function organizeParamsByKey(
	params: ParamType[],
	extraData: Record<string, unknown>
) {
	let results: GenericType = {};

	for (const par of params) {
		if (`${par.paramValue}`.trim().startsWith('{{')) {
			results[par.paramName] = (
				`${par.paramValue || ''}` ||
				par.value ||
				''
			).replace(/\{{|\}}/gi, '');
		} else if (
			typeof par.paramValue === 'string' &&
			isNumber(par.paramValue) &&
			canUseNumber(par.paramValue)
		) {
			results[par.paramName] = Number(par.paramValue);
		} else {
			results[par.paramName] = par.paramValue || par.value || '';
		}
	}

	if (extraData && Object.keys(extraData).length > 0) {
		results = { ...results, ...extraData };
	}

	return results;
}

function canUseNumber(str: string) {
	// Try to convert to Number
	const num = Number(str);

	// Check if it's a valid number and an integer
	if (Number.isNaN(num) || !Number.isFinite(num) || !Number.isInteger(num)) {
		return false;
	}

	// Check if it's within the safe integer range
	return num >= Number.MIN_SAFE_INTEGER && num <= Number.MAX_SAFE_INTEGER;
}

const isNumber = (n: string) => {
	if (typeof n === 'string') {
		if (n.trim().startsWith('0') && !n.trim().startsWith('0.')) {
			return false;
		}
	}
	return /^-?[\d.]+(?:e-?\d+)?$/.test(n);
};

export async function renderStringContext(
	str: string,
	context: { params: GenericType; table: GenericType }
) {
	for (const key of Object.keys(context.params)) {
		if (
			typeof context.params[key] === 'string' &&
			context.params[key].trim().startsWith('{{')
		) {
			context.params[key] = await paramFunction(context.params[key]);
		}
	}

	if (str) {
		str = nunjucks.renderString(str, context);
	}

	return str || '';
}

async function paramFunction(paramValue: string) {
	const { stdout } =
		await $`clickhouse-local -q "SELECT (${paramValue.replace(/\{{|}}/gi, '')}) AS paramValue limit 1"`;

	return stdout.toString()?.trim()?.split('\n')?.join(' ');
}
