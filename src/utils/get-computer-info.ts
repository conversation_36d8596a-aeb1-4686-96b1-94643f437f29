import os from 'node:os';
import { exec } from 'node:child_process';
import { promisify } from 'node:util';
import { HTTPException } from 'hono/http-exception';

const execAsync = promisify(exec);

export async function getComputerInfo() {
	try {
		const hostname = os.hostname();
		const platform = os.platform();

		const getMacAddress = async (): Promise<string | null> => {
			try {
				if (platform === 'darwin' || platform === 'linux') {
					const { stdout } = await execAsync(
						'ifconfig | grep -E "ether|HWaddr" | head -1 | awk \'{print $2}\''
					);
					return stdout.trim() || null;
				}

				if (platform === 'win32') {
					const { stdout } = await execAsync('getmac /fo csv /nh');
					const lines = stdout.trim().split('\n');
					if (lines.length > 0) {
						const macAddress = lines[0].split(',')[0].replace(/"/g, '');
						return macAddress || null;
					}
				}
				return null;
			} catch (error) {
				console.error('Erro ao obter MAC address:', error);
				return null;
			}
		};

		const macAddress = await getMacAddress();

		return {
			computerName: null,
			macAddress: null,
			platform: platform,
			architecture: os.arch(),
			operatingSystem: `${os.type()} ${os.release()}`,
			networkInterfaces: Object.keys(os.networkInterfaces()).filter(
				(name) => name !== 'lo' && name !== 'lo0'
			)
		};
	} catch (error) {
		console.error('Erro ao obter informações do computador:', error);
		throw new HTTPException(500, {
			cause: 'computerInfoError',
			message: 'Erro ao obter informações do computador'
		});
	}
}