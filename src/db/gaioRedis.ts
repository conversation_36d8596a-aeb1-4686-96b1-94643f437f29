import { env } from '@/utils/env';
import type { RedisOptions } from 'ioredis';
import { Redis } from 'ioredis';

const redisOptions: RedisOptions = {
	db: 3, // needs Redis >= 6
	host: env.GAIO_REDIS_HOST || 'redis', // Redis host
	password: '',
	port: env.GAIO_REDIS_PORT, // Redis port
	username: '' // Defaults to 0
};

const redis = new Redis(redisOptions);

export const gaioRedis = {
	get: async <T>(key: string): Promise<T | null> => {
		const data = await redis.get(key);

		if (!data) {
			return null;
		}

		return JSON.parse(data) as T;
	},
	pub: new Redis(
		redisOptions 
	),
	set: async <T>(key: string, value: T) => {
		await redis.set(key, JSON.stringify(value));
	},
	sub: () =>
		new Redis(
			redisOptions
		)
};
