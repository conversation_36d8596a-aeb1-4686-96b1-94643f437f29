import type { ClickHouseSummary, Row } from '@clickhouse/client-common';
import type { CredentialsType, GenericType } from '@gaio/shared/types';

import { dbClickhouseConnectionInterface } from './connections/db.clickhouse';
import { dbDatabricksConnectionInterface } from './connections/db.databricks';
import { dbMariaConnectionInterface } from './connections/db.mariadb';
import { dbMemsqlConnectionInterface } from './connections/db.memsql';
import { dbMssqlConnectionInterface } from './connections/db.mssql';
import { dbMysqlConnectionInterface } from './connections/db.mysql';
import { dbOracleConnectionInterface } from './connections/db.oracle';
import { dbPgConnectionInterface } from './connections/db.pg';
import { dbRedshiftConnectionInterface } from './connections/db.redshift';
import { dbSalesforceConnectionInterface } from './connections/db.salesforce';
import { dbSnowflakeConnectionInterface } from './connections/db.snowflake';

export type DatabaseConnectionQueryStatistics =
	| {
			elapsed: number;
			rows_read: number;
			bytes_read: number;
	  }
	| ClickHouseSummary;

export type DatabaseConnectionExtras = { appId: string };

export type DatabaseConnectionQuery = {
	query: <TOutput = unknown[]>(
		sql: string,
		abortSignal?: AbortSignal,
		logComment?: string
	) => Promise<{
		data: TOutput | unknown;
		statistics?: DatabaseConnectionQueryStatistics;
		meta?: { name: string; type?: string }[];
	}>;
	stream?: <TOutput = unknown[]>(
		sql: string,
		abortSignal?: AbortSignal,
		logComment?: string
	) => Promise<ReadableStream<Row<TOutput, 'JSONEachRow'>[]>>;
	insert?: (
		sql: string,
		data: GenericType[],
		abortSignal: AbortSignal,
		logComment?: string
	) => Promise<{ queryId: string }>;
};

export async function dbHub(
	credentials: CredentialsType,
	extras: DatabaseConnectionExtras
): Promise<DatabaseConnectionQuery> {
	switch (credentials.client) {
		case 'clickhouse':
			return dbClickhouseConnectionInterface(credentials);

		case 'mysql':
			return dbMysqlConnectionInterface(credentials);

		case 'mariadb':
			return dbMariaConnectionInterface(credentials);

		case 'memsql':
			return dbMemsqlConnectionInterface(credentials);

		case 'pg':
			return dbPgConnectionInterface(credentials);

		case 'redshift':
			return dbRedshiftConnectionInterface(credentials);

		case 'oracle':
			return dbOracleConnectionInterface(credentials);

		case 'mssql':
			return dbMssqlConnectionInterface(credentials);

		case 'salesforce':
			return dbSalesforceConnectionInterface(credentials);

		case 'databricks':
			return dbDatabricksConnectionInterface(credentials, extras);
		// throw new Error('@databricks/sql deps having issues with bun - something related to l4z')

		case 'snowflake':
			return dbSnowflakeConnectionInterface(credentials);

		default:
			throw new Error('Unknown client');
	}
}
