import type { CronEntity } from '@/components/cron/cron.entity';
import type {
	AppEntity,
	CodeSnippetEntity,
	FlowEntity,
	MapsGeometryEntity,
	MapsMetaEntity,
	MetaEntity,
	MetaViewEntity,
	PassEntity,
	RepoEntity,
	SerialEntity,
	SettingEntity,
	SourceEntity,
	SurveyEntity,
	TagEntity,
	TaskLogEntity,
	TimeCapsuleEntity,
	UserEntity
} from '@/entities/index';
import type { MakeCreatedAtOptional } from '@/types/EntityTimestamps.type';
import type { AtLeastOne } from '@/types/utilities.type';
import { env } from '@/utils/env';
import { clickDate } from '@/utils/helpers';
import { createClient } from '@clickhouse/client';
import type { ClickHouseSettings } from '@clickhouse/client';
import type {
	ApiKeyType,
	ApiType,
	GenericType,
	MapType
} from '@gaio/shared/types';
import { isArray } from 'lodash-es';

type DBGaioTablesToEntity = {
	app: AppEntity;
	code_snippet: CodeSnippetEntity;
	flow: FlowEntity;
	cron: CronEntity;
	maps_geometry: MapsGeometryEntity;
	maps_meta: MapsMetaEntity;
	meta_view: MetaViewEntity;
	meta: MetaEntity;
	pass: PassEntity;
	repo: RepoEntity;
	serial: SerialEntity;
	setting: SettingEntity;
	source: SourceEntity;
	survey: SurveyEntity;
	tag: TagEntity;
	time_capsule: TimeCapsuleEntity;
	task_log: TaskLogEntity;
	user: UserEntity;
	map: MapType;
	api: ApiType;
	api_key: ApiKeyType;
};

type DBGaioTablesNames = keyof DBGaioTablesToEntity;

const clickhouseSettings: ClickHouseSettings = {
	allow_experimental_object_type: 1,
	alter_sync: '2',
	date_time_input_format: 'best_effort',
	enable_http_compression: 1,
	format_csv_allow_double_quotes: 1,
	format_csv_allow_single_quotes: 1,
	format_csv_null_representation: '',
	input_format_try_infer_datetimes: 1,
	join_use_nulls: 0,
	mutations_sync: '2',
	output_format_json_quote_64bit_integers: 0,
	s3_truncate_on_insert: 1,
	transform_null_in: 1,
	wait_end_of_query: 1
};

export const dbGaio = (queryLabel?: string) => {
	const host = env.GAIO_CLICKHOUSE_HOST;
	const port = env.GAIO_CLICKHOUSE_PORT;
	const user = env.GAIO_CLICKHOUSE_USER;
	const database = env.GAIO_CLICKHOUSE_DATABASE;
	const password = env.GAIO_CLICKHOUSE_PASSWORD;
	const ssl = env.GAIO_CLICKHOUSE_SSL;

	const client = createClient({
		clickhouse_settings: clickhouseSettings,
		compression: {
			request: true,
			response: true
		},
		database: database || 'default',
		password: password,
		url: ssl ? `https://${host}:${port}` : `http://${host}:${port}`,
		username: user
	});

	return {
		/**
		 *
		 * @param table String - The table name
		 * @param where Object - Accepts at least one column from entity
		 *
		 * @returns Boolean - Success or failure
		 */
		delete: async <TEntityName extends DBGaioTablesNames>({
			table,
			where
		}: {
			table: TEntityName;
			where: AtLeastOne<DBGaioTablesToEntity[TEntityName]>;
		}): Promise<{ success: boolean }> => {
			try {
				const whereValues: string[] = [];
				for (const [key, value] of Object.entries(where)) {
					whereValues.push(`${key} = '${value}'`);
				}

				const whereStatement = `WHERE ${whereValues.join(' AND ')}`;
				const deleteQuery = `DELETE
														 FROM ${table} ${whereStatement}`;

				await client.exec({
					query: deleteQuery
				});

				return { success: true };
			} catch (err) {
				throw new Error(err);
			}
		},

		exec: async (
			sql: string,
			options?: {
				params?: Record<string, unknown>;
				stringify?: string[];
				ref?: string;
			}
		) => {
			try {
				if (options) {
					for (const key in options?.params) {
						if (options?.stringify?.includes(key)) {
							options.params[key] = JSON.stringify(options.params[key]);
						}
					}
				}

				await client.exec({
					query: sql,
					query_params: options?.params
				});

				return { success: true };
			} catch (err) {
				throw new Error(err.message);
			}
		},

		/**
		 *
		 * @param table String - The table name
		 * @param values Object - Accepts any column from entity
		 *
		 * @returns A list of items based on table
		 */
		findAll: async <TEntityName extends DBGaioTablesNames>({
			table,
			where,
			options
		}: {
			table: TEntityName;
			where: Partial<DBGaioTablesToEntity[TEntityName]>;
			options?: {
				pagination?: { limit?: number; currentPage?: number };
				select?: Array<keyof DBGaioTablesToEntity[TEntityName]>;
				// exclude?: Array<keyof DBGaioTablesToEntity[TEntityName]>
				order?: {
					by: keyof DBGaioTablesToEntity[TEntityName];
					direction: 'ASC' | 'DESC';
				};
				join?: {
					from: keyof DBGaioTablesToEntity[TEntityName];
					table: DBGaioTablesNames;
					on: string;
					type: 'LEFT' | 'RIGHT' | 'INNER';
					select?: Array<string>;
				};
				// groupBy: {}
				parse?: Array<keyof DBGaioTablesToEntity[TEntityName]>;
			};
		}): Promise<{
			data: Array<DBGaioTablesToEntity[TEntityName]>;
			meta?: { currentPage: number; pageCount: number; totalCount: number };
		}> => {
			try {
				const whereValues: string[] = [];
				for (const [key, value] of Object.entries(where)) {
					whereValues.push(`${key} = '${value}'`);
				}

				const itemsToSelect: string[] = [];

				let joinStatement = '';

				const selectTableAlias = 'gaioSelectTable';
				const joinTableAlias = 'gaioJoinTable';

				if (options?.select) {
					options.select.forEach((item) =>
						itemsToSelect.push(`${selectTableAlias}.${item as string}`)
					);
				}

				if (options?.join) {
					joinStatement = `${options.join.type} JOIN ${options.join.table} AS ${joinTableAlias} ON ${options.join.from as string} = ${options.join.on}`;

					if (options.join.select) {
						options.join.select.forEach((item) =>
							itemsToSelect.push(`${joinTableAlias}.${item as string}`)
						);
					}
				}

				const whereStatement = `WHERE ${whereValues.join(' AND ')}`;
				let selectQuery = `SELECT ${itemsToSelect.length > 0 ? itemsToSelect.join(', ') : '*'}
													 FROM ${table} AS ${selectTableAlias} ${joinStatement} ${whereStatement}`;

				if (options?.order) {
					selectQuery += ` ORDER BY ${options.order.by as string} ${options.order.direction}`;
				}

				const meta = {
					currentPage: options?.pagination.currentPage,
					pageCount: 0,
					totalCount: 0
				};

				if (options?.pagination.limit) {
					// PAGINATION
					if (options.pagination.currentPage) {
						selectQuery += ` LIMIT ${options.pagination.limit} OFFSET ${options.pagination.limit * (options.pagination.currentPage - 1)}`;

						const totalCountQuery = await client.query({
							format: 'JSONCompact',
							query: `
								SELECT count(*)
								FROM ${table} ${whereStatement}
							`
						});

						const { data: totalCountResults } =
							await totalCountQuery.json<Array<number>>();

						const totalCount = totalCountResults[0][0];
						const pageCount = Math.ceil(totalCount / options.pagination.limit);

						meta.pageCount = pageCount;
						meta.totalCount = totalCount;
					} else {
						selectQuery += ` LIMIT ${options.pagination.limit}`;
					}
				}

				const query = await client.query({
					format: 'JSON',
					query: selectQuery
				});

				const results = await query.json<DBGaioTablesToEntity[TEntityName]>();

				if (options?.parse) {
					if (isArray(results)) {
						return {
							data: results.map((item) => {
								options.parse.forEach((parseOptions) => {
									if (item[parseOptions]) {
										item[parseOptions] = JSON.parse(
											item[parseOptions] as string
										);
									}
								});

								return item;
							}),
							meta
						};
					}
				}

				return { data: results.data, meta };
			} catch (err) {
				throw new Error(err);
			}
		},

		/**
		 *
		 * @param table String - The table name
		 * @param where Object - Accepts at least one column from entity
		 *
		 * @returns The entity or undefined
		 */
		findOne: async <TEntityName extends DBGaioTablesNames>({
			table,
			where,
			options
		}: {
			table: TEntityName;
			where: AtLeastOne<DBGaioTablesToEntity[TEntityName]>;
			options?: { select?: Array<keyof DBGaioTablesToEntity[TEntityName]> };
		}): Promise<DBGaioTablesToEntity[TEntityName] | undefined> => {
			try {
				const whereValues: string[] = [];
				for (const [key, value] of Object.entries(where)) {
					whereValues.push(`${key} = '${value}'`);
				}

				const itemsToSelect: string[] = [];
				if (options?.select) {
					options.select.forEach((item) => itemsToSelect.push(item as string));
				}

				const whereStatement = `WHERE ${whereValues.join(' AND ')}`;
				const selectQuery = `SELECT ${itemsToSelect.length > 0 ? itemsToSelect.join(', ') : '*'}
														 FROM ${table} ${whereStatement} LIMIT 1`;

				const query = await client.query({
					format: 'JSONEachRow',
					query: selectQuery
				});

				const results = await query.json<DBGaioTablesToEntity[TEntityName]>();

				if (!results) return undefined;

				return results[0];
			} catch (err) {
				throw new Error(err);
			}
		},

		insert: async <TEntityName extends DBGaioTablesNames>(
			table: TEntityName,
			values: Array<Partial<DBGaioTablesToEntity[TEntityName]>>,
			options?: {
				stringify?: Array<keyof DBGaioTablesToEntity[TEntityName]>;
				ref?: string;
			}
		) => {
			try {
				const valuesToInsert = values.map((item) => {
					const newItem = { ...item };
					if (options?.stringify) {
						options.stringify.forEach((key) => {
							if (key in newItem && newItem[key] !== undefined) {
								// @ts-ignore
								newItem[key] = JSON.stringify(newItem[key]);
							}
						});
					}
					return newItem as Partial<DBGaioTablesToEntity[TEntityName]>;
				});

				await client.insert({
					format: 'JSONEachRow',
					table,
					values: valuesToInsert
				});

				return { success: true };
			} catch (err) {
				throw new Error(err.message);
			}
		},

		/**
		 * @typeparam TObject A type with desired output shape, passed to .json()
		 *
		 * @param sql String
		 * @param options { params: Record<string, unknown>, parse: Array<keyof TObject>}
		 * @returns TObject[]
		 */
		query: async <TObject>(
			sql: string,
			options: {
				params?: Record<string, unknown>;
				parse?: Array<keyof TObject>;
			} = {}
		) => {
			try {
				const query = await client.query({
					format: 'JSONEachRow',
					query: sql,
					query_params: options.params
				});

				const results = await query.json<TObject>();

				if (results) {
					if (options.parse?.length) {
						if (isArray(results)) {
							return results.map((item) => {
								options.parse.forEach((parseOptions) => {
									if (item[parseOptions]) {
										item[parseOptions] = JSON.parse(
											item[parseOptions] as string
										);
									}

									if (item[parseOptions] === '[]') {
										return [];
									}

									if (item[parseOptions] === '{}') {
										return {};
									}
								});

								return item;
							});
						}

						return results;
					}

					return results;
				}

				return [];
			} catch (e) {
				throw new Error(e?.message);
			}
		},

		updateOrInsert: async ({
			table,
			values,
			primaryKeys
		}: {
			table: string;
			values: GenericType;
			primaryKeys: string[];
		}) => {
			// all primary keys must be present

			if (
				!primaryKeys.length &&
				!primaryKeys.some((key) => {
					return values[key];
				})
			) {
				throw new Error('Primary keys are required');
			}

			const checkIfExist = await client.query({
				format: 'JSONEachRow',
				query: `SELECT *
								FROM ${table}
								WHERE ${primaryKeys.map((key) => `${key} = '${values[key]}'`).join(' AND ')}`
			});

			const [item] = (await checkIfExist.json()) as GenericType[];

			const insertData = (valuesToBeInserted: GenericType) => {
				return client.insert({
					format: 'JSONEachRow',
					table,
					values: [valuesToBeInserted]
				});
			};

			if (!item) {
				await insertData(values);
			} else {
				await client.exec({
					query: `ALTER TABLE ${table} DELETE
						WHERE ${primaryKeys.map((key) => `${key} = '${values[key]}'`).join(' AND ')}`
				});

				values.createdAt = undefined;
				values.createdBy = undefined;

				values.createdBy = item.createdBy;
				values.createdAt = item.createdAt;

				Object.keys(values).forEach((key) => {
					if (Object.hasOwn(item, key)) {
						item[key] = values[key];
					}
				});

				await insertData({
					...item,
					updateAt: clickDate(new Date())
				});
			}
		},

		/**
		 * @param table The table name
		 * @param	values The values to insert/update
		 * @param where The where clause
		 *
		 * @returns The updated entity
		 *
		 */
		upsert: async <TEntityName extends DBGaioTablesNames>({
			table,
			values,
			where
		}: {
			table: TEntityName;
			values: MakeCreatedAtOptional<DBGaioTablesToEntity[TEntityName]>;
			where: Partial<DBGaioTablesToEntity[TEntityName]>;
			options?: object;
		}): Promise<DBGaioTablesToEntity[TEntityName] | undefined> => {
			try {
				const whereValues: string[] = [];
				for (const [key, value] of Object.entries(where)) {
					whereValues.push(`${key} = '${value}'`);
				}

				const whereStatement = `WHERE ${whereValues.join(' AND ')}`;
				const selectQuery = `SELECT *
														 FROM ${table} ${whereStatement}`;

				const itemExistsQuery = await client.query({
					format: 'JSONEachRow',
					query: selectQuery
				});

				const [itemExists] =
					await itemExistsQuery.json<DBGaioTablesToEntity[TEntityName]>();

				if (!itemExists) {
					await client.insert({
						format: 'JSONEachRow',
						table,
						values: [values]
					});
				} else {
					const updateValues: string[] = [];
					for (const [key, value] of Object.entries(values)) {
						const reservedColumns = [
							'createdAt',
							'createdBy',
							'flowId',
							'appId',
							'codeSnippetId'
						];

						if (!reservedColumns.includes(key)) {
							if (key === 'updateAt' || key === 'createdAt') {
								const date = new Date(value as string | number | Date)
									.toISOString()
									.replace('T', ' ')
									.split('.')[0];
								updateValues.push(`${key} = '${date}'`);
							} else {
								updateValues.push(`${key} = '${value}'`);
							}
						}
					}

					await client.exec({
						query: `
							ALTER TABLE ${table}
							UPDATE
								${updateValues.join(', ')}
									${whereStatement}
						`,
						query_params: values
					});
				}

				const updatedItem = await client.query({
					format: 'JSONEachRow',
					query: selectQuery
				});

				const updatedItemResults =
					await updatedItem.json<DBGaioTablesToEntity[TEntityName]>();

				return updatedItemResults[0];
			} catch (err) {
				throw new Error(err);
			}
		}
	};
};
