import RepoRepository from '@/components/repo/repo.repository'
import SourceRepository from '@/components/source/source.repository'
import type {
	CommonTaskType,
	GenericType,
	TaskContextType,
	TaskType
} from '@gaio/shared/types'
import { getBucketNameFromAppId } from '@gaio/shared/utils'
import { dbConnections } from './db.connections'
import type { DatabaseConnectionQuery } from './db.hub'
import { dbHub } from './db.hub'
import { defineConnectionName } from '@/utils/helpers'

export type BasicConnectionInfoDTO = Pick<
	CommonTaskType,
	'sourceId' | 'sourceType' | 'appId' | 'admin' | 'repoId'
>;

const canTableBeTemporary = (logFrom: string) => {
	const options = ['studio']

	return !options.includes(logFrom)
}

export class DbService {
	private transformTemporaryName(
		input: string,
		taskContext: TaskContextType,
		isAdmin: boolean
	): string {
		if (
			taskContext?.logFrom &&
			canTableBeTemporary(taskContext.logFrom) &&
			!isAdmin
		) {
			const tmpName = `gaio${taskContext.sessionid}`
			return input
				.replace(/\btmp_/g, `tmp_${tmpName}_`)
				.replace(`tmp_${tmpName}_${tmpName}`, `tmp_${tmpName}_`)
		}
		return input
	}

	private connectionName(args: BasicConnectionInfoDTO) {
		return defineConnectionName({
			sourceType: args.sourceType,
			sourceId: args.sourceId,
			appId: args.appId,
			repoId: args.repoId,
			admin: args.admin
		})
	}

	private async getCredentials({
																 sourceType,
																 sourceId,
																 appId,
																 repoId
															 }: BasicConnectionInfoDTO) {
		if (sourceType === 'super') {
			return await RepoRepository.getRepoDataById(repoId)
		}

		if (sourceType === 'bucket') {
			return await RepoRepository.getRepoCredentials({ appId, repoId })
		}

		return await SourceRepository.getSourceCredentials(sourceId)
	}

	private async getConnection(
		basicConnectionInfo: BasicConnectionInfoDTO
	): Promise<DatabaseConnectionQuery> {
		const connectionName = this.connectionName(basicConnectionInfo)

		if (!Object.hasOwn(dbConnections, connectionName)) {
			const credentials = await this.getCredentials(basicConnectionInfo)

			if (!credentials) {
				throw new Error('No credentials')
			}

			dbConnections[connectionName] = await dbHub(credentials, {
				appId: basicConnectionInfo.appId
			})
		}

		return dbConnections[connectionName]
	}

	private async _getConnection(basicConnectionInfo: BasicConnectionInfoDTO) {
		const credentials = await this.getCredentials(basicConnectionInfo)

		if (!credentials) {
			throw new Error('No credentials')
		}

		return await dbHub(credentials, { appId: basicConnectionInfo.appId })
	}

	public async connect(taskData: TaskType, taskContext: TaskContextType = {}) {
		const connection = await this.getConnection({
			sourceType: taskData.sourceType,
			sourceId: taskData.sourceId,
			appId: taskData.appId,
			repoId: taskData.repoId,
			admin: taskData.admin
		})

		if (!connection) {
			throw new Error('No connection')
		}

		return {
			query: async <TOutput = unknown[]>(
				sql: string,
				abortSignal?: AbortSignal,
				logComment?: string
			) => {
				sql = this.transformTemporaryName(sql, taskContext, taskData.admin)

				return await connection.query<TOutput>(sql, abortSignal, logComment)
			},
			stream: async <TOutput = unknown[]>(
				sql: string,
				abortSignal?: AbortSignal,
				logComment?: string
			) => {
				sql = this.transformTemporaryName(sql, taskContext, taskData.admin)

				return await connection.stream<TOutput>(sql, abortSignal, logComment)
			},
			insert: async (
				table: string,
				data: GenericType[],
				abortSignal?: AbortSignal
			) => {
				table = this.transformTemporaryName(table, taskContext, taskData.admin)

				return await connection.insert(table, data, abortSignal)
			}
		}
	}
}
