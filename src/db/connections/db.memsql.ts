import type { CredentialsType } from '@gaio/shared/types';
import { dbMysqlConnection } from './db.mysql';

export function dbMemsqlConnection(connectionData: Partial<CredentialsType>) {
	return dbMysqlConnection(connectionData);
}

export function dbMemsqlConnectionInterface(credentials: CredentialsType) {
	return {
		query: async <TOutput = unknown>(sql: string) => {
			try {
				const dbQuery = await dbMemsqlConnection(credentials).query(sql);

				const [results] = dbQuery;

				return { data: results as TOutput[] };
			} catch (err) {
				throw new Error(
					`Code: ${err.code}, Message: ${err.message}, Type: ${err.type}`
				);
			}
		}
	};
}
