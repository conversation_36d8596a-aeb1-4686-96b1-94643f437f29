import type { CredentialsType } from '@gaio/shared/types';
import snowflake from 'snowflake-sdk';

export function dbSnowflakeConnection(
	connectionData: Partial<CredentialsType>
) {
	return snowflake.createConnection({
		account: connectionData.account,
		username: connectionData.user,
		password: connectionData.password,
		host: connectionData.host,
		database: connectionData.database,
		schema: connectionData.schemaName
	});
}

export async function dbSnowflakeConnectionInterface(
	credentials: CredentialsType
) {
	return {
		query: async <TOutput = unknown>(sql: string) => {
			try {
				const conn = dbSnowflakeConnection(credentials);

				await conn.connectAsync((err) => {
					if (err) throw err;
				});

				const results = await new Promise((resolve, reject) => {
					conn.execute({
						sqlText: sql,
						asyncExec: true,
						complete: (err, stmt, rows) => {
							if (err) reject(err);

							resolve(rows);
						}
					});
				});

				return { data: results as TOutput[] };
			} catch (err) {
				throw new Error(
					`Code: ${err.code}, Message: ${err.message}, Type: ${err.type}`
				);
			}
		}
	};
}
