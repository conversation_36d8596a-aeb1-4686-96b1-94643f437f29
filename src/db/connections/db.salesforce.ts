import type { CredentialsType } from '@gaio/shared/types';
import jsforce from '@jsforce/jsforce-node';

export function dbSalesforceConnection(
	connectionData: Partial<CredentialsType>
) {
	return new jsforce.Connection({
		loginUrl: connectionData.loginUrl
	});
}

export async function dbSalesforceConnectionInterface(
	credentials: CredentialsType
) {
	return {
		query: async <TOutput = unknown>(sql: string) => {
			try {
				const conn = dbSalesforceConnection(credentials);

				await conn.login(
					credentials.user,
					credentials.password + credentials.token
				);

				const results = await conn.query<TOutput>(sql);

				return { data: results };
			} catch (err) {
				throw new Error(
					`Code: ${err.code}, Message: ${err.message}, Type: ${err.type}`
				);
			}
		}
	};
}
