import type { CredentialsType } from '@gaio/shared/types';
import sql from 'mssql';

export function dbMssqlConnection(connectionData: Partial<CredentialsType>) {
	const { host, port, user, password, database, encrypt } = connectionData;

	return sql.connect({
		server: host,
		port: Number(port),
		user,
		password,
		database,
		// schema, // or 'dbo'
		requestTimeout: 2147483646, // why? ok
		options: {
			useUTC: false,
			enableArithAbort: true,
			encrypt: encrypt ? encrypt : false
		}
	});
}

export function dbMssqlConnectionInterface(credentials: CredentialsType) {
	return {
		query: async <TOutput = unknown>(sql: string) => {
			try {
				const connection = await dbMssqlConnection(credentials);
				const results = await connection.query<TOutput>(sql);

				return { data: results };
			} catch (err) {
				throw new Error(
					`Code: ${err.code}, Message: ${err.message}, Type: ${err.type}`
				);
			}
		}
	};
}
