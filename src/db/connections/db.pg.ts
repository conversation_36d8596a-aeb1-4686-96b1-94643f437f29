import type { CredentialsType } from '@gaio/shared/types';
import pg from 'pg';

export function dbPgConnection(connectionData: Partial<CredentialsType>) {
	const { database, port, host, user, password } = connectionData;

	pg.types.setTypeParser(20, (val) => Number.parseInt(val, 10)); // BIGINT
	pg.types.setTypeParser(1700, (val) => Number.parseFloat(val)); // NUMERIC
	pg.types.setTypeParser(23, (val) => Number.parseInt(val, 10)); // INTEGER

	return new pg.Pool({
		database,
		port: Number(port),
		host,
		user,
		password,
		bigIntMode: 'number'
	});
}

export function dbPgConnectionInterface(credentials: CredentialsType) {
	return {
		query: async <TOutput = unknown>(sql: string) => {
			try {
				const results = await dbPgConnection(credentials).query<TOutput>(sql);

				return { data: results.rows, meta: results.fields || [] };
			} catch (err) {
				throw new Error(
					`Code: ${err.code}, Message: ${err.message}, Type: ${err.type}`
				);
			}
		}
	};
}
