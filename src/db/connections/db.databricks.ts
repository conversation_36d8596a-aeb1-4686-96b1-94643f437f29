import { unlink } from 'node:fs/promises';
import { join } from 'node:path';
import {
	connectionsFolder,
	makeSureFolderExist
} from '@/components/task/use-cases/runners/runner.tools';
import type { CredentialsType } from '@gaio/shared/types';
import { getId } from '@gaio/shared/utils';
import { $ } from 'bun';
import type { DatabaseConnectionExtras } from '../db.hub';

//! DBSQLClient makes Bun throw an error - have to find a way to fix it
//* Ref: https://github.com/oven-sh/bun/issues/5578 - https://github.com/oven-sh/bun/issues/158

export function dbDatabricksConnectionInterface(
	credentials: CredentialsType,
	{ appId }: DatabaseConnectionExtras
) {
	return {
		query: async <TOutput = unknown>(sql: string) => {
			try {
				if (!appId) {
					throw new Error(
						'Missing appId - Could not create a Databricks connection'
					);
				}

				const databricksConnFileName = `${getId(8)}-databricks.config.yaml`;
				const databricksConnFileDir = connectionsFolder(appId);
				const databricksConnFilePath = join(
					connectionsFolder(appId),
					databricksConnFileName
				);

				makeSureFolderExist(databricksConnFileDir);

				const databricksConnFile = Bun.file(databricksConnFilePath);
				const databricksConnFileExists = await databricksConnFile.exists();

				if (databricksConnFileExists) {
					// TODO:: handle this
					// delete it?
				}

				if (!credentials.httpPath) {
					throw Error(
						'Missing httpPath - Could not create Databricks connection'
					);
				}

				if (!credentials.httpPath.startsWith('/')) {
					credentials.httpPath = `/${credentials.httpPath}`;
				}
				const firstLine = 'connections:\n';

				const databricksDatabase = credentials.httpPath.includes('warehouses')
					? ''
					: `&database=${credentials.database}`;
				const secondLine = ` run: ["databricks", "token:${credentials.token}@${credentials.host}:443${credentials.httpPath}?catalog=${credentials.schemaName}${databricksDatabase}&useCloudFetch=true"]\n`;
				const connectionString = firstLine + secondLine;

				await Bun.write(databricksConnFilePath, connectionString);

				const result =
					await $`usql --config ${databricksConnFilePath} run --json -q -w --command "${sql}" --set SHOW_HOST_INFORMATION=false`
						.json()
						.finally(async () => {
							await unlink(databricksConnFilePath);
						});

				return {
					data: result as TOutput
				};
			} catch (err) {
				// console.log('Error:', Buffer.from(err.stderr))

				throw new Error(
					`Code: ${err.code}, Message: ${err.message}, Type: ${err.type}`
				);
			}
		}
	};
}
