import RepoRepository from '@/components/repo/repo.repository';
import {
	type ClickHouseSettings,
	createClient,
	type DataFormat,
	type Row
} from '@clickhouse/client';
import type { CredentialsType, GenericType } from '@gaio/shared/types';
import type { DatabaseConnectionQuery } from '../db.hub';

const clickhouseSettings: ClickHouseSettings = {
	allow_experimental_object_type: 1,
	alter_sync: '2',
	async_insert: 1,
	date_time_input_format: 'best_effort',
	enable_http_compression: 1,
	default_table_engine: 'MergeTree',
	format_csv_allow_double_quotes: 1,
	format_csv_allow_single_quotes: 1,
	format_csv_null_representation: '',
	join_use_nulls: 0,
	mutations_sync: '2',
	output_format_json_quote_64bit_integers: 0,
	s3_truncate_on_insert: 1,
	transform_null_in: 1,
	wait_end_of_query: 1,
	wait_for_async_insert: 1,
	enable_json_type: 1
	// exact_rows_before_limit: 1
};

export function dbClickhouseConnection(
	connectionData: Partial<CredentialsType>,
	clickhouse_settings: ClickHouseSettings
) {
	const { host, port, user, password, ssl, database } = connectionData;

	return createClient({
		clickhouse_settings,
		database: database || 'default',
		password: password,
		url: ssl ? `http://${host}:${port}` : `http://${host}:${port}`,
		username: user,
		compression: {
			request: true,
			response: true
		},
		keep_alive: {
			enabled: false
		}
	});
}

export function dbClickhouseConnectionInterface(
	credentials: CredentialsType
): DatabaseConnectionQuery {
	return {
		query: async <TOutput>(
			sql: string,
			abortSignal: AbortSignal,
			logComment?: string
		) => {
			const clickhouse_settings = {
				...clickhouseSettings,
				log_comment: logComment || 'Query from Gaio'
			};

			const queryStatement = sql
				.replace(/^\s+|\s+$/g, '')
				.toLowerCase()
				.trim();

			const queryStartsWithSelect =
				queryStatement.toLowerCase().trimStart().startsWith('select') ||
				queryStatement.toLowerCase().trimStart().startsWith('with') ||
				queryStatement.toLowerCase().trimStart().startsWith('show') ||
				queryStatement.toLowerCase().trimStart().startsWith('describe') ||
				queryStatement.toLowerCase().trimStart().startsWith('explain');

			if (queryStartsWithSelect) {
				const dbQuery = await dbClickhouseConnection(
					credentials,
					clickhouse_settings
				).query({
					abort_signal: abortSignal,
					clickhouse_settings,
					format: 'JSON',
					query: sql
				});

				const results = await dbQuery.json<TOutput>();
				return {
					data: results.data,
					rows: results.rows,
					rows_before_limit_at_least: results.rows_before_limit_at_least,
					success: true,
					statistics: results.statistics,
					totals: results.totals,
					meta: results.meta || []
				};
			}
			const results = await dbClickhouseConnection(
				credentials,
				clickhouse_settings
			).exec({
				abort_signal: abortSignal,
				clickhouse_settings,
				query: sql
			});

			return {
				data: [{ status: 'success' }],
				meta: [
					{
						name: 'status',
						type: 'string'
					}
				],
				success: true,
				statistics: results.summary
			};
		},
		stream: async <TOutput>(
			sql: string,
			abortSignal?: AbortSignal,
			logComment?: string
		) => {
			const clickhouse_settings = {
				...clickhouseSettings,
				log_comment: logComment || 'Query from Gaio'
			};

			const dbQuery = await dbClickhouseConnection(
				credentials,
				clickhouse_settings
			).query({
				abort_signal: abortSignal,
				clickhouse_settings,
				format: 'JSONEachRow',
				query: sql
			});

			const streamReadable = dbQuery.stream<TOutput>();

			// Convert StreamReadable to ReadableStream
			return new ReadableStream<Row<TOutput, 'JSONEachRow'>[]>({
				async start(controller) {
					try {
						for await (const rows of streamReadable) {
							controller.enqueue(rows);
						}
						controller.close();
					} catch (error) {
						controller.error(error);
					}
				}
			});
		},
		insert: async (
			table: string,
			data: GenericType[],
			abortSignal: AbortSignal,
			logComment?: string
		) => {
			const clickhouse_settings = {
				...clickhouseSettings,
				log_comment: logComment || 'Query from Gaio'
			};

			const dbQuery = await dbClickhouseConnection(
				credentials,
				clickhouse_settings
			).insert({
				abort_signal: abortSignal,
				clickhouse_settings,
				format: 'JSONEachRow',
				table,
				values: data
			});

			return {
				queryId: dbQuery.query_id
			};
		}
	};
}

/**
 * @desc Provide query for repo, using default credentials
 * @param repoId
 * @param logComment
 */
export function repositoryInstance(repoId: string, logComment?: string) {
	const clickhouse_settings = {
		...clickhouseSettings,
		log_comment: logComment || 'Query from Gaio'
	};

	return {
		insert: async (tableName: string, data: unknown[]) => {
			const repoData = await RepoRepository.getRepoDataById(repoId);
			await dbClickhouseConnection(repoData, clickhouse_settings)
				.insert({
					format: 'JSONEachRow',
					table: tableName,
					values: data
				})
				.then(() => {
					return { command: 'success' };
				})
				.catch((err) => {
					// get only 200 characters of message
					const message = err.message
						? err.message?.substring(0, 200)
						: 'Error inserting data';
					throw new Error(message, {
						cause: 'errorInsertDataToRepo'
					});
				});
		},
		query: async (sql: string, format: DataFormat = 'JSON') => {
			const repoData = await RepoRepository.getRepoDataById(repoId);

			const query = sql
				.replace(/^\s+|\s+$/g, '')
				.toLowerCase()
				.trim();

			if (query.startsWith('select') || query.startsWith('with')) {
				const resultSet = await dbClickhouseConnection(
					repoData,
					clickhouse_settings
				).query({
					clickhouse_settings,
					format,
					query: sql
				});

				const result = await resultSet.text();

				if (result) {
					return JSON.parse(result);
				}
				return { command: 'success' };
			}
			await dbClickhouseConnection(repoData, clickhouse_settings).command({
				clickhouse_settings,
				query: sql
			});

			return { command: 'success' };
		}
	};
}
