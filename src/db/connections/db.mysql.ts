import type { CredentialsType } from '@gaio/shared/types';
import mysql from 'mysql2/promise';

export function dbMysqlConnection(connectionData: Partial<CredentialsType>) {
	const { database, host, password, port, user } = connectionData;

	return mysql.createPool({
		bigNumberStrings: false,
		database: database,
		dateStrings: true,
		host: host,
		multipleStatements: true,
		password: password,
		port: Number(port),
		supportBigNumbers: true,
		user: user,
		ssl: {
			rejectUnauthorized: false
		}
	});
}

// export const dbMysqlConnectionInterface = (credentials: CredentialsType) => {
// 	return {
// 		query: async <TOutput = unknown>(sql: string) => {
// 			try {
// 				const { user, password, host, port, database } = credentials
// 				const conn = `${user}:${encodeURIComponent(password)}@${host}:${Number(port)}`
//
// 				const result =
// 					await $`usql --json -q my://${conn}/${database} -w --command "${sql}" --set SHOW_HOST_INFORMATION=false`.json()
//
// 				return {
// 					data: result
// 				}
// 			} catch (err) {
// 				throw new Error(`Code: ${err.code}, Message: ${err.message}, Type: ${err.type}`)
// 			}
// 		}
// 	}
// }

export const dbMysqlConnectionInterface = (credentials: CredentialsType) => {
	return {
		query: async <TOutput = unknown>(sql: string) => {
			try {
				const dbQuery = await dbMysqlConnection(credentials).query(sql);

				const [results] = dbQuery;

				return { data: results as TOutput[] }; // TODO: Is this correct typing????????
			} catch (err) {
				let message = 'MySQL Error:\n';

				if (err.code) message += `Code: ${err.code}\n`;
				if (err.errno) message += `Errno: ${err.errno}\n`;
				if (err.sqlMessage) message += `Message: ${err.sqlMessage}\n`;
				if (err.sqlState) message += `SQL State: ${err.sqlState}\n`;
				if ('fatal' in err) message += `Fatal: ${err.fatal}\n`;
				if (err.sql) message += `SQL: ${err.sql}\n`;
				if (err.stack) message += `Stack:\n${err.stack}\n`;

				throw new Error(message);
			}
		}
	};
};
