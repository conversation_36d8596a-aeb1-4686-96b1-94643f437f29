import type { CredentialsType } from '@gaio/shared/types';
import oracledb from 'oracledb';

export function dbOracleConnection(connectionData: Partial<CredentialsType>) {
	let connectString = connectionData.connectString;

	// Ref: https://github.com/typeorm/typeorm/blob/71addb2ef49b5fba8f6ac8c08129c32dc52c5c74/src/driver/oracle/OracleDriver.ts#L1007

	if (!connectString) {
		let address = '(PROTOCOL=TCP)';

		if (connectionData.host) {
			address += `(HOST=${connectionData.host})`;
		}

		if (connectionData.port) {
			address += `(PORT=${connectionData.port})`;
		}

		let connectData = '(SERVER=DEDICATED)';

		if (connectionData.sid) {
			connectData += `(SID=${connectionData.sid})`;
		}

		if (connectionData.serviceName) {
			connectData += `(SERVICE_NAME=${connectionData.serviceName})`;
		}

		connectString = `(DESCRIPTION=(ADDRESS=${address})(CONNECT_DATA=${connectData}))`;
	}

	return oracledb.createPool({
		connectString,
		user: connectionData.user,
		password: connectionData.password,
		poolMax: 2,
		poolMin: 0,
		poolTimeout: 0
	});
}

export function dbOracleConnectionInterface(credentials: CredentialsType) {
	return {
		query: async <TOutput = unknown>(sql: string) => {
			try {
				const client = await dbOracleConnection(credentials);
				const conn = await client.getConnection();

				const results = await conn.execute<TOutput>(sql, [], {
					outFormat: oracledb.OUT_FORMAT_OBJECT
				});

				return {
					data: results.rows,
					meta: results.metaData
				};
			} catch (err) {
				throw new Error(
					`Code: ${err.code}, Message: ${err.message}, Type: ${err.type}`
				);
			}
		}
	};
}
