import {
	ExecuteStatementCommand,
	RedshiftDataClient
} from '@aws-sdk/client-redshift-data';
import type { CredentialsType } from '@gaio/shared/types';
import { dbPgConnection } from './db.pg';

export function dbRedshiftConnection(connectionData: Partial<CredentialsType>) {
	if (connectionData.accessKey && connectionData.secretKey) {
		return new RedshiftDataClient({
			credentials: {
				accessKeyId: connectionData.accessKey,
				secretAccessKey: connectionData.secretKey,
				accountId: connectionData.account
			}
		});
	}

	return dbPgConnection(connectionData);
}

export function dbRedshiftConnectionInterface(credentials: CredentialsType) {
	return {
		query: async <TOutput = unknown>(sql: string) => {
			try {
				const conn = dbRedshiftConnection(credentials);

				if (conn instanceof RedshiftDataClient) {
					const command = new ExecuteStatementCommand({
						Sql: sql,
						Database: credentials.database, // Confirm if correct
						DbUser: credentials.user // Confirm if correct
					});

					const results = await conn.send(command);

					return { data: results };
				}

				const results = await conn.query<TOutput>(sql);

				return { data: results };
			} catch (err) {
				throw new Error(
					`Code: ${err.code}, Message: ${err.message}, Type: ${err.type}`
				);
			}
		}
	};
}
