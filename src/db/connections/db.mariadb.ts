import type { CredentialsType } from '@gaio/shared/types';
import mariadb from 'mariadb';

export function dbMariadbConnection(connectionData: Partial<CredentialsType>) {
	const { host, port, user, password, database } = connectionData;

	return mariadb.createPool({
		host,
		port: Number(port),
		user,
		password,
		database,
		dateStrings: true,
		multipleStatements: true,
		supportBigNumbers: true, // @deprecated
		bigNumberStrings: false // @deprecated
	});
}

export function dbMariaConnectionInterface(credentials: CredentialsType) {
	return {
		query: async <TOutput = unknown>(sql: string) => {
			try {
				const results = await dbMariadbConnection(credentials).query(sql);
				return { data: results as TOutput };
			} catch (err) {
				throw new Error(
					`Code: ${err.code}, Message: ${err.message}, Type: ${err.type}`
				);
			}
		}
	};
}
