FROM ubuntu:24.04

LABEL maintainer="tuliomitico <https://github.com/tuliomitico>"

RUN apt-get update && \
	apt-get install -y  \
	sudo \
	build-essential \
	libssl-dev zlib1g-dev libbz2-dev libreadline-dev libsqlite3-dev \
	llvm libncurses5-dev libncursesw5-dev xz-utils tk-dev libxml2-dev \
	libxmlsec1-dev libffi-dev liblzma-dev \
	curl wget bash zip git default-jdk

RUN usermod -l bun -d /home/<USER>
RUN groupmod -n bun ubuntu		
    
SHELL [ "/bin/bash", "-c" ]

ENV PATH="/home/<USER>/.local/bin:/home/<USER>/.local/share/mise/shims:/home/<USER>/.pyenv/shims:/home/<USER>/.pyenv/bin:${PATH}"

ENV MISE_INSTALL_PATH=/home/<USER>/.local/bin/mise

WORKDIR /home/<USER>

ENV MISE_DATA_DIR=/home/<USER>/.local/share/mise
ENV MISE_CACHE_DIR=/home/<USER>/.cache/mise

ARG RUST_VERSION
ENV RUST_VERSION=${RUST_VERSION:-1.81.0}

ARG BUN_VERSION
ENV BUN_VERSION=${BUN_VERSION:-1.1.25}

USER bun
RUN curl https://mise.run | bash
RUN curl https://pyenv.run | bash

RUN  echo 'export PYENV_ROOT="$HOME/.pyenv"' >> /home/<USER>/.bashrc && \
     echo 'command -v pyenv >/dev/null || export PATH="$PYENV_ROOT/bin:$PATH"' >> /home/<USER>/.bashrc && \
     echo 'eval "$(pyenv init -)"' >> /home/<USER>/.bashrc

RUN source /home/<USER>/.bashrc && /home/<USER>/.pyenv/bin/pyenv install 3.10.15 && mise sync python --pyenv
RUN mise install usql --yes && mise install rust@${RUST_VERSION} --yes && mise install bun@${BUN_VERSION} --yes
RUN touch "/home/<USER>/.tool-versions" && echo 'python 3.10.15' >> /home/<USER>/.tool-versions \
										&& echo "rust $RUST_VERSION" >> /home/<USER>/.tool-versions \
										&& echo 'usql latest' >> /home/<USER>/.tool-versions \
                                        && echo "bun $BUN_VERSION" >> /home/<USER>/.tool-versions

RUN cargo install xsv scrubcsv

USER root
RUN ln -s "/home/<USER>/.local/share/mise/installs/rust/${RUST_VERSION}/xsv" /home/<USER>/.local/bin/
RUN ln -s "/home/<USER>/.local/share/mise/installs/rust/${RUST_VERSION}/scrubcsv" /home/<USER>/.local/bin/

USER bun
RUN wget https://github.com/aotimme/gocsv/releases/download/v1.0.4/gocsv-linux-amd64.zip
RUN wget https://github.com/johnkerl/miller/releases/download/v6.13.0/miller-6.13.0-linux-amd64.tar.gz

USER root
RUN unzip gocsv-linux-amd64.zip && mv gocsv-linux-amd64/gocsv /home/<USER>/.local/bin && rmdir gocsv-linux-amd64
RUN tar -xzf miller-6.13.0-linux-amd64.tar.gz && mv miller-6.13.0-linux-amd64/mlr /home/<USER>/.local/bin && rm -rf miller-6.13.0-linux-amd64

RUN curl https://clickhouse.com/ | sh
RUN ./clickhouse install

USER bun
RUN pip install --upgrade pip && \
    pip install --user pandas clickhouse_connect psutil

RUN pyenv virtualenv 3.10.15 env_task_pca && \
 pyenv virtualenv 3.10.15 env_task_cluster && \
 pyenv virtualenv 3.10.15 env_task_forecast && \
 pyenv virtualenv 3.10.15 env_task_basket

RUN eval "$(pyenv init -)" && eval "$(pyenv virtualenv-init -)" && pyenv activate env_task_pca && \
    pip install h2o pandas clickhouse_connect psutil && \
    pyenv deactivate

RUN eval "$(pyenv init -)" && eval "$(pyenv virtualenv-init -)" && pyenv activate env_task_cluster && \
    pip install h2o pandas clickhouse_connect psutil && \
    pyenv deactivate

RUN eval "$(pyenv init -)" && eval "$(pyenv virtualenv-init -)" && pyenv activate env_task_forecast && \
    pip install prophet==1.1.6 scikit-learn plotly==5.24.1 pandas clickhouse_connect psutil && \
    pyenv deactivate

RUN eval "$(pyenv init -)" && eval "$(pyenv virtualenv-init -)" && pyenv activate env_task_basket && \
    pip install mlxtend==0.22.0 pandas clickhouse_connect psutil && \
    pyenv deactivate

COPY --chown=bun:bun . api/

WORKDIR /home/<USER>/api

EXPOSE 3000

RUN bun i

CMD [ "bun","run","dev" ]