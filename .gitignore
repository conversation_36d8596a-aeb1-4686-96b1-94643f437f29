# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

/content

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

gaio

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

.idea

# vercel
.vercel

**/*.trace
**/*.zip
**/*.tar.gz
**/*.tgz
**/*.log
**/*.bun

# lock files
package-lock.json
yarn.lock
pnpm-lock.yaml

.vscode

dist

logs