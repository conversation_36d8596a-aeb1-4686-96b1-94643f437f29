{"name": "@gaio/api", "version": "0.0.877", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "bun run --watch --hot src/bootstrap.ts", "start": "NODE_ENV=PRODUCTION bun src/bootstrap.ts", "lint": "biome check . --apply", "build": "bun build src/bootstrap.ts --outdir ./dist --target bun", "compile": "bun build src/bootstrap.ts --compile --outfile ./gaio", "prereload-shared": "bun remove @gaio/shared", "reload-shared": "bun add github:gaiolabs/gaio-shared"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/azure": "^1.3.23", "@ai-sdk/deepseek": "^0.2.14", "@ai-sdk/google": "^1.2.19", "@ai-sdk/groq": "^1.2.9", "@ai-sdk/mistral": "^1.2.8", "@ai-sdk/openai": "^1.3.22", "@anthropic-ai/sdk": "^0.39.0", "@aws-sdk/client-redshift-data": "^3.721.0", "@aws-sdk/client-ses": "^3.670.0", "@clickhouse/client": "^1.11.2", "@clickhouse/client-web": "^1.11.2", "@databricks/sql": "^1.9.0", "@gaio/shared": "github:gaiolabs/gaio-shared", "@google/generative-ai": "^0.22.0", "@hono/sentry": "^1.2.1", "@hono/zod-openapi": "^0.19.2", "@hono/zod-validator": "^0.4.3", "@jsforce/jsforce-node": "^3.6.3", "@paralleldrive/cuid2": "^2.2.2", "@scalar/hono-api-reference": "^0.7.2", "@sendgrid/mail": "^8.1.5", "ai": "^4.3.16", "better-sqlite3": "^11.3.0", "bullmq": "^5.51.1", "dayjs": "^1.11.13", "directory-tree": "^3.5.2", "dot-object": "^2.1.5", "fast-csv": "^5.0.1", "fastest-validator": "^1.19.0", "fs-extra": "^11.2.0", "hono": "^4.7.8", "hono-openapi": "^0.4.6", "ioredis": "^5.4.1", "knex": "^3.1.0", "lint-staged": "^15.2.10", "lodash-es": "^4.17.21", "mariadb": "^3.4.0", "mssql": "^11.0.1", "mustache": "^4.2.0", "mysql": "^2.18.1", "mysql2": "^3.11.3", "nodemailer": "^6.9.15", "nunjucks": "^3.2.4", "ofetch": "^1.4.1", "ollama-ai-provider": "^1.2.0", "openai": "^4.90.0", "oracledb": "6.6.0", "pg": "^8.13.0", "pg-query-stream": "^4.7.0", "picocolors": "^1.1.1", "pino": "^9.6.0", "pino-pretty": "^13.0.0", "qs": "^6.14.0", "resend": "^4.7.0", "snowflake-sdk": "^2.0.1", "socket.io": "^4.8.0", "sqlite": "^5.1.1", "sqlite3": "^5.1.7", "tasktimer": "^3.0.0", "twilio": "^4.23.0", "ulidx": "^2.4.1", "unemail": "^0.3.0", "uuid": "^11.1.0", "zod": "^3.24.2"}, "devDependencies": {"@aws-sdk/types": "^3.664.0", "@biomejs/biome": "1.9.4", "@total-typescript/ts-reset": "^0.6.1", "@types/bun": "^1.1.11", "@types/fs-extra": "^11.0.4", "@types/lodash-es": "^4.17.12", "@types/mssql": "^9.1.5", "@types/mustache": "^4.2.5", "@types/nodemailer": "^6.4.16", "@types/nunjucks": "^3.2.6", "@types/oracledb": "^6.5.3", "@types/pg": "^8.11.10", "@types/qs": "^6.9.18", "@types/twilio": "^3.19.3", "@types/uuid": "^10.0.0", "bun-types": "latest", "globals": "^15.13.0", "prettier": "^3.4.2", "typescript": "^5.8.2"}, "module": "src/index.js"}