{"compilerOptions": {"target": "ES2022", "module": "ES2022", "moduleResolution": "node", "types": ["bun-types"], "esModuleInterop": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noImplicitAny": true, "strictNullChecks": false, "useUnknownInCatchVariables": false, "baseUrl": "./", "paths": {"@/types/*": ["./src/@types/*"], "@/components/*": ["./src/components/*"], "@/db/*": ["./src/db/*"], "@/entities/*": ["./src/db/entities/*"], "@/dtos/*": ["src/@types/http/dtos/*"], "@/use-cases/*": ["./src/use-cases/core/*"], "@/http/routes/*": ["./src/http/routes/*"], "@/tables/*": ["./src/db/tables/*"], "@/server/*": ["./src/server/*"], "@/utils/*": ["./src/utils/*"], "@/main/*": ["./src/main/*"], "@/infra/*": ["./src/infra/*"]}}, "include": ["./src/**/*.ts"], "exclude": ["node_modules"]}