# Gaio DataOS API

Sistema backend de todo o sistema operacional de dados da Gaio

## Principais Dependências

* Bun
* Hono
* ClickHouse
* Redis
* Typescript

## Ambientes -WIP

## Comandos

O projeto pode ser manipulado pelo seus scripts em conjuto com o comando make. Para descobrir quais comandos são possíveis no projeto execute:
```bash
$ make help
```
Caso não utilize o sistema de containerização será necessário ter instalado em sua máquina [Redis](https://redis.io/docs/latest/operate/oss_and_stack/install/install-redis/) e o cliente [Clickhouse](https://clickhouse.com/docs/en/install#quick-install) vide referências.

Então execute:
```bash
bun run dev
```

## Principais comandos

### Configuração

Cria envs baseado nos templates, realiza build da imagem docker e cria os bancos de dados para desenvolvimento e testes:
```bash
$ make setup
```
### Execução

Executar a aplicação:
```
$ make up
```

## Run
Executar qualquer comando usando um container:
```
$ make exec printenv
```

# Licença e Autoria
<p align="flex-start">
	<a href="https://gaio.io/"><img src="https://img.shields.io/badge/powered%20by-gaio-goldenrod.svg?style=for-the-badge&logo=data:image/svg%2bxml;base64,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"></a>
</p>