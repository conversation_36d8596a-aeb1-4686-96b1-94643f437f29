{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "ignore": []}, "formatter": {"enabled": true, "formatWithErrors": false, "attributePosition": "auto", "indentStyle": "tab", "indentWidth": 2, "lineWidth": 80}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"noParameterAssign": "off"}, "performance": {"noAccumulatingSpread": "off", "noDelete": "off"}, "complexity": {"noForEach": "off"}}}, "javascript": {"formatter": {"arrowParentheses": "always", "bracketSameLine": false, "bracketSpacing": true, "jsxQuoteStyle": "single", "quoteProperties": "asNeeded", "semicolons": "always", "quoteStyle": "single", "trailingCommas": "none"}}}