# Gaio API - High-Level Architecture Overview

## Core Architecture

The Gaio API is a modern backend service built with TypeScript using the Hono framework. It follows a component-based architecture where each functional area is organized into its own module with clear separation of concerns.

### Key Components

1. **Server Layer**
   - Entry point: `src/server/api.ts`
   - Uses Hono for HTTP routing and middleware
   - Implements WebSocket support for real-time communication
   - Provides OpenAPI documentation and API reference

2. **Component Structure**
   - Each functional area is organized as a component in `src/components/`
   - Components follow a consistent pattern:
     - `*.entity.ts`: Data models/entities
     - `*.repository.ts`: Data access layer
     - `*.routes.ts`: HTTP route definitions
     - `*.core.ts`: Core business logic
     - `use-cases/*.ts`: Specific business operations

3. **Database Layer**
   - Supports multiple database systems (ClickHouse, PostgreSQL, MySQL, etc.)
   - Uses a database abstraction layer for consistent query interface
   - Implements connection pooling and query building

4. **Authentication & Authorization**
   - JWT-based authentication
   - Role-based access control (user/dev roles)
   - API key management for external integrations

## Data Flow

1. HTTP requests are received by the Hono server
2. Requests are routed to appropriate component handlers
3. Middleware validates authentication and request data
4. Component handlers invoke use cases with validated data
5. Use cases interact with repositories to access data
6. Repositories execute database operations
7. Results flow back through the chain to the client

## Key Modules and Their Responsibilities

### App Management (`/app`)
- Application lifecycle management
- App configuration and parameters
- Form and option management

### Flow Management (`/flow`)
- Workflow definition and execution
- Task orchestration
- Data processing pipelines

### API Hook Management (`/api-hook`)
- External API integration
- API key management
- Webhook handling

### Data Source Management (`/source`)
- Connection to external data sources
- Data import/export
- Source configuration

### User Management (`/user`)
- User authentication
- Profile management
- Permission control

### Repository Management (`/repo`)
- Data repository configuration
- Storage management
- Version control integration

### Task Management (`/task`)
- Background job processing
- Task scheduling and execution
- Task logging and monitoring

## Integration Points

1. **Database Systems**
   - ClickHouse (primary data store)
   - PostgreSQL, MySQL, MariaDB
   - Oracle, SQL Server, Snowflake
   - SQLite for lightweight storage

2. **External Services**
   - Email services (SendGrid, AWS SES)
   - AI services (Anthropic, OpenAI, Google)
   - Cloud storage
   - Authentication providers

3. **Client Applications**
   - REST API endpoints
   - WebSocket real-time updates
   - Webhook callbacks

## Technology Stack

- **Runtime**: Bun.js
- **Framework**: Hono
- **Database**: Multiple (ClickHouse, PostgreSQL, MySQL, etc.)
- **Authentication**: JWT
- **Documentation**: OpenAPI, Scalar API Reference
- **Messaging**: WebSockets, Socket.io
- **Templating**: Mustache, Nunjucks
- **Validation**: Zod
- **Utilities**: Lodash, dayjs

## Development Guidelines

1. **Component Structure**
   - Keep related functionality together in components
   - Follow the established patterns for entity, repository, routes, and use cases
   - Maintain clear separation of concerns

2. **Error Handling**
   - Use HTTPException for HTTP-related errors
   - Implement proper error logging
   - Return consistent error responses

3. **Authentication**
   - Use the jwtGuard middleware for protected routes
   - Implement proper role-based access control
   - Validate user permissions in route handlers

4. **Database Operations**
   - Use the database abstraction layer
   - Implement proper transaction handling
   - Clean up temporary resources

5. **API Design**
   - Follow RESTful principles
   - Document endpoints with OpenAPI
   - Implement proper validation for request data
